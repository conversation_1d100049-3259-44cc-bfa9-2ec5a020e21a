{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Building AI Agents with Supabase and Strands 🔷 🧠"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Welcome to this tutorial on integrating Supabase with Strands AI agents! In this notebook, you'll learn how to leverage Supabase's powerful backend services through the Model Context Protocol (MCP) to build intelligent agents that can interact with your database, storage, and edge functions.\n", "\n", "## What is Supabase?\n", "\n", "[Supabase](https://supabase.com/) is an open-source Firebase alternative that provides all the backend services you need to build a product:\n", "\n", "- PostgreSQL database\n", "- Authentication\n", "- Instant APIs\n", "- Edge Functions\n", "- Realtime subscriptions\n", "- Storage\n", "\n", "## What is Supabase MCP?\n", "\n", "Supabase MCP (Model Context Protocol) is Supabase's implementation of a standardized interface that allows AI models to interact directly with Supabase services. It enables Strands AI agents to seamlessly access and manipulate your Supabase database, storage, authentication, and edge functions through natural language instructions. With Supabase MCP, you can build AI agents that can query your PostgreSQL database, manage files in storage, and trigger serverless functions without writing complex integration code for each specific operation.\n", "\n", "## What is <PERSON><PERSON>?\n", "\n", "The AWS Strands Agent Framework enables rapid development of AI agents with minimal code. Strands facilitates building highly dynamic agents through natural language, leveraging prompt engineering to generate varied output types and accept diverse natural language inputs seamlessly.\n", "\n", "By the end of this tutorial, you'll know how to:\n", "- Connect Strands agents to Supabase using MCP\n", "- Perform database operations with natural language\n", "- Manage edge functions through AI agents\n", "- Handle storage operations with AI assistance\n", "\n", "Let's get started!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting Started\n", "\n", "Follow these steps to set up:\n", "\n", "1. **Sign up** for Supabase at [supabase.com](https://supabase.com/) \n", "2. **Create** a new project. Name the project \"strandsai\".\n", "\n", "3. **Generate a personal access token** from your [Supabase account settings](https://supabase.com/dashboard/account/tokens).\n", "\n", "4. **Paste your token** into the cell below and execute the cell."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["# To export your personal access token into a .env file, run the following cell (PLEASE REPLACE WITH YOUR TOKEN):\n", "!echo \"SUPABASE_ACCESS_TOKEN=your-personal-access-token\" >> .env"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Install and import necessary dependencies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install strands-agents dotenv supabase --quiet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting Up Your Supabase MCP Client"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The code below will instantiate the Supabase MCP client with your personal access token."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import getpass\n", "from dotenv import load_dotenv\n", "from strands import Agent\n", "from strands.tools.mcp import MCPClient\n", "from mcp import StdioServerParameters, stdio_client\n", "\n", "# Load environment variables from .env file\n", "load_dotenv()\n", "\n", "# Prompt the user to securely input the personal access token if not already set in the environment\n", "if not os.environ.get(\"SUPABASE_ACCESS_TOKEN\"):\n", "    os.environ[\"SUPABASE_ACCESS_TOKEN\"] = getpass.getpass(\"SUPABASE_ACCESS_TOKEN:\\n\")\n", "\n", "# Initialize the Supabase MCP client\n", "supabase_client = MCPClient(\n", "    lambda: stdio_client(\n", "        StdioServerParameters(\n", "            command=\"npx\", \n", "            args=[\n", "                \"-y\",\n", "                \"@supabase/mcp-server-supabase@latest\",\n", "                \"--features=database,storage,functions,docs,debug,account\",\n", "                \"--access-token\", os.getenv(\"SUPABASE_ACCESS_TOKEN\")\n", "            ]\n", "        )\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Supabase MCP Architecture\n", "\n", "The Supabase MCP integration provides a set of tools that allow AI agents to interact with Supabase services. Here's how it works:\n", "\n", "![architecture2.png](assets/architecture2.png)\n", "\n", "The flow works as follows:\n", "\n", "1. The Strands agent receives a natural language request from the user\n", "2. The agent determines which Supabase service to use based on the request\n", "3. The agent calls the appropriate MCP tool with the necessary parameters\n", "4. The Supabase MCP server executes the request against the Supabase API\n", "5. The Supabase API interacts with the requested service (Database, Storage, or Edge Functions)\n", "6. The results are returned to the agent, which formats them for the user\n", "\n", "This architecture allows for seamless integration between AI agents and Supabase services, enabling natural language interactions with your database, storage, and edge functions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a Strands Agent with Supabase MCP Tools\n", "\n", "Now, let's create a Strands agent that can use the Supabase MCP tools. We'll use the AWS Bedrock Claude model for this example, but you can use any model supported by Strands."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from strands.models import BedrockModel\n", "\n", "# Initialize the Bedrock model\n", "bedrock_model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-5-sonnet-20241022-v2:0\",  # Claude 3.5 Sonnet v2\n", "    # Other model options:\n", "    # model_id=\"us.anthropic.claude-3-5-haiku-20241022-v1:0\",  # Claude 3.5 <PERSON><PERSON>\n", "    # model_id=\"us.anthropic.claude-sonnet-4-20250514-v1:0\",   # claude 4\n", "    region_name=\"us-west-2\",\n", ")\n", "\n", "# Create a system prompt for the agent\n", "system_prompt = \"\"\"\n", "You are a Supabase expert assistant that helps users manage their Supabase projects, databases, edge functions, and storage.\n", "You have access to Supabase MCP tools that allow you to interact with Supabase services.\n", "\n", "When helping users:\n", "1. Always explain what you're doing and why\n", "2. Provide code examples when relevant\n", "3. Suggest best practices for Supabase usage\n", "4. Be security-conscious and avoid exposing sensitive data\n", "\n", "You can help with:\n", "- Database operations (SQL queries, table management)\n", "- Edge function deployment and management\n", "- Storage operations (file upload/download, bucket management)\n", "- Project settings and configuration\n", "\"\"\"\n", "\n", "# Create the agent with Supabase MCP tools\n", "with supabase_client:\n", "    # Get the available tools from the Supabase MCP server\n", "    tools = supabase_client.list_tools_sync()\n", "    \n", "    # Create the agent with the tools and system prompt\n", "    agent = Agent(\n", "        model=bedrock_model,\n", "        system_prompt=system_prompt,\n", "        tools=tools\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploring Available Supabase MCP Tools\n", "\n", "Let's explore the tools available through the Supabase MCP server. This will help us understand what capabilities our agent has."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with supabase_client:\n", "    # The tools are already loaded in our agent, so let's ask it directly\n", "    response = agent(\"What Supabase operations can you help me with? List all the tools and capabilities you have access to.\")\n", "    print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Database Management Examples\n", "\n", "Let's start by exploring how our agent can help with database management tasks. We'll ask it to perform various operations on our Supabase database."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 1: List all tables in the database\n", "with supabase_client:\n", "    response = agent(\"List all tables in my Supabase strandai project and explain what each one is used for.\")\n", "    print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 2: Create a simple hello world table\n", "with supabase_client:\n", "    response = agent(\"\"\"\n", "    Create a new table called 'hello_world' with the following columns:\n", "    - id (integer, primary key)\n", "    - message (text, not null)\n", "    - created_at (timestamp with time zone, default to current timestamp)\n", "    \"\"\")\n", "    print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 3: Insert data into the table\n", "with supabase_client:\n", "    response = agent(\"\"\"\n", "    Insert the following messages into the 'hello_world' table:\n", "    1. Message: 'Hello, <PERSON>!'\n", "    2. Message: 'Welcome to Supabase!'\n", "    3. Message: 'AI agents are awesome!'\n", "    \"\"\")\n", "    print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 4: Query data from the table\n", "with supabase_client:\n", "    response = agent(\"Retrieve all messages from the 'hello_world' table and show them with their timestamps.\")\n", "    print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Edge Function Management Examples\n", "\n", "Now, let's explore how our agent can help with edge function management. Edge functions are serverless functions that run on Supabase's edge network."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 1: List all edge functions\n", "with supabase_client:\n", "    response = agent(\"List all edge functions in my Supabase project.\")\n", "    print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 2: Create a new edge function\n", "with supabase_client:\n", "    response = agent(\"\"\"\n", "    Create a new edge function called 'hello-world' that returns a JSON response with the following structure:\n", "    {\n", "        \"message\": \"Hello, <PERSON>!\",\n", "        \"timestamp\": <current timestamp>\n", "    }\n", "    JWT authorization is not needed in this function\n", "    \"\"\")\n", "    print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Storage Management Examples\n", "\n", "Finally, let's explore how our agent can help with storage management. Supabase Storage allows you to store and serve files like images, videos, and documents."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 1: List all storage buckets\n", "with supabase_client:\n", "    response = agent(\"List all Supabase storage buckets in my Supabase project strandsai.\")\n", "    print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building a Complete Application: RSS Content Curator\n", "\n", "Now that we've explored the individual capabilities of our Supabase-powered agent, let's build a complete RSS content curator system that automatically fetches news, stores it in a database, and sends daily email summaries.\n", "\n", "### Prerequisites\n", "\n", "Before we start, you'll need to:\n", "\n", "1. **Sign up for Resend**: Create a free account at [resend.com](https://resend.com/)\n", "2. **Get your Resend API token**: Go to your Resend dashboard and generate an API key\n", "3. **Add secrets to Supabase Edge Functions**: \n", "   - Go to your Supabase project dashboard\n", "   - Navigate to **Edge Functions** → **Secrets**\n", "   - Add the following secrets:\n", "     \n", "     **Secret 1 - Resend API Token:**\n", "     - Click **Add new secret**\n", "     - Set **Name**: `RESEND_API_TOKEN`\n", "     - Set **Value**: Your Resend API token (starts with `re_`)\n", "     - Click **Save**\n", "     \n", "     **Secret 2 - <PERSON><PERSON> Address:**\n", "     - Click **Add new secret**\n", "     - Set **Name**: `RECIPIENT_EMAIL`\n", "     - Set **Value**: Your email address (where you want to receive the daily summaries)\n", "     - Click **Save**\n", "\n", "These secrets will be securely available to your edge functions for sending emails."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Build the Complete RSS Curator System\n", "\n", "Let's have our agent build the entire RSS content curator system in one go! This will include:\n", "- Database tables for RSS sources and news articles\n", "- RSS fetcher edge function\n", "- Email summary edge function\n", "- Automated cron jobs for hourly RSS fetching and daily email summaries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with supabase_client:\n", "    response = agent(\"\"\"\n", "    Build me a complete RSS content curator system in the strandsai project, with the following requirements:\n", "    \n", "    1. Create database tables:\n", "       - rss_sources table (id, name, url, description, active, created_at)\n", "       - news_articles table (id, title, description, link, pub_date, source_id, created_at)\n", "    \n", "    2. Insert BBC News RSS feed (http://feeds.bbci.co.uk/news/rss.xml) as the first source\n", "    \n", "    3. Create 'fetch-rss-content' edge function that:\n", "       - Fetches RSS feeds from active sources\n", "       - Parses XML and extracts articles\n", "       - Only inserts new articles (avoid duplicates)\n", "    \n", "    4. Create 'send-daily-summary' edge function that:\n", "       - Gets articles from last 24 hours\n", "       - Sends HTML email via Resend API\n", "       - Use RESEND_API_TOKEN and RECIPIENT_EMAIL environment variables\n", "\n", "    5. Store project URL and Anon key in Supabase Vault\n", "\n", "    6. Set up cron jobs to call the edge functions:\n", "       - Hourly RSS fetch (every hour)\n", "       - Daily email summary (8:00 AM daily)\n", "    \n", "    Please build everything and show me the results!\n", "    \"\"\")\n", "    print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## What Happens Next?\n", "\n", "Congratulations! Your RSS content curator system is now fully operational. Here's what you can expect:\n", "\n", "### Automatic Operation\n", "- **Hourly RSS Fetching**: Every hour, your system will automatically fetch the latest news from BBC News RSS feed and store new articles in your database\n", "- **Daily Email Summaries**: Every morning at 8:00 AM, you'll receive a beautifully formatted HTML email with a summary of all news articles from the past 24 hours\n", "- **No Manual Intervention**: The system runs completely automatically using Supabase's cron jobs\n", "\n", "### Your Daily Email\n", "The email you receive will include:\n", "- A clean, organized summary of recent news articles\n", "- Article titles, descriptions, and links to read more\n", "- Grouped by RSS source (currently BBC News, but you can add more sources later)\n", "- Professional HTML formatting for easy reading\n", "\n", "You should start receiving your first daily summary email tomorrow morning at 8:00 AM!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optional: Inspect Your Supabase Dashboard\n", "\n", "Want to see what the agent built for you? Here's how to explore your new RSS curator system in the Supabase dashboard:\n", "\n", "### 1. Database Tables\n", "- Go to **Database** → **Tables**\n", "- You'll see two new tables:\n", "  - `rss_sources`: Contains your RSS feed sources (currently BBC News)\n", "  - `news_articles`: Will populate with news articles as they're fetched\n", "- Click on each table to see the data and structure\n", "\n", "### 2. Edge Functions\n", "- Go to **Edge Functions**\n", "- You'll see two new functions:\n", "  - `fetch-rss-content`: Fetches and parses RSS feeds\n", "  - `send-daily-summary`: Sends your daily email summaries\n", "- Click on each function to view the code and logs\n", "\n", "### 3. <PERSON><PERSON>\n", "- Go to **Database** → **Cron Jobs** (or check the SQL editor for cron job queries)\n", "- You'll see two scheduled jobs:\n", "  - `hourly-rss-fetch`: Runs every hour to fetch new articles\n", "  - `daily-email-summary`: Runs daily at 8:00 AM to send emails\n", "\n", "### 4. Edge Function Secrets\n", "- Go to **Edge Functions** → **Secrets**\n", "- Verify your secrets are properly configured:\n", "  - `RESEND_API_TOKEN`: Your Resend API key\n", "  - `RECIPIENT_EMAIL`: Your email address\n", "\n", "### 5. Monitor Activity\n", "- Check **Edge Functions** logs to see function executions\n", "- Monitor the `news_articles` table to see new articles being added\n", "- View function invocation history and any error logs\n", "\n", "This gives you full visibility into your automated RSS curator system!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this tutorial, we've explored how to integrate Supabase with Strands AI agents using the Model Context Protocol (MCP). We've seen how to:\n", "\n", "1. Set up a Supabase MCP client\n", "2. Create a Strands agent with Supabase tools\n", "3. Perform database operations with natural language\n", "4. Manage edge functions through AI agents\n", "5. Handle storage operations with AI assistance\n", "6. Build a complete RSS content curator application\n", "\n", "This integration opens up exciting possibilities for building AI-powered applications with Supabase as the backend. You can now create intelligent agents that can interact with your database, storage, and edge functions using natural language, making development faster and more intuitive.\n", "\n", "## Next Steps\n", "\n", "Here are some ideas for further exploration:\n", "\n", "1. Add more RSS sources to your curator system\n", "2. Build a chatbot that can query your Supabase database\n", "3. <PERSON>reate an AI assistant for database schema design\n", "4. Develop a content management system with AI-powered content generation\n", "5. Implement real-time features using Supabase's realtime subscriptions\n", "\n", "Happy building with Supabase and Strands! 🔷 🧠"]}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}