{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Zep Dining Assistant\n", "\n", "This notebook demonstrates a personal dining assistant agent built with Zep AI's graph-based memory and the Strands agent framework. The demo walks through a realistic user scenario, showcasing how the agent leverages semantic, episodic, and procedural memory to provide personalized dining recommendations and manage user preferences over time."]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 📦 Package Installation\n", "\n", "This section installs all required dependencies for the demo, including the Strands agent framework, Zep AI memory client, and libraries for data handling and visualization. These packages enable the agent's core logic, memory management, and graph visualization features."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 8ms\u001b[0m\u001b[0m\n", "\u001b[2mAudited \u001b[1m2 packages\u001b[0m \u001b[2min 11ms\u001b[0m\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 3ms\u001b[0m\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 5ms\u001b[0m\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 2ms\u001b[0m\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 4ms\u001b[0m\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 6ms\u001b[0m\u001b[0m\n", "\u001b[2K  \u001b[31m×\u001b[0m No solution found when resolving dependencies:                                  \u001b[0m\n", "\u001b[31m  ╰─▶ \u001b[0mBecause warnings was not found in the package registry and you require\n", "\u001b[31m      \u001b[0mwarnings, we can conclude that your requirements are unsatisfiable.\n"]}], "source": ["!uv pip install strands-agents\n", "!uv pip install strands-agents strands-agents-tools\n", "!uv pip install zep-cloud\n", "!uv pip install pandas\n", "!uv pip install IPython\n", "!uv pip install warnings"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## ⚙️ Environment Setup & Initialization\n", "\n", "Here, we set up the environment by loading API keys, initializing the Zep AI client, and creating a demo user and session. This ensures that all subsequent interactions are tracked and stored in a dedicated memory graph for the demo user."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Zep Dining Assistant Demo - Setup Complete\n", "============================================================\n", "✅ Zep connection verified\n", "👤 Demo User: demo_user_5155b519\n", "📝 Session: session_demo_user_5155b519_de8b418d\n"]}], "source": ["import os\n", "import json\n", "import uuid\n", "from typing import Dict, List, Any\n", "\n", "# Core libraries\n", "from dotenv import load_dotenv\n", "from zep_cloud.client import Zep\n", "from strands import Agent, tool\n", "import pandas as pd\n", "from IPython.display import display, Markdown, HTML\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "\n", "# Initialize clients\n", "zep_client = Zep(api_key=os.environ.get(\"ZEP_API_KEY\"))\n", "\n", "print(\"🚀 Zep Dining Assistant Demo - Setup Complete\")\n", "print(\"=\" * 60)\n", "\n", "# Create demo user and session for proper memory management\n", "demo_user_id = f\"demo_user_{uuid.uuid4().hex[:8]}\" # The user id used previously is: demo_user_5155b519\n", "session_id = f\"session_{demo_user_id}_{uuid.uuid4().hex[:8]}\"\n", "\n", "try:\n", "    # Create user in Zep\n", "    user_result = zep_client.user.add(\n", "        user_id=demo_user_id, \n", "        metadata={\"demo\": True, \"name\": \"Demo User\"}\n", "    )\n", "    \n", "    # Create session for this user\n", "    session_result = zep_client.memory.add_session(\n", "        session_id=session_id,\n", "        user_id=demo_user_id,\n", "        metadata={\"type\": \"dining_assistant_demo\"}\n", "    )\n", "    \n", "    print(f\"✅ Zep connection verified\")\n", "    print(f\"👤 Demo User: {demo_user_id}\")\n", "    print(f\"📝 Session: {session_id}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Zep setup failed: {e}\")\n", "    print(\"Please ensure ZEP_API_KEY is set in your .env file\")"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🔧 Mock External Services\n", "\n", "To provide a consistent and realistic demo experience, we define mock calendar and restaurant APIs. These services simulate real-world data, such as calendar conflicts and restaurant options, allowing the agent to reason about availability and make recommendations without relying on external APIs."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📅 Mock Calendar API initialized\n", "🍽️  Mock Restaurant API initialized\n", "   - 4 restaurants available\n"]}], "source": ["class MockCalendarAPI:\n", "    \"\"\"Mock calendar service for realistic demo scenarios\"\"\"\n", "\n", "    def __init__(self):\n", "        # Predefined conflicts for consistent demo experience\n", "        self.conflicts = {\n", "            \"2025-01-20 18:00\": {\n", "                \"event\": \"Team Meeting\",\n", "                \"duration\": 60,\n", "                \"flexibility\": \"low\",\n", "                \"alternative_times\": [\"2025-01-20 19:30\", \"2025-01-20 20:00\"],\n", "            },\n", "            \"6:00 PM\": {\n", "                \"event\": \"Team Meeting\",\n", "                \"duration\": 60,\n", "                \"flexibility\": \"low\",\n", "                \"alternative_times\": [\"7:30 PM\", \"8:00 PM\"],\n", "            },\n", "            \"18:00\": {\n", "                \"event\": \"Team Meeting\",\n", "                \"duration\": 60,\n", "                \"flexibility\": \"low\",\n", "                \"alternative_times\": [\"19:30\", \"20:00\"],\n", "            },\n", "        }\n", "\n", "    def check_availability(\n", "        self, datetime_str: str, duration: int = 90\n", "    ) -> Dict[str, Any]:\n", "        \"\"\"Check if the requested time is available\"\"\"\n", "        # Check for conflicts with different time formats\n", "        for conflict_time in self.conflicts:\n", "            if conflict_time in datetime_str or datetime_str in conflict_time:\n", "                conflict = self.conflicts[conflict_time]\n", "                return {\n", "                    \"available\": <PERSON><PERSON><PERSON>,\n", "                    \"conflict\": conflict,\n", "                    \"suggested_times\": conflict[\"alternative_times\"],\n", "                }\n", "        return {\"available\": True, \"conflict\": None}\n", "\n", "\n", "class MockRestaurantAPI:\n", "    \"\"\"Mock restaurant service with realistic data\"\"\"\n", "\n", "    def __init__(self):\n", "        self.restaurants = [\n", "            {\n", "                \"id\": \"resto_001\",\n", "                \"name\": \"Ocean's Bounty\",\n", "                \"cuisine\": \"seafood\",\n", "                \"price_range\": \"$$\",\n", "                \"rating\": 4.5,\n", "                \"location\": \"Downtown\",\n", "                \"distance\": 0.8,\n", "                \"accepts_reservations\": True,\n", "                \"specialties\": [\"fresh salmon\", \"lobster bisque\", \"grilled octopus\"],\n", "            },\n", "            {\n", "                \"id\": \"resto_002\",\n", "                \"name\": \"Green Garden Bistro\",\n", "                \"cuisine\": \"vegetarian\",\n", "                \"price_range\": \"$\",\n", "                \"rating\": 4.2,\n", "                \"location\": \"Midtown\",\n", "                \"distance\": 1.2,\n", "                \"accepts_reservations\": True,\n", "                \"specialties\": [\"quinoa bowls\", \"veggie burgers\", \"fresh salads\"],\n", "            },\n", "            {\n", "                \"id\": \"resto_003\",\n", "                \"name\": \"Bella Italia\",\n", "                \"cuisine\": \"italian\",\n", "                \"price_range\": \"$$\",\n", "                \"rating\": 4.3,\n", "                \"location\": \"Little Italy\",\n", "                \"distance\": 1.5,\n", "                \"accepts_reservations\": True,\n", "                \"specialties\": [\"handmade pasta\", \"wood-fired pizza\", \"osso buco\"],\n", "            },\n", "            {\n", "                \"id\": \"resto_004\",\n", "                \"name\": \"<PERSON> Sushi\",\n", "                \"cuisine\": \"japanese\",\n", "                \"price_range\": \"$$$\",\n", "                \"rating\": 4.7,\n", "                \"location\": \"Japantown\",\n", "                \"distance\": 2.1,\n", "                \"accepts_reservations\": True,\n", "                \"specialties\": [\"omakase\", \"fresh sashimi\", \"tempura\"],\n", "            },\n", "        ]\n", "\n", "    def search_restaurants(self, preferences: Dict[str, Any]) -> List[Dict[str, Any]]:\n", "        \"\"\"Search restaurants based on preferences\"\"\"\n", "        results = []\n", "\n", "        for restaurant in self.restaurants:\n", "            score = 0\n", "\n", "            # Cuisine match\n", "            if (\n", "                preferences.get(\"cuisine\")\n", "                and restaurant[\"cuisine\"] == preferences[\"cuisine\"]\n", "            ):\n", "                score += 3\n", "\n", "            # Price range match\n", "            if (\n", "                preferences.get(\"budget\")\n", "                and restaurant[\"price_range\"] == preferences[\"budget\"]\n", "            ):\n", "                score += 2\n", "\n", "            # Distance preference (closer is better)\n", "            distance_score = max(0, 3 - restaurant[\"distance\"])\n", "            score += distance_score\n", "\n", "            # Rating bonus\n", "            score += restaurant[\"rating\"] / 5 * 2\n", "\n", "            restaurant_result = restaurant.copy()\n", "            restaurant_result[\"match_score\"] = score\n", "            results.append(restaurant_result)\n", "\n", "        # Sort by match score\n", "        results.sort(key=lambda x: x[\"match_score\"], reverse=True)\n", "        return results[:3]  # Return top 3 matches\n", "\n", "    def book_restaurant(\n", "        self, restaurant_id: str, datetime_str: str, party_size: int\n", "    ) -> Dict[str, Any]:\n", "        \"\"\"Mock restaurant booking\"\"\"\n", "        import random\n", "\n", "        # 85% success rate for realism\n", "        if random.random() < 0.85:\n", "            confirmation_id = f\"CONF_{uuid.uuid4().hex[:8].upper()}\"\n", "            return {\n", "                \"success\": True,\n", "                \"confirmation_id\": confirmation_id,\n", "                \"message\": f\"Reservation confirmed for {party_size} people\",\n", "            }\n", "        else:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"message\": \"Sorry, that time slot is no longer available\",\n", "            }\n", "\n", "\n", "# Initialize mock services\n", "calendar_api = MockCalendarAPI()\n", "restaurant_api = MockRestaurantAPI()\n", "\n", "print(\"📅 Mock Calendar API initialized\")\n", "print(\"🍽️  Mock Restaurant API initialized\")\n", "print(f\"   - {len(restaurant_api.restaurants)} restaurants available\")"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🛠️ Strands Agent Tools Configuration\n", "\n", "This section defines the agent's tools using the Strands framework. Each tool encapsulates a specific capability, such as checking calendar availability, searching for restaurants, booking reservations, and interacting with the Zep memory system. These tools are orchestrated by the agent to fulfill user requests and manage memory."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 Strands agent tools configured:\n", "   ✅ Calendar check tool\n", "   ✅ Restaurant search tool\n", "   ✅ Restaurant booking tool\n", "   ✅ Memory storage tool\n", "   ✅ Memory retrieval tool\n", "   ✅ Fact search tool\n"]}], "source": ["# Define tools using @tool decorator\n", "@tool\n", "def calendar_check(datetime: str, duration: int = 90) -> str:\n", "    \"\"\"Check calendar availability for a proposed dining time\"\"\"\n", "    result = calendar_api.check_availability(datetime, duration)\n", "    return json.dumps(result, indent=2)\n", "\n", "@tool\n", "def restaurant_search(preferences: Dict[str, Any]) -> str:\n", "    \"\"\"Search for restaurants based on user preferences\"\"\"\n", "    results = restaurant_api.search_restaurants(preferences)\n", "    return json.dumps(results, indent=2)\n", "\n", "@tool\n", "def restaurant_booking(restaurant_id: str, datetime: str, party_size: int) -> str:\n", "    \"\"\"Book a restaurant reservation\"\"\"\n", "    result = restaurant_api.book_restaurant(restaurant_id, datetime, party_size)\n", "    return json.dumps(result, indent=2)\n", "\n", "@tool\n", "def store_dining_memory(content: str) -> str:\n", "    \"\"\"Store dining-related information in user's memory\"\"\"\n", "    try:\n", "        # Add message to session to trigger memory storage\n", "        zep_client.memory.add(\n", "            session_id=session_id,\n", "            messages=[{\n", "                \"role\": \"assistant\",\n", "                \"content\": f\"Storing user preference: {content}\"\n", "            }]\n", "        )\n", "        \n", "        return f\"Successfully stored: {content}\"\n", "    except Exception as e:\n", "        return f\"Failed to store memory: {str(e)}\"\n", "\n", "@tool\n", "def retrieve_dining_memories(query: str) -> str:\n", "    \"\"\"Retrieve relevant dining memories and preferences\"\"\"\n", "    try:\n", "        # Get memory context for the session\n", "        memory = zep_client.memory.get(session_id=session_id)\n", "        \n", "        if memory and memory.context:\n", "            return f\"Retrieved memories: {memory.context}\"\n", "        else:\n", "            return \"No relevant memories found for this query.\"\n", "            \n", "    except Exception as e:\n", "        return f\"Failed to retrieve memories: {str(e)}\"\n", "\n", "@tool\n", "def search_dining_facts(query: str) -> str:\n", "    \"\"\"Search for specific facts in user's dining history\"\"\"\n", "    try:\n", "        # Search for facts in the user's graph\n", "        search_results = zep_client.graph.search(\n", "            user_id=demo_user_id, \n", "            query=query, \n", "            limit=5\n", "        )\n", "        \n", "        if search_results and hasattr(search_results, 'edges'):\n", "            facts = [edge.fact for edge in search_results.edges if hasattr(edge, 'fact') and edge.fact]\n", "            if facts:\n", "                return f\"Found {len(facts)} relevant facts: {json.dumps(facts, indent=2)}\"\n", "        \n", "        return f\"No specific facts found for query: {query}\"\n", "        \n", "    except Exception as e:\n", "        return f\"Fact search failed: {str(e)}\"\n", "\n", "print(\"🔧 Strands agent tools configured:\")\n", "print(\"   ✅ Calendar check tool\")\n", "print(\"   ✅ Restaurant search tool\") \n", "print(\"   ✅ Restaurant booking tool\")\n", "print(\"   ✅ Memory storage tool\")\n", "print(\"   ✅ Memory retrieval tool\")\n", "print(\"   ✅ Fact search tool\")"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🤖 Dining Assistant Agent Initialization\n", "\n", "Here, we initialize the Strands agent with a system prompt and the previously defined tools. The prompt guides the agent's behavior, ensuring it uses memory and external tools in the correct order to provide a personalized and context-aware dining experience."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 Strands Dining Assistant Agent Initialized\n", "👤 Demo User ID: demo_user_5155b519\n", "📝 Session ID: session_demo_user_5155b519_de8b418d\n", "🧠 Zep Memory System: Connected\n", "============================================================\n"]}], "source": ["DINING_ASSISTANT_PROMPT = f\"\"\"\n", "You are a sophisticated personal dining assistant powered by Zep AI's memory system and the Strands framework.\n", "\n", "Your core capabilities:\n", "- Store and retrieve user dining preferences using memory tools\n", "- Handle both permanent and temporary dining preferences  \n", "- Manage scheduling conflicts intelligently\n", "- Make personalized restaurant recommendations\n", "- Learn from user feedback to improve future suggestions\n", "\n", "Memory Management Guidelines:\n", "1. ALWAYS retrieve memories first using retrieve_dining_memories() before asking questions\n", "2. Store new preferences immediately using store_dining_memory() when you learn them\n", "3. Use search_dining_facts() for specific factual queries about past experiences\n", "4. Distinguish between permanent preferences (vegetarian) and temporary desires (seafood tonight)\n", "5. ALWAYS check calendar using calendar_check() when a specific time is mentioned\n", "\n", "Current session: {session_id}\n", "User: {demo_user_id}\n", "\n", "Available tools:\n", "- retrieve_dining_memories: Get user's dining history and preferences \n", "- store_dining_memory: Save new preferences and experiences\n", "- search_dining_facts: Search for specific facts in dining history\n", "- calendar_check: Check for scheduling conflicts (ALWAYS use when time mentioned)\n", "- restaurant_search: Find restaurants matching preferences\n", "- restaurant_booking: Make restaurant reservations\n", "\n", "Remember: Use memory tools to provide personalized, contextual assistance based on accumulated knowledge.\n", "\n", "IMPORTANT: When user mentions a time, ALWAYS check calendar_check() first, then retrieve memories, then proceed.\n", "\"\"\"\n", "\n", "# Initialize Strands Agent\n", "dining_agent = Agent(\n", "    system_prompt=DINING_ASSISTANT_PROMPT,\n", "    tools=[\n", "        calendar_check,\n", "        restaurant_search, \n", "        restaurant_booking,\n", "        store_dining_memory,\n", "        retrieve_dining_memories,\n", "        search_dining_facts,\n", "    ],\n", ")\n", "\n", "print(\"🤖 Strands Dining Assistant Agent Initialized\")\n", "print(f\"👤 Demo User ID: {demo_user_id}\")\n", "print(f\"📝 Session ID: {session_id}\")\n", "print(\"🧠 Zep Memory System: Connected\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🍽️ Demo Scenario 1: Initial Preference Discovery\n", "\n", "This scenario simulates a user's first interaction with the dining assistant. The agent checks for calendar conflicts, retrieves any existing preferences, and prompts the user for additional information to tailor its recommendations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/markdown": ["*User requests dinner for the first time - agent discovers preferences*"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["👤 User: Hi! I'd like to have dinner tonight around 6 PM. Can you help me find a good restaurant?\n", "\n", "🤖 Assistant Response:\n", "----------------------------------------\n", "I'd be happy to help you find a restaurant for dinner tonight at 6 PM. First, let me check your calendar to make sure you're available, and then I'll look up your dining preferences to give you personalized recommendations.\n", "Tool #1: calendar_check\n", "I see there's a scheduling conflict at 6 PM - you have a Team Meeting that will last for 60 minutes. Let me check if you have any specific dining preferences before suggesting alternative times.\n", "Tool #2: retrieve_dining_memories\n", "I see you have a Team Meeting at 6 PM tonight, so that time won't work for dinner. Your calendar suggests 7:30 PM or 8:00 PM would be available instead.\n", "\n", "Since I don't have any information about your dining preferences yet, could you share:\n", "- What type of cuisine you're interested in tonight?\n", "- Any dietary restrictions I should be aware of?\n", "- How many people will be joining for dinner?\n", "\n", "Once you provide this information, I can help find the perfect restaurant for your dinner tonight at one of the available times.I see you have a Team Meeting at 6 PM tonight, so that time won't work for dinner. Your calendar suggests 7:30 PM or 8:00 PM would be available instead.\n", "\n", "Since I don't have any information about your dining preferences yet, could you share:\n", "- What type of cuisine you're interested in tonight?\n", "- Any dietary restrictions I should be aware of?\n", "- How many people will be joining for dinner?\n", "\n", "Once you provide this information, I can help find the perfect restaurant for your dinner tonight at one of the available times.\n", "\n", "\n", "============================================================\n"]}], "source": ["display(\n", "    Markdown(\"*User requests dinner for the first time - agent discovers preferences*\")\n", ")\n", "\n", "# Simulate user's first request\n", "user_message_1 = \"Hi! I'd like to have dinner tonight around 6 PM. Can you help me find a good restaurant?\"\n", "\n", "print(\"👤 User:\", user_message_1)\n", "print(\"\\n🤖 Assistant Response:\")\n", "print(\"-\" * 40)\n", "\n", "# Get agent response\n", "response_1 = dining_agent(user_message_1)\n", "print(response_1)\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 Memory State Analysis\n", "\n", "This section inspects the current state of the user's memory after initial interactions. It displays stored context, messages, and graph edges, providing insight into how the agent accumulates and organizes knowledge over time."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def check_memory_state():\n", "    \"\"\"Check current memory state for debugging\"\"\"\n", "    try:\n", "        # Get current session memory\n", "        memory = zep_client.memory.get(session_id=session_id)\n", "        \n", "        print(f\"📊 Session Memory Status:\")\n", "        if memory:\n", "            print(f\"   ✅ Memory object exists\")\n", "            if hasattr(memory, 'context') and memory.context:\n", "                print(f\"   ✅ Context available: {len(memory.context)} characters\")\n", "                print(f\"   📝 Context preview: {memory.context[:200]}...\")\n", "            else:\n", "                print(f\"   ⚠️ No context available yet\")\n", "                \n", "            if hasattr(memory, 'messages') and memory.messages:\n", "                print(f\"   💬 Messages: {len(memory.messages)} stored\")\n", "            else:\n", "                print(f\"   💬 No messages stored yet\")\n", "        else:\n", "            print(f\"   ❌ No memory found for session\")\n", "            \n", "        # Try to get user's graph data\n", "        try:\n", "            search_results = zep_client.graph.search(\n", "                user_id=demo_user_id,\n", "                query=\"dining preferences\",\n", "                limit=5\n", "            )\n", "            if search_results and hasattr(search_results, 'edges'):\n", "                print(f\"   🔗 Graph edges found: {len(search_results.edges)}\")\n", "            else:\n", "                print(f\"   🔗 No graph edges found yet\")\n", "        except Exception as e:\n", "            print(f\"   🔗 Graph search not ready: {str(e)}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Memory check failed: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Session Memory Status:\n", "   ✅ Memory object exists\n", "   ✅ Context available: 2234 characters\n", "   📝 Context preview: \n", "FACTS and ENTITIES represent relevant context to the current conversation.\n", "\n", "# These are the most relevant facts and their valid date ranges\n", "# format: FACT (Date range: from - to)\n", "<FACTS>\n", "  - On July ...\n", "   💬 Messages: 3 stored\n", "   🔗 Graph edges found: 4\n"]}], "source": ["# Check memory after each interaction\n", "check_memory_state()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📅 Demo Scenario 2: Calendar Conflict & Resolution\n", "\n", "The agent detects a scheduling conflict for the requested dinner time and negotiates an alternative. It also stores the user's dietary and budget preferences, demonstrating how procedural and episodic memory are updated in response to user input."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/markdown": ["*Agent discovers calendar conflict and negotiates alternative time*"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["👤 User: I'm vegetarian and prefer places that aren't too expensive. Actually, can we make it exactly at 6:00 PM? That would be perfect.\n", "\n", "🤖 Assistant Response:\n", "----------------------------------------\n", "Thank you for sharing your dietary preference as vegetarian and that you prefer places that aren't too expensive. Let me check again about the 6:00 PM timeframe since you specifically requested it.\n", "Tool #3: calendar_check\n", "I need to let you know that you still have a Team Meeting at 6:00 PM that appears to be inflexible. According to your calendar, the earliest available times after your meeting would be 7:30 PM or 8:00 PM.\n", "\n", "Let me store your dining preferences in my memory so I can provide better recommendations in the future.\n", "Tool #4: store_dining_memory\n", "Unfortunately, 6:00 PM won't work for dinner tonight due to your Team Meeting. Would you prefer a 7:30 PM reservation instead? Also, may I ask how many people would be joining for dinner? Once you confirm a time that works with your schedule and the party size, I'll search for vegetarian-friendly restaurants with reasonable prices.Unfortunately, 6:00 PM won't work for dinner tonight due to your Team Meeting. Would you prefer a 7:30 PM reservation instead? Also, may I ask how many people would be joining for dinner? Once you confirm a time that works with your schedule and the party size, I'll search for vegetarian-friendly restaurants with reasonable prices.\n", "\n", "\n", "============================================================\n", "\n", "🧠 Checking Memory Storage:\n", "⚠️ No memories found yet - may take a moment to process\n"]}], "source": ["display(Markdown(\"*Agent discovers calendar conflict and negotiates alternative time*\"))\n", "\n", "# Follow up with more specific preferences and a time that has a conflict\n", "user_message_2 = \"I'm vegetarian and prefer places that aren't too expensive. Actually, can we make it exactly at 6:00 PM? That would be perfect.\"\n", "\n", "print(\"👤 User:\", user_message_2)\n", "print(\"\\n🤖 Assistant Response:\")\n", "print(\"-\" * 40)\n", "\n", "response_2 = dining_agent(user_message_2)\n", "print(response_2)\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "\n", "# Check if memory was saved properly\n", "print(\"\\n🧠 Checking Memory Storage:\")\n", "try:\n", "    # Search for newly stored preferences\n", "    memory_check = zep_client.graph.search(\n", "        user_id=demo_user_id, query=\"vegetarian preferences\", limit=5\n", "    )\n", "\n", "    if memory_check and hasattr(memory_check, \"edges\") and memory_check.edges:\n", "        print(f\"✅ Found {len(memory_check.edges)} stored memories\")\n", "        for edge in memory_check.edges:\n", "            if hasattr(edge, \"fact\") and edge.fact:\n", "                print(f\"   • {edge.fact}\")\n", "    else:\n", "        print(\"⚠️ No memories found yet - may take a moment to process\")\n", "\n", "except Exception as e:\n", "    print(f\"Memory check error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Demo Scenario 3: Preference Evolution\n", "\n", "The user temporarily changes their preference from vegetarian to seafood. The agent distinguishes between permanent and temporary preferences, updating the memory graph accordingly to reflect this context-specific change."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/markdown": ["*User changes preference temporarily - showcasing temporal vs permanent preferences*"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["👤 User: You know what, I'm actually really craving seafood tonight instead. I know I'm usually vegetarian, but I'm in the mood for something different tonight.\n", "\n", "🤖 Assistant Response:\n", "----------------------------------------\n", "I understand you're craving seafood tonight even though you typically follow a vegetarian diet. That's perfectly fine - let me update my understanding of your preferences for tonight.\n", "Tool #5: store_dining_memory\n", "Now, let me search for seafood restaurants that aren't too expensive. But first, I need to confirm a time that works with your schedule and how many people will be dining.\n", "\n", "Based on your calendar availability, would 7:30 PM work for your seafood dinner tonight? And how many people should I include in the reservation?Now, let me search for seafood restaurants that aren't too expensive. But first, I need to confirm a time that works with your schedule and how many people will be dining.\n", "\n", "Based on your calendar availability, would 7:30 PM work for your seafood dinner tonight? And how many people should I include in the reservation?\n", "\n", "\n", "============================================================\n"]}], "source": ["display(\n", "    Markdown(\n", "        \"*User changes preference temporarily - showcasing temporal vs permanent preferences*\"\n", "    )\n", ")\n", "\n", "user_message_3 = \"You know what, I'm actually really craving seafood tonight instead. I know I'm usually vegetarian, but I'm in the mood for something different tonight.\"\n", "\n", "print(\"👤 User:\", user_message_3)\n", "print(\"\\n🤖 Assistant Response:\")\n", "print(\"-\" * 40)\n", "\n", "response_3 = dining_agent(user_message_3)\n", "print(response_3)\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 De<PERSON> 4: Restaurant Search & Booking\n", "\n", "The agent uses the accumulated preferences and resolved schedule to search for suitable restaurants and book a reservation. This demonstrates the full workflow from preference discovery to action, with all steps informed by the memory graph."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/markdown": ["*Agent finds and books restaurant based on memory and current preferences*"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["👤 User: Great! Let's go with Ocean's Bounty for 7:30 PM. Can you book it for 2 people?\n", "\n", "🤖 Assistant Response:\n", "----------------------------------------\n", "I'll help you book a reservation at Ocean's Bounty for 7:30 PM for 2 people. Let me first confirm this time is still available in your calendar, and then I'll make the booking.\n", "Tool #6: calendar_check\n", "Great! 7:30 PM is available in your calendar. Now I'll book your reservation at Ocean's Bounty for 2 people.\n", "Tool #7: restaurant_booking\n", "\n", "Tool #8: store_dining_memory\n", "Your reservation has been successfully booked! \n", "\n", "Here are the details:\n", "- Restaurant: Ocean's Bounty\n", "- Date and time: Tonight (July 6, 2023) at 7:30 PM\n", "- Party size: 2 people\n", "- Confirmation ID: CONF_F39A36A8\n", "\n", "Your reservation is all set. I hope you enjoy your seafood dinner tonight! Is there anything else you need help with regarding your reservation or any other dining plans?Your reservation has been successfully booked! \n", "\n", "Here are the details:\n", "- Restaurant: Ocean's Bounty\n", "- Date and time: Tonight (July 6, 2023) at 7:30 PM\n", "- Party size: 2 people\n", "- Confirmation ID: CONF_F39A36A8\n", "\n", "Your reservation is all set. I hope you enjoy your seafood dinner tonight! Is there anything else you need help with regarding your reservation or any other dining plans?\n", "\n", "\n", "============================================================\n"]}], "source": ["display(\n", "    Markdown(\n", "        \"*Agent finds and books restaurant based on memory and current preferences*\"\n", "    )\n", ")\n", "\n", "user_message_4 = (\n", "    \"Great! Let's go with Ocean's Bounty for 7:30 PM. Can you book it for 2 people?\"\n", ")\n", "\n", "print(\"👤 User:\", user_message_4)\n", "print(\"\\n🤖 Assistant Response:\")\n", "print(\"-\" * 40)\n", "\n", "response_4 = dining_agent(user_message_4)\n", "print(response_4)\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 Complete Memory Graph Analysis\n", "\n", "This section analyzes the complete memory graph built during the conversation. It summarizes permanent and temporary preferences, past experiences, and the structure of the knowledge graph, highlighting the agent's ability to learn and adapt."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/markdown": ["*Examining the full knowledge graph built through our conversation*"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📊 Total Memory Entries: 4\n", "\n", "🔍 Discovered Knowledge:\n", "\n", "🎯 Permanent Preferences (2):\n", "   • User prefers restaurants that aren't too expensive\n", "   • User is vegetarian\n", "\n", "⏰ Temporary Preferences (1):\n", "   • On July 6, 2023, user specifically mentioned craving seafood as a temporary preference.\n", "\n", "📝 Experiences (1):\n", "   • On July 6, 2023, user made a reservation at Ocean's Bounty seafood restaurant for 7:30 PM for 2 people.\n", "\n", "🔗 Graph Structure: 6 nodes\n", "   Node Details:\n", "   • Node 1: vegetarian preference (['Entity', 'Preference'])\n", "   • Node 2: assistant (norole) (['Entity', 'Preference'])\n", "   • Node 3: user (['Entity', 'User'])\n", "   • Node 4: seafood preference (['Entity', 'Preference'])\n", "   • Node 5: Ocean's Bounty seafood restaurant (['Entity'])\n", "   • Node 6: assistant (['Enti<PERSON>'])\n", "\n", "🔍 Additional Memory Searches:\n", "   • Dining-related memories: 4\n", "   • Booking experiences: 4\n", "\n", "💡 Memory Graph Insights:\n", "   • Total facts stored: 4\n", "   • Graph nodes created: 6\n", "   • Preference hierarchy maintained: Permanent vs Temporary\n", "   • Experience tracking: Restaurant bookings with context\n", "   • Temporal awareness: Date-specific preferences captured\n"]}], "source": ["display(Markdown(\"*Examining the full knowledge graph built through our conversation*\"))\n", "\n", "try:\n", "    # Get all edges (facts) for this user\n", "    all_edges = zep_client.graph.edge.get_by_user_id(user_id=demo_user_id)\n", "    \n", "    print(f\"📊 Total Memory Entries: {len(all_edges)}\")\n", "    print(\"\\n🔍 Discovered Knowledge:\")\n", "\n", "    permanent_prefs = []\n", "    temporary_prefs = []\n", "    experiences = []\n", "\n", "    for edge in all_edges:\n", "        fact = edge.fact.lower()\n", "        if \"vegetarian\" in fact and \"usually\" in fact:\n", "            permanent_prefs.append(edge.fact)\n", "        elif \"seafood\" in fact and (\"tonight\" in fact or \"craving\" in fact):\n", "            temporary_prefs.append(edge.fact)\n", "        elif \"booked\" in fact or \"reservation\" in fact:\n", "            experiences.append(edge.fact)\n", "        else:\n", "            permanent_prefs.append(edge.fact)\n", "\n", "    print(f\"\\n🎯 Permanent Preferences ({len(permanent_prefs)}):\")\n", "    for pref in permanent_prefs:\n", "        print(f\"   • {pref}\")\n", "\n", "    print(f\"\\n⏰ Temporary Preferences ({len(temporary_prefs)}):\")\n", "    for pref in temporary_prefs:\n", "        print(f\"   • {pref}\")\n", "\n", "    print(f\"\\n📝 Experiences ({len(experiences)}):\")\n", "    for exp in experiences:\n", "        print(f\"   • {exp}\")\n", "\n", "    # Get graph structure using nodes\n", "    try:\n", "        user_nodes = zep_client.graph.node.get_by_user_id(demo_user_id, limit=30)\n", "        print(f\"\\n🔗 Graph Structure: {len(user_nodes)} nodes\")\n", "\n", "        # Display node information\n", "        print(\"   Node Details:\")\n", "        for i, node in enumerate(user_nodes[:10]):  # Show first 10 nodes\n", "            node_name = getattr(node, 'name', 'Unknown')\n", "            node_label = getattr(node, 'labels', ['Unknown'])\n", "            print(f\"   • Node {i+1}: {node_name} ({node_label})\")\n", "            \n", "        if len(user_nodes) > 10:\n", "            print(f\"   • ... and {len(user_nodes) - 10} more nodes\")\n", "\n", "    except Exception as e:\n", "        print(f\"Graph structure analysis: {e}\")\n", "        \n", "    # Also try targeted searches for specific memory types\n", "    print(f\"\\n🔍 Additional Memory Searches:\")\n", "    \n", "    try:\n", "        # Search for dining preferences\n", "        dining_memories = zep_client.graph.search(\n", "            user_id=demo_user_id,\n", "            query=\"dining preferences vegetarian seafood restaurant\",\n", "            limit=10\n", "        )\n", "        print(f\"   • Dining-related memories: {len(dining_memories.edges)}\")\n", "        \n", "        # Search for booking experiences  \n", "        booking_memories = zep_client.graph.search(\n", "            user_id=demo_user_id,\n", "            query=\"booking reservation restaurant confirmed\",\n", "            limit=10\n", "        )\n", "        print(f\"   • Booking experiences: {len(booking_memories.edges)}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"   • Targeted search error: {e}\")\n", "\n", "    # Memory Graph Insights\n", "    print(f\"\\n💡 Memory Graph Insights:\")\n", "    print(f\"   • Total facts stored: {len(all_edges)}\")\n", "    print(f\"   • Graph nodes created: {len(user_nodes) if 'user_nodes' in locals() else 'N/A'}\")\n", "    print(f\"   • Preference hierarchy maintained: Permanent vs Temporary\")\n", "    print(f\"   • Experience tracking: Restaurant bookings with context\")\n", "    print(f\"   • Temporal awareness: Date-specific preferences captured\")\n", "\n", "except Exception as e:\n", "    print(f\"Memory analysis error: {e}\")"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 📊 Memory Graph Visualization\n", "\n", "A visual representation of the Zep AI knowledge graph, showing how user preferences, experiences, and relationships are interconnected. This helps illustrate the advantages of graph-based memory over traditional flat storage."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Zep Memory Graph\n", "This is the User Relationship Graph, a visualization of user relationships and connections from Zep\n", "![zep memory graph](./img/zep-memory-graph.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Future Interaction Simulation\n", "\n", "This scenario simulates a follow-up conversation days later. The agent leverages accumulated memory to provide recommendations that reflect both long-term and recent user preferences, demonstrating memory persistence and learning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/markdown": ["*Simulating a conversation days later to show memory persistence*"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["⏰ Simulating conversation 3 days later...\n", "============================================================\n", "👤 User: Hi! I'm looking for a dinner recommendation for this Friday. What do you suggest based on what you know about me?\n", "\n", "🤖 Assistant Response:\n", "----------------------------------------\n", "I'd be happy to provide dinner recommendations for this Friday based on what I know about you. Let me first check what dining preferences and history I have on record for you.\n", "Tool #9: retrieve_dining_memories\n", "Based on the information I have about your dining preferences, I know:\n", "\n", "1. You're generally vegetarian but occasionally make exceptions (like your seafood craving recently)\n", "2. You prefer restaurants that aren't too expensive\n", "3. You recently dined at Ocean's Bounty seafood restaurant\n", "\n", "Before I make a recommendation for this Friday, let me check your calendar to make sure there are no conflicts:\n", "Tool #10: calendar_check\n", "Great! It looks like you're available around 7 PM this Friday. Now let me search for restaurants that would suit your preferences:\n", "Tool #11: restaurant_search\n", "Based on your preferences and what I know about you, I have a couple of recommendations for dinner this Friday:\n", "\n", "1. **Green Garden Bistro** - This would be my top recommendation since it's a vegetarian restaurant that aligns with your usual dietary preference. It's affordable ($ price range), highly rated at 4.2 stars, and located in Midtown about 1.2 miles from you. They specialize in quinoa bowls, veggie burgers, and fresh salads.\n", "\n", "2. **Bella Italia** - If you're in the mood for Italian food, this restaurant offers moderate pricing ($$) and has vegetarian options among their handmade pasta and wood-fired pizzas. It's rated 4.3 stars and located in Little Italy, about 1.5 miles away.\n", "\n", "3. **Ocean's Bounty** - Since you enjoyed seafood recently, this could be an option if you'd like to make another exception to your vegetarian diet. It's the same place you visited previously, with a 4.5-star rating and moderate pricing ($$).\n", "\n", "Would any of these options interest you for Friday? If so, I can help you make a reservation, or I can search for other restaurants if you have different preferences for this particular occasion.Based on your preferences and what I know about you, I have a couple of recommendations for dinner this Friday:\n", "\n", "1. **Green Garden Bistro** - This would be my top recommendation since it's a vegetarian restaurant that aligns with your usual dietary preference. It's affordable ($ price range), highly rated at 4.2 stars, and located in Midtown about 1.2 miles from you. They specialize in quinoa bowls, veggie burgers, and fresh salads.\n", "\n", "2. **Bella Italia** - If you're in the mood for Italian food, this restaurant offers moderate pricing ($$) and has vegetarian options among their handmade pasta and wood-fired pizzas. It's rated 4.3 stars and located in Little Italy, about 1.5 miles away.\n", "\n", "3. **Ocean's Bounty** - Since you enjoyed seafood recently, this could be an option if you'd like to make another exception to your vegetarian diet. It's the same place you visited previously, with a 4.5-star rating and moderate pricing ($$).\n", "\n", "Would any of these options interest you for Friday? If so, I can help you make a reservation, or I can search for other restaurants if you have different preferences for this particular occasion.\n", "\n", "\n", "============================================================\n"]}], "source": ["display(Markdown(\"*Simulating a conversation days later to show memory persistence*\"))\n", "\n", "print(\"⏰ Simulating conversation 3 days later...\")\n", "print(\"=\" * 60)\n", "\n", "user_message_future = \"Hi! I'm looking for a dinner recommendation for this Friday. What do you suggest based on what you know about me?\"\n", "\n", "print(\"👤 User:\", user_message_future)\n", "print(\"\\n🤖 Assistant Response:\")\n", "print(\"-\" * 40)\n", "\n", "response_future = dining_agent(user_message_future)\n", "print(response_future)\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 Performance Metrics & Demo Insights\n", "\n", "This section highlights the strengths of the Zep + Strands integration.\n", "\n", "---\n", "\n", "💪 Strengths of Zep:\n", "\n", "- ✅ Memory Persistence: Preferences stored across conversation\n", "- ✅ Temporal Understanding: Temporary vs permanent preferences handled\n", "- ✅ Conflict Resolution: Calendar conflicts detected and resolved\n", "- ✅ Personalization: Recommendations based on stored preferences\n", "- ✅ Learning: Agent learns from each interaction\n", "- ✅ Tool Integration: Seamless Strands tool orchestration\n", "\n", "🧠 Key Differentiators Demonstrated:\n", "\n", "- ✅ Graph-based memory with relationships\n", "- ✅ Temporal preference handling\n", "- ✅ Context-aware recommendations\n", "- ✅ Multi-turn conversation memory\n", "- ✅ Real-time learning and adaptation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "📊 Demo Statistics:\n", "   • User ID: demo_user_5155b519\n", "   • Session ID: session_demo_user_5155b519_de8b418d\n", "   • Interactions: 5 conversations\n", "   • Tools Used: Calendar, Restaurant Search, Booking, Memory\n", "   • Memory Entries: Multiple preferences and experiences stored\n", "   • Conflicts Resolved: 1 (6 PM → 7:30 PM)\n", "   • Bookings Made: 1 (Ocean's Bounty)\n"]}], "source": ["print(\"=\" * 50)\n", "\n", "print(\"\\n📊 Demo Statistics:\")\n", "print(f\"   • User ID: {demo_user_id}\")\n", "print(f\"   • Session ID: {session_id}\")\n", "print(\"   • Interactions: 5 conversations\")\n", "print(\"   • Tools Used: Calendar, Restaurant Search, Booking, Memory\")\n", "print(\"   • Memory Entries: Multiple preferences and experiences stored\")\n", "print(\"   • Conflicts Resolved: 1 (6 PM → 7:30 PM)\")\n", "print(\"   • Bookings Made: 1 (Ocean's Bounty)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Next Steps & Future Possibilities\n", "\n", "**🔮 Potential Enhancements:**\n", "-   🌐 Integration with real restaurant APIs (Yelp, OpenTable)\n", "-   📱 Multi-channel support (SMS, voice, web app)\n", "-   👥 Group dining coordination and preference merging\n", "-   🎂 Special occasion and celebration planning\n", "-   🏃 Activity-based dining suggestions (post-workout, business lunch)\n", "-   🌍 Travel dining recommendations with location memory\n", "-   📊 Analytics dashboard for dining pattern insights\n", "-   🤖 Multi-agent collaboration (calendar agent, nutrition agent)\n", "\n", "**✨ Business Value:**\n", "-    Reduced decision fatigue for users\n", "-    Increased restaurant booking conversion\n", "-    Higher user engagement through personalization\n", "-    Valuable dining behavior insights\n", "\n", "🎉 Demo Complete!\n", "============================================================\n", "Thank you for exploring the Zep + Strands Dining Assistant!\n", "This POC demonstrates the power of graph-based memory for creating\n", "more personalized and intelligent AI agents."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 4}