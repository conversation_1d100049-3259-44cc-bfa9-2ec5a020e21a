# Agent to Agent (A2A) protocol using Strands Agent SDK

An example Strands agent that helps with searching AWS documentation.

## Getting started

1. Install [uv](https://docs.astral.sh/uv/getting-started/installation/).
2. Configure AWS credentials, follow instructions [here](https://strandsagents.com/latest/user-guide/quickstart/#configuring-credentials).
3. Start the A2A server using `uv run __main__.py`.
4. Run the test client `uv run test_client.py`.