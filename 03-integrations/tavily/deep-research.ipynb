{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Build a Web Research Agent with Tavily API 🌐 🟠"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Welcome! In this tutorial, you'll learn how to build a web research agent using [Tavily API](https://docs.tavily.com/documentation/api-reference/introduction) that can search, extract, crawl, and reason over live web data.\n", "\n", "These skills are essential for anyone building AI agents or applications that need up-to-date, relevant information from the internet. By learning how to programmatically access and process real-time web data, you'll be able to bridge the gap between static language models and the dynamic world they operate in, making your agents smarter, more accurate, and context-aware.\n", "\n", "The AWS Strands Agent Framework enables rapid development of AI agents with minimal code. Many research agent implementations require extensive development efforts and rely on deterministic logic with constrained inputs and outputs. Alternatively, Strands facilitates building highly dynamic agents through natural language. Strands agents leverage prompt engineering to dynamically generate varied output types and accept diverse natural language inputs seamlessly.\n", "\n", "The core philosophy of Strands shifts complexity from hard-coded logic directly into the weights of the LLM, granting the model significant autonomy to determine agent behavior. This design approach ensures agents remain highly flexible and scalable, easily benefiting from advancements in new model releases. By simply integrating updated LLMs, developers can immediately unlock significant performance improvements without needing to modify any existing agent logic.\n", "\n", "By the end of this lesson, you'll know how to:\n", "- Connect agents to the web for up-to-date research\n", "- Orchestrate the web tools dynamically with the Strands agent framework\n", "- Build dynamic research agents capable of performing a range of tasks, including deep research, report writing, direct question answering, list building, etc.\n", "\n", "\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting Started\n", "\n", "Follow these steps to set up:\n", "\n", "1. **Sign up** for Tavily at [app.tavily.com](https://app.tavily.com/home/<USER>\n", "\n", "   *Refer to the screenshots linked below for step-by-step guidance:*\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"assets/sign-up.png\" width=\"65%\" />\n", "</div>\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"assets/api-key.png\" width=\"65%\" />\n", "</div>\n", "\n", "2. **Copy your API key** from your Tavily account dashboard.\n", "\n", "3. **Paste your API key** into the cell below and execute the cell."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To export your API key into a .env file, run the following cell (PLEASE REPLACE WITH YOUR API KEY):\n", "!echo \"TAVILY_API_KEY=<your-tavily-api-key>\" >> .env"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Install and import necessary dependencies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install strands-agents tavily-python --quiet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting Up Your Tavily API Client"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The code below will instantiate the Tavily client with your API key."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import getpass\n", "from dotenv import load_dotenv\n", "from tavily import TavilyClient\n", "\n", "# Load environment variables from .env file\n", "load_dotenv()\n", "\n", "# Prompt the user to securely input the API key if not already set in the environment\n", "if not os.environ.get(\"TAVILY_API_KEY\"):\n", "    os.environ[\"TAVILY_API_KEY\"] = getpass.getpass(\"TAVILY_API_KEY:\\n\")\n", "\n", "# Initialize the Tavily API client using the loaded or provided API key\n", "tavily_client = TavilyClient(api_key=os.getenv(\"TAVILY_API_KEY\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> **ℹ️ Strands Agent Architecture**\n", ">\n", "> This research agent is composed of three primary components:\n", ">\n", "> 1. **Language Model(LLM):** Acts as the agent's \"brain,\" responsible for understanding queries and generating responses.\n", "> 2. **Tools:** Includes `web search`, `web extract`, and `web crawl` functionalities, enabling the agent to interact with and gather information from the internet. Also includes a `research formatting` tool to allow the agent to dynamically alter the research output format based on the user's intent.\n", "> 3. **System Prompt:** Guides the agent's behavior, outlining how and when to use each tool to achieve its research objectives.\n", "\n", "Using these 3 major components, we will create this architecture in this notebook:\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"assets/architecture.png\" width=\"65%\" />\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Language Model\n", "\n", "We'll use the Strands SDK to set up a language model for our agent via AWS Bedrock. In this example, we're choosing Anthropic's Claude 4 Sonnet model, but you can substitute any [Bedrock-supported model](https://docs.aws.amazon.com/bedrock/latest/userguide/models-supported.html) as needed. Before you can use a foundation model in Amazon Bedrock, you must request access to it. For instructions, see [Add or remove access to Amazon Bedrock foundation models](https://docs.aws.amazon.com/bedrock/latest/userguide/model-access-modify.html) in the Amazon Bedrock User Guide."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["from strands.models import BedrockModel\n", "\n", "bedrock_model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-sonnet-4-20250514-v1:0\",\n", "    region_name=\"us-east-1\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Tool Definitions \n", "Let's define the following modular tools with the Tavi<PERSON>-<PERSON><PERSON><PERSON><PERSON> integration:\n", "1. **Search** the web for relevant information\n", "\n", "2. **Extract** the full page content from a webpage\n", "\n", "2. **Crawl** entire websites and scrape their content\n", "\n", "3. **Format Responses** dynamically using an LLM\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define the Tavily Search Tool 🔍\n", "\n", "We'll wrap the Tavily search endpoint in the Strands `@tool` decorator. Tools are passed to agents during initialization or at runtime, making them available for use throughout the agent's lifecycle. We implement a `format_search_results_for_agent` helper function which parses Tavily search results into a clear, structured format that's easy for the LLM to process. \n", "\n", "The agent will have the ability to set the query, time range, and include domains parameters. Feel free to experiment with different Tavily API parameter configurations to see <PERSON><PERSON> in action. You can adjust parameters such as the number of results, time range, and domain filters to tailor your search. For more information, read the [search API reference](https://docs.tavily.com/documentation/api-reference/endpoint/search) and [best practices guide](https://docs.tavily.com/documentation/best-practices/best-practices-search). \n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from strands import Agent, tool\n", "\n", "\n", "def format_search_results_for_agent(tavily_result):\n", "    \"\"\"\n", "    Format Tavily search results into a well-structured string for language models.\n", "\n", "    Args:\n", "        tavily_result (Dict): A Tavily search result dictionary\n", "\n", "    Returns:\n", "        str: A formatted string with search results organized for easy consumption by LLMs\n", "    \"\"\"\n", "    if (\n", "        not tavily_result\n", "        or \"results\" not in tavily_result\n", "        or not tavily_result[\"results\"]\n", "    ):\n", "        return \"No search results found.\"\n", "\n", "    formatted_results = []\n", "\n", "    for i, doc in enumerate(tavily_result[\"results\"], 1):\n", "        # Extract metadata\n", "        title = doc.get(\"title\", \"No title\")\n", "        url = doc.get(\"url\", \"No URL\")\n", "\n", "        # Create a formatted entry\n", "        formatted_doc = f\"\\nRESULT {i}:\\n\"\n", "        formatted_doc += f\"Title: {title}\\n\"\n", "        formatted_doc += f\"URL: {url}\\n\"\n", "\n", "        raw_content = doc.get(\"raw_content\")\n", "\n", "        # Prefer raw_content if it's available and not just whitespace\n", "        if raw_content and raw_content.strip():\n", "            formatted_doc += f\"Raw Content: {raw_content.strip()}\\n\"\n", "        else:\n", "            # Fallback to content if raw_content is not suitable or not available\n", "            content = doc.get(\"content\", \"\").strip()\n", "            formatted_doc += f\"Content: {content}\\n\"\n", "\n", "        formatted_results.append(formatted_doc)\n", "\n", "    # Join all formatted results with a separator\n", "    return \"\\n\" + \"\\n\".join(formatted_results)\n", "\n", "\n", "@tool\n", "def web_search(\n", "    query: str, time_range: str | None = None, include_domains: str | None = None\n", ") -> str:\n", "    \"\"\"Perform a web search. Returns the search results as a string, with the title, url, and content of each result ranked by relevance.\n", "\n", "    Args:\n", "        query (str): The search query to be sent for the web search.\n", "        time_range (str | None, optional): Limits results to content published within a specific timeframe.\n", "            Valid values: 'd' (day - 24h), 'w' (week - 7d), 'm' (month - 30d), 'y' (year - 365d).\n", "            Defaults to None.\n", "        include_domains (list[str] | None, optional): A list of domains to restrict search results to.\n", "            Only results from these domains will be returned. Defaults to None.\n", "\n", "    Returns:\n", "        formatted_results (str): The web search results\n", "    \"\"\"\n", "    client = TavilyClient(api_key=os.getenv(\"TAVILY_API_KEY\"))\n", "    formatted_results = format_search_results_for_agent(\n", "        client.search(\n", "            query=query,  # The search query to execute with <PERSON><PERSON>.\n", "            max_results=10,\n", "            time_range=time_range,\n", "            include_domains=include_domains,  # list of domains to specifically include in the search results.\n", "        )\n", "    )\n", "    return formatted_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define the Tavily Extract Tool 📄\n", "\n", "We'll wrap the Tavily extract endpoint in the Strands `@tool` decorator to retrieve the complete content (i.e., `raw_content`) of web pages. For efficiency, the extract endpoint can process up to 20 URLs at once in a single call\n", "\n", "For more information, read the [extract API reference](https://docs.tavily.com/documentation/api-reference/endpoint/extract) and [best practices guide](https://docs.tavily.com/documentation/best-practices/best-practices-extract). \n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["def format_extract_results_for_agent(tavily_result):\n", "    \"\"\"\n", "    Format Tavily extract results into a well-structured string for language models.\n", "\n", "    Args:\n", "        tavily_result (Dict): A Tavily extract result dictionary\n", "\n", "    Returns:\n", "        str: A formatted string with extract results organized for easy consumption by LLMs\n", "    \"\"\"\n", "    if not tavily_result or \"results\" not in tavily_result:\n", "        return \"No extract results found.\"\n", "\n", "    formatted_results = []\n", "\n", "    # Process successful results\n", "    results = tavily_result.get(\"results\", [])\n", "    for i, doc in enumerate(results, 1):\n", "        url = doc.get(\"url\", \"No URL\")\n", "        raw_content = doc.get(\"raw_content\", \"\")\n", "        images = doc.get(\"images\", [])\n", "\n", "        formatted_doc = f\"\\nEXTRACT RESULT {i}:\\n\"\n", "        formatted_doc += f\"URL: {url}\\n\"\n", "\n", "        if raw_content:\n", "            # Truncate very long content for readability\n", "            if len(raw_content) > 5000:\n", "                formatted_doc += f\"Content: {raw_content[:5000]}...\\n\"\n", "            else:\n", "                formatted_doc += f\"Content: {raw_content}\\n\"\n", "        else:\n", "            formatted_doc += \"Content: No content extracted\\n\"\n", "\n", "        if images:\n", "            formatted_doc += f\"Images found: {len(images)} images\\n\"\n", "            for j, image_url in enumerate(images[:3], 1):  # Show up to 3 images\n", "                formatted_doc += f\"  Image {j}: {image_url}\\n\"\n", "            if len(images) > 3:\n", "                formatted_doc += f\"  ... and {len(images) - 3} more images\\n\"\n", "\n", "        formatted_results.append(formatted_doc)\n", "\n", "    # Process failed results if any\n", "    failed_results = tavily_result.get(\"failed_results\", [])\n", "    if failed_results:\n", "        formatted_results.append(\"\\nFAILED EXTRACTIONS:\\n\")\n", "        for i, failure in enumerate(failed_results, 1):\n", "            url = failure.get(\"url\", \"Unknown URL\")\n", "            error = failure.get(\"error\", \"Unknown error\")\n", "            formatted_results.append(f\"Failed {i}: {url} - {error}\\n\")\n", "\n", "    # Add response time info\n", "    response_time = tavily_result.get(\"response_time\", 0)\n", "    formatted_results.append(f\"\\nResponse time: {response_time} seconds\")\n", "\n", "    return \"\\n\" + \"\".join(formatted_results)\n", "\n", "\n", "@tool\n", "def web_extract(\n", "    urls: str | list[str], include_images: bool = False, extract_depth: str = \"basic\"\n", ") -> str:\n", "    \"\"\"Extract content from one or more web pages using Tavily's extract API.\n", "\n", "    Args:\n", "        urls (str | list[str]): A single URL string or a list of URLs to extract content from.\n", "        include_images (bool, optional): Whether to also extract image URLs from the pages.\n", "                                       Defaults to False.\n", "        extract_depth (str, optional): The depth of extraction. 'basic' provides standard\n", "                                     content extraction, 'advanced' provides more detailed\n", "                                     extraction. Defaults to \"basic\".\n", "\n", "    Returns:\n", "        str: A formatted string containing the extracted content from each URL, including\n", "             the full raw content, any images found (if requested), and information about\n", "             any URLs that failed to be processed.\n", "    \"\"\"\n", "    try:\n", "        # Ensure urls is always a list for the API call\n", "        if isinstance(urls, str):\n", "            urls_list = [urls]\n", "        else:\n", "            urls_list = urls\n", "\n", "        # Clean and validate URLs\n", "        cleaned_urls = []\n", "        for url in urls_list:\n", "            if url.strip().startswith(\"{\") and '\"url\":' in url:\n", "                import re\n", "\n", "                m = re.search(r'\"url\"\\s*:\\s*\"([^\"]+)\"', url)\n", "                if m:\n", "                    url = m.group(1)\n", "\n", "            if not url.startswith((\"http://\", \"https://\")):\n", "                url = \"https://\" + url\n", "\n", "            cleaned_urls.append(url)\n", "\n", "        # Call Tavily extract API\n", "        api_response = tavily_client.extract(\n", "            urls=cleaned_urls,  # List of URLs to extract content from\n", "            include_images=include_images,  # Whether to include image extraction\n", "            extract_depth=extract_depth,  # Depth of extraction (basic or advanced)\n", "        )\n", "\n", "        # Format the results for the agent\n", "        formatted_results = format_extract_results_for_agent(api_response)\n", "        return formatted_results\n", "\n", "    except Exception as e:\n", "        return f\"Error during extraction: {e}\\nURLs attempted: {urls}\\nFailed to extract content.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define the Tavily Crawl Tool 🕸️ \n", "\n", "Now let’s use <PERSON><PERSON> to crawl a webpage and extract all its links. Web crawling is the process of automatically navigating through websites by following hyperlinks to discover numerous web pages and URLs (think of it like falling down a Wikipedia rabbit hole 🐇—clicking from page to page, diving deeper into interconnected topics). For autonomous web agents, this capability is essential for accessing deep web data which might be difficult to retrieve via search. \n", "\n", "\n", "We'll wrap the Tavily crawl endpoint in the Strands `@tool` decorator, similar to the search tool. We implement a `format_crawl_results_for_agent` helper function which parses Tavily search results into a clear, structured format that's easy for the LLM to process. \n", "\n", "The agent will have the ability to set the crawled url and the crawl instruction. You can adjust parameters such as the crawl depth, limit, and domain filters to tailor your crawl. For more information, read the crawl [API reference](https://docs.tavily.com/documentation/api-reference/endpoint/crawl) and [best practices guide](https://docs.tavily.com/documentation/best-practices/best-practices-crawl)."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def format_crawl_results_for_agent(tavily_result):\n", "    \"\"\"\n", "    Format Tavily crawl results into a well-structured string for language models.\n", "\n", "    Args:\n", "        tavily_result (List[Dict]): A list of Tavily crawl result dictionaries\n", "\n", "    Returns:\n", "        formatted_results (str): The formatted crawl results\n", "    \"\"\"\n", "    if not tavily_result:\n", "        return \"No crawl results found.\"\n", "\n", "    formatted_results = []\n", "\n", "    for i, doc in enumerate(tavily_result, 1):\n", "        # Extract metadata\n", "        url = doc.get(\"url\", \"No URL\")\n", "        raw_content = doc.get(\"raw_content\", \"\")\n", "\n", "        # Create a formatted entry\n", "        formatted_doc = f\"\\nRESULT {i}:\\n\"\n", "        formatted_doc += f\"URL: {url}\\n\"\n", "\n", "        if raw_content:\n", "            # Extract a title from the first line if available\n", "            title_line = raw_content.split(\"\\n\")[0] if raw_content else \"No title\"\n", "            formatted_doc += f\"Title: {title_line}\\n\"\n", "            formatted_doc += (\n", "                f\"Content: {raw_content[:4000]}...\\n\"\n", "                if len(raw_content) > 4000\n", "                else f\"Content: {raw_content}\\n\"\n", "            )\n", "\n", "        formatted_results.append(formatted_doc)\n", "\n", "    # Join all formatted results with a separator\n", "    return \"\\n\" + \"-\" * 40 + \"\\n\".join(formatted_results)\n", "\n", "\n", "@tool\n", "def web_crawl(url: str, instructions: str | None = None) -> str:\n", "    \"\"\"\n", "    Crawls a given URL, processes the results, and formats them into a string.\n", "\n", "    Args:\n", "        url (str): The URL of the website to crawl.\n", "\n", "        instructions (str | None, optional): Specific instructions to guide the\n", "                                             Tavily crawler, such as focusing on\n", "                                             certain types of content or avoiding\n", "                                             others. Defaults to None.\n", "\n", "    Returns:\n", "        str: A formatted string containing the crawl results. Each result includes\n", "             the URL and a snippet of the page content.\n", "             If an error occurs during the crawl process (e.g., network issue,\n", "             API error), a string detailing the error and the attempted URL is\n", "             returned.\n", "\n", "    \"\"\"\n", "    max_depth = 2\n", "    limit = 20\n", "\n", "    if url.strip().startswith(\"{\") and '\"url\":' in url:\n", "        import re\n", "\n", "        m = re.search(r'\"url\"\\s*:\\s*\"([^\"]+)\"', url)\n", "        if m:\n", "            url = m.group(1)\n", "\n", "    if not url.startswith((\"http://\", \"https://\")):\n", "        url = \"https://\" + url\n", "\n", "    try:\n", "        # Crawls the web using Tavily API\n", "        api_response = tavily_client.crawl(\n", "            url=url,  # The URL to crawl\n", "            max_depth=max_depth,  # Defines how far from the base URL the crawler can explore\n", "            limit=limit,  # Limits the number of results returned\n", "            instructions=instructions,  # Optional instructions for the crawler\n", "        )\n", "\n", "        tavily_results = (\n", "            api_response.get(\"results\")\n", "            if isinstance(api_response, dict)\n", "            else api_response\n", "        )\n", "\n", "        formatted = format_crawl_results_for_agent(tavily_results)\n", "        return formatted\n", "    except Exception as e:\n", "        return f\"Error: {e}\\n\" f\"URL attempted: {url}\\n\" \"Failed to crawl the website.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📝 Research Formatter Tool\n", "\n", "The `format_research_response` tool uses a specialized agent to transform raw research content into clear, well-structured, and properly cited responses. It ensures every factual claim is supported by inline citations and provides a complete \"Sources\" section with URLs. The tool automatically selects the most appropriate format—such as direct answer, blog, academic report, executive summary, bullet points, or comparison—based on the user's question and the research content.\n", "\n", "Use this tool as the final step, after completing all research, to transform your findings into a clear, well-structured, and audience-appropriate response before delivering it to the user.\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Define specialized system prompt for research response formatting\n", "RESEARCH_FORMATTER_PROMPT = \"\"\"\n", "You are a specialized Research Response Formatter Agent. Your role is to transform research content into well-structured, properly cited, and reader-friendly formats.\n", "\n", "Core formatting requirements (ALWAYS apply):\n", "1. Include inline citations using [n] notation for EVERY factual claim\n", "2. Provide a complete \"Sources\" section at the end with numbered references an urls\n", "3. Write concisely - no repetition or filler words\n", "4. Ensure information density - every sentence should add value\n", "5. Maintain professional, objective tone\n", "6. Format your response in markdown\n", "\n", "Based on the semantics of the user's original research question, format your response in one of the following styles:\n", "- **Direct Answer**: Concise, focused response that directly addresses the question\n", "- **Blog Style**: Engaging introduction, subheadings, conversational tone, conclusion\n", "- **Academic Report**: Abstract, methodology, findings, analysis, conclusions, references\n", "- **Executive Summary**: Key findings upfront, bullet points, actionable insights\n", "- **Bullet Points**: Structured lists with clear hierarchy and supporting details\n", "- **Comparison**: Side-by-side analysis with clear criteria and conclusions\n", "\n", "When format is not specified, analyze the research content and user query to determine:\n", "- Complexity level (simple vs. comprehensive)\n", "- Audience (general public vs. technical)\n", "- Purpose (informational vs. decision-making)\n", "- Content type (factual summary vs. analytical comparison)\n", "\n", "Your response below should be polished, containing only the information that is relevant to the user's query and NOTHING ELSE.\n", "\n", "Your final research response:\n", "\"\"\"\n", "\n", "\n", "@tool\n", "def format_research_response(\n", "    research_content: str, format_style: str = None, user_query: str = None\n", ") -> str:\n", "    \"\"\"Format research content into a well-structured, properly cited response.\n", "\n", "    This tool uses a specialized Research Formatter Agent to transform raw research\n", "    into polished, reader-friendly content with proper citations and optimal structure.\n", "\n", "    Args:\n", "        research_content (str): The raw research content to be formatted\n", "        format_style (str, optional): Desired format style (e.g., \"blog\", \"report\",\n", "                                    \"executive summary\", \"bullet points\", \"direct answer\")\n", "        user_query (str, optional): Original user question to help determine appropriate format\n", "\n", "    Returns:\n", "        str: Professionally formatted research response with proper citations,\n", "             clear structure, and appropriate style for the intended audience\n", "    \"\"\"\n", "    try:\n", "        bedrock_model = BedrockModel(\n", "            model_id=\"us.anthropic.claude-sonnet-4-20250514-v1:0\",\n", "            region_name=\"us-east-1\",\n", "        )\n", "        # Strands Agents SDK makes it easy to create a specialized agent\n", "        formatter_agent = Agent(\n", "            model=bedrock_model,\n", "            system_prompt=RESEARCH_FORMATTER_PROMPT,\n", "        )\n", "\n", "        # Prepare the input for the formatter\n", "        format_input = f\"Research Content:\\n{research_content}\\n\\n\"\n", "\n", "        if format_style:\n", "            format_input += f\"Requested Format Style: {format_style}\\n\\n\"\n", "\n", "        if user_query:\n", "            format_input += f\"Original User Query: {user_query}\\n\\n\"\n", "\n", "        format_input += \"Please format this research content according to the guidelines and appropriate style.\"\n", "\n", "        # Call the agent and return its response\n", "        response = formatter_agent(format_input)\n", "        return str(response)\n", "    except Exception as e:\n", "        return f\"Error in research formatting: {str(e)}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. Agent System Prompt\n", "\n", "The Strands SDK enables the agent to reason about which actions to take, use the available tools in sequence, and iterate as needed until it completes its research task. The system prompt is especially important—it instructs the agent on best practices for using the tools together, ensuring that the agent's responses are thorough, accurate, and well-sourced.\n", "\n", "You are encouraged to experiment with the system prompt or try different language models to change the agent's style, personality, or optimize its performance for specific use cases."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "today = datetime.datetime.today().strftime(\"%A, %B %d, %Y\")\n", "\n", "SYSTEM_PROMPT = f\"\"\"\n", "You are an expert research assistant specializing in deep, comprehensive information gathering and analysis.\n", "You are equipped with advanced web tools: Web Search, Web Extract, and Web Crawl.\n", "Your mission is to conduct comprehensive, accurate, and up-to-date research, grounding your findings in credible web sources.\n", "\n", "**Today's Date:** {today}\n", "\n", "Your TOOLS include:\n", "\n", "1. WEB SEARCH\n", "- Conduct thorough web searches using the web_search tool.\n", "- You will enter a search query and the web_search tool will return 10 results ranked by semantic relevance.\n", "- Your search results will include the title, url, and content of 10 results ranked by semantic relevance.\n", "\n", "2. WEB EXTRACT\n", "- Conduct web extraction with the web_extract tool.\n", "- You will enter a url and the web_extract tool will extract the content of the page.\n", "- Your extract results will include the url and content of the page.\n", "- This tool is great for finding all the information that is linked from a single page.\n", "\n", "3. WEB CRAWL\n", "- Conduct deep web crawls with the web_crawl tool.\n", "- You will enter a url and the web_crawl tool will find all the nested links.\n", "- Your crawl results will include the url and content of the pages that were discovered.\n", "- This tool is great for finding all the information that is linked from a single page.\n", "\n", "3. FORMATTING RESEARCH RESPONSE\n", "- You will use the format_research_response tool to format your research response.\n", "- This tool will create a well-structured response that is easy to read and understand.\n", "- The response will clearly address the user's query, the research results.\n", "- The response will be in markdown format.\n", "\n", "RULES:\n", "- You must start the research process by creating a plan. Think step by step about what you need to do to answer the research question.\n", "- You can iterate on your research plan and research response multiple times, using combinations of the tools available to you until you are satisfied with the results.\n", "- You must use the format_research_response tool at the end of your research process.\n", "\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's combine the search and crawl tools into a single agent, as shown in the diagram below.\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"assets/agent.svg\" width=\"65%\" />\n", "</div>\n"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["web_agent = Agent(\n", "    model=bedrock_model,\n", "    system_prompt=SYSTEM_PROMPT,\n", "    tools=[\n", "        web_search,\n", "        web_extract,\n", "        web_crawl,\n", "        format_research_response,\n", "    ],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's test the agent."]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["research_prompt = \"find the integration partners of the tavily api\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["research = web_agent(research_prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now lets view the final research output."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Markdown\n", "\n", "# Find the specific tool result for format_research_response\n", "for msg in web_agent.messages:\n", "    if msg.get(\"role\") == \"user\":\n", "        for content in msg.get(\"content\", []):\n", "            tool_result = content.get(\"toolResult\", {})\n", "            if tool_result.get(\"status\") == \"success\":\n", "                # Check if this corresponds to format_research_response\n", "                # Look for the toolUseId that matches format_research_response\n", "                tool_use_id = tool_result.get(\"toolUseId\", \"\")\n", "\n", "                # Find the matching tool use in assistant messages\n", "                for assistant_msg in web_agent.messages:\n", "                    if assistant_msg.get(\"role\") == \"assistant\":\n", "                        for assistant_content in assistant_msg.get(\"content\", []):\n", "                            tool_use = assistant_content.get(\"toolUse\", {})\n", "                            if (\n", "                                tool_use.get(\"toolUseId\") == tool_use_id\n", "                                and tool_use.get(\"name\") == \"format_research_response\"\n", "                            ):\n", "\n", "                                formatted_content = tool_result.get(\"content\", [{}])[\n", "                                    0\n", "                                ].get(\"text\", \"\")\n", "                                display(Markdown(formatted_content))\n", "                                break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's view the tool execution order."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tools_used = []\n", "\n", "print(\"🚀 Tool Execution Flow\")\n", "print(\"─\" * 50)\n", "\n", "for i, msg in enumerate(web_agent.messages):\n", "    if msg.get(\"role\") == \"assistant\" and msg.get(\"content\"):\n", "        content = msg.get(\"content\", [])\n", "\n", "        for item in content:\n", "            if isinstance(item, dict) and \"toolUse\" in item:\n", "                tool_use = item[\"toolUse\"]\n", "                tool_name = tool_use.get(\"name\", \"unknown\")\n", "                tool_input = tool_use.get(\"input\", {})\n", "                tools_used.append(tool_name)\n", "\n", "                # Choose emoji based on tool type\n", "                if \"crawl\" in tool_name:\n", "                    emoji = \"🕷️\"\n", "                elif \"search\" in tool_name:\n", "                    emoji = \"🔍\"\n", "                elif \"format\" in tool_name:\n", "                    emoji = \"📝\"\n", "                elif \"extract\" in tool_name:\n", "                    emoji = \"📄\"\n", "                else:\n", "                    emoji = \"⚡\"\n", "\n", "                print(f\"{len(tools_used):2d}. {emoji} {tool_name}\")\n", "\n", "                # Format input nicely\n", "                if isinstance(tool_input, dict):\n", "                    for key, value in tool_input.items():\n", "                        # Truncate long values for readability\n", "                        if isinstance(value, str) and len(value) > 80:\n", "                            value = value[:77] + \"...\"\n", "                        print(f\"    💭 {key}: {value}\")\n", "                else:\n", "                    print(f\"    💭 input: {tool_input}\")\n", "                print()  # Add blank line for readability\n", "\n", "print(f\"🎯 Completed {len(tools_used)} tool invocations!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can view the agent sub steps for monitoring and observability."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["web_agent.messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["research.metrics"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}