# Strands Agent Integrations

| Integration                           | Features showcased                                                                                                                                                               |
| ------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [A2A Protocol](./A2A-protocol/) | Demonstrates agent-to-agent communication protocol for collaborative problem-solving between specialized AI agents.         
| [Amazon Data Processing Agent](./Amazon-DataProcessing-Agent/) | Intelligent conversational AI assistant for AWS data processing services including AWS Glue, Amazon Athena, and Amazon EMR-EC2 with natural language interface and MCP integration. |
| [Amazon Neptune](./Amazon-Neptune/) | This directory contains several examples of how to use Strands Agent SDK with [Amazon Neptune](https://aws.amazon.com/neptune/developer-resources/).                                                     |
| [Aurora DSQL](./aurora-DSQL) | Demonstrates the Strands Agent integration with Amazon Aurora DSQL. |
| [Nova Act](./nova-act) | Nova Act integration with Strands. Amazon Nova Act is an AI model trained to perform actions within a web browser. |
| [Nova Sonic](./nova-sonic) | Nova Sonic integration with Strands. [Amazon Nova Sonic](https://aws.amazon.com/ai/generative-ai/nova/speech/) provides real-time, conversational interactions through bidirectional audio streaming, enabling natural, 
| [Tavily](./tavily/)             | This agent uses Tavily's web search, extract and crawl APIs to gather information from reliable sources, extract key insights, and save comprehensive research reports in Markdown format. |
| [Arize](./Openinference-Arize)           | Demonstrates Arize Observability integration with Strands Agent which is a restuarant assistant with AWS Services |
| [Zep AI](./zep-ai/)             | Minimal proof-of-concept for a personal dining assistant agent using Zep AI's graph-based memory and the Strands framework. |
| [Supabase](./supabase/) | Demonstrate using Strands Agent with Supabase MCP to build a application backend with natural language. |
