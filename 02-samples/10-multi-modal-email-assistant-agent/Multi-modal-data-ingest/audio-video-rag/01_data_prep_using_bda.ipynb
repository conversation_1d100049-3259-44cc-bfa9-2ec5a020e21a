{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["# Amazon Bedrock Knowledge Bases - Audio and Video Data Preparation using Amazon Bedrock Data Automation\n", "\n", "# Introduction\n", "\n", "This module demonstrates how to build a Multimodal Retrieval-Augmented Generation (RAG) application using Amazon Bedrock Data Automation (BDA) and Bedrock Knowledge Bases (KB). The application is designed to analyze and generate insights from multi-modalal data, including video and audio data. By incorporating contextual information from your own data sources with BDA, you can create highly accurate and secure intelligent search Generative AI applications.\n", "\n", "In this notebook, it shows the first step of building this intelligent search application: how to efficiently process video and audio data by using BDA to generate contextual outputs for KB embedding.\n", "\n", "With the latest integration between BDA and Amazon Bedrock Knowledge Bases, you can specify BDA as parser of your data source for Bedrock Knowledge Bases.\n", "\n", "## Key Features\n", "\n", "- Amazon Bedrock Data Automation (BDA): A managed service that automatically extracts content from multimodal data. BDA streamlines the generation of valuable insights from unstructured multimodal content such as documents, images, audio, and videos through a unified multi-modal inference API.\n", "  \n", "- Bedrock KB to build a RAG solution with BDA: Amazon Bedrock KB extract multi-modal content using BDA, generating semantic embeddings using the selected embedding model, and storing them in the chosen vector store. This enables users to retrieve and generate answers to questions derived not only from text but also from image, video and audio data. Additionally, retrieved results include source attribution for visual data, enhancing transparency and building trust in the generated outputs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites\n", "Please make sure to enable `Anthropic Claude 3 Sonnet` , `Amazon Nova Micro` and  `Titan Text Embeddings V2` model access in Amazon Bedrock Console\n", "\n", "You need to have suitable IAM role permission to run this notebook. For IAM role, choose either an existing IAM role in your account or create a new role. The role must the necessary permissions to invoke the BDA, Bedrock KB, create IAM roles, SageMaker and S3 APIs.\n", "\n", "Note: The AdministratorAccess IAM policy can be used, if allowed by security policies at your organization.\n", "\n", "<div class=\"alert alert-block alert-info\">\n", "<b>Note:</b> Please run the notebook cell one at a time instead of using \"Run All Cells\" option.\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup notebook and boto3 clients\n", "\n", "In this step, we will import some necessary libraries that will be used throughout this notebook. To use Amazon Bedrock Data Automation (BDA) with boto3, you'll need to ensure you have the latest version of the AWS SDK for Python (boto3) installed. Version Boto3 1.35.96 of later is required.\n", "\n", "Note: At time of Public Preview launch, BDA is available in us-west-2 only."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install --upgrade pip --quiet\n", "%pip install -r ../requirements.txt --no-deps -U --quiet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# restart kernel\n", "from IPython.core.display import HTML\n", "\n", "HTML(\"<script>Jupyter.notebook.kernel.restart()</script>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-block alert-info\">\n", "<b>Note:</b> In this workshop, a new S3 bucket following the naming convention \"kb-bda-multimodal-datasource-{account_id}\" will be used, and the input and output will be saved under a folder called \"bda\" in the default bucket.\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "import json\n", "import uuid\n", "from datetime import datetime\n", "import time\n", "import random\n", "from pathlib import Path\n", "from IPython.display import clear_output\n", "import logging\n", "import sys\n", "import requests\n", "\n", "# Get current path and go up two parent directories\n", "current_path = Path().resolve()\n", "parent_path = current_path.parent  # Go up two levels\n", "\n", "# Add to sys.path if not already there\n", "if str(parent_path) not in sys.path:\n", "    sys.path.append(str(parent_path))\n", "\n", "# Now you can import from utils"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Clients\n", "suffix = random.randrange(200, 900)\n", "\n", "sts_client = boto3.client(\"sts\")\n", "account_id = sts_client.get_caller_identity()[\"Account\"]\n", "\n", "bucket_name_kb = f\"bda-kb-{suffix}-1\"  # replace it with your first bucket name.\n", "region_name = (\n", "    \"us-east-1\"  # can be removed ones BDA is GA and available in other regions.\n", ")\n", "region = region_name\n", "\n", "s3_client = boto3.client(\"s3\", region_name=region_name)\n", "\n", "bda_client = boto3.client(\"bedrock-data-automation\", region_name=region_name)\n", "bda_runtime_client = boto3.client(\n", "    \"bedrock-data-automation-runtime\", region_name=region_name\n", ")\n", "\n", "bedrock_agent_client = boto3.client(\"bedrock-agent\")\n", "bedrock_agent_runtime_client = boto3.client(\"bedrock-agent-runtime\")\n", "\n", "logging.basicConfig(\n", "    format=\"[%(asctime)s] p%(process)s {%(filename)s:%(lineno)d} %(levelname)s - %(message)s\",\n", "    level=logging.INFO,\n", ")\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to check if the bucket exists, if not, create the data_bucket\n", "from utils.knowledge_base_operators import bucket_exists\n", "\n", "# Create S3 bucket for the KB if it doesn't exist\n", "if not bucket_exists(bucket_name_kb):\n", "    print(f\"Bucket '{bucket_name_kb}' does not exist. Creating it now...\")\n", "    if region == \"us-east-1\":\n", "        s3_client.create_bucket(Bucket=bucket_name_kb)\n", "    else:\n", "        s3_client.create_bucket(\n", "            Bucket=bucket_name_kb,\n", "            CreateBucketConfiguration={\"LocationConstraint\": region},\n", "        )\n", "    print(f\"Bucket '{bucket_name_kb}' created successfully.\")\n", "else:\n", "    print(f\"Bucket '{bucket_name_kb}' already exists.\")\n", "\n", "\n", "bucket_name_input = f\"s3://{bucket_name_kb}/bda/input\"  # DBA input path\n", "bucket_name_output = f\"s3://{bucket_name_kb}/bda/output\"  # DBA output path"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create a BDA project\n", "To start a BDA job, you need a BDA project, which organizes both standard and custom output configurations. This project is reusable, allowing you to apply the same configuration to process multiple video/audio files that share the same settings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["project_name = f\"bda-kb-project-{str(uuid.uuid4())[0:4]}\"\n", "\n", "# delete project if it already exists\n", "projects_existing = [\n", "    project\n", "    for project in bda_client.list_data_automation_projects()[\"projects\"]\n", "    if project[\"projectName\"] == project_name\n", "]\n", "if len(projects_existing) > 0:\n", "    print(f\"Deleting existing project: {projects_existing[0]}\")\n", "    bda_client.delete_data_automation_project(\n", "        projectArn=projects_existing[0][\"projectArn\"]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# BDA API standard output schema\n", "response = bda_client.create_data_automation_project(\n", "    projectName=project_name,\n", "    projectDescription=\"BDA video processing project\",\n", "    projectStage=\"DEVELOPMENT\",\n", "    standardOutputConfiguration={\n", "        \"video\": {\n", "            \"extraction\": {\n", "                \"category\": {\n", "                    \"state\": \"ENABLED\",\n", "                    \"types\": [\"CONTENT_MODERATION\", \"TEXT_DETECTION\", \"TRANSCRIPT\"],\n", "                },\n", "                \"boundingBox\": {\"state\": \"ENABLED\"},\n", "            },\n", "            \"generativeField\": {\n", "                \"state\": \"ENABLED\",\n", "                \"types\": [\"VIDEO_SUMMARY\", \"CHAPTER_SUMMARY\", \"IAB\"],\n", "            },\n", "        },\n", "        \"audio\": {\n", "            \"extraction\": {\n", "                \"category\": {\n", "                    \"state\": \"ENABLED\",\n", "                    \"types\": [\n", "                        \"AUDIO_CONTENT_MODERATION\",\n", "                        \"TOPIC_CONTENT_MODERATION\",\n", "                        \"TRANSCRIPT\",\n", "                    ],\n", "                }\n", "            },\n", "            \"generativeField\": {\n", "                \"state\": \"ENABLED\",\n", "                \"types\": [\"AUDIO_SUMMARY\", \"TOPIC_SUMMARY\", \"IAB\"],\n", "            },\n", "        },\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kb_project_arn = response.get(\"projectArn\")\n", "print(\"BDA kb project ARN:\", kb_project_arn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tempfile\n", "from pydub import AudioSegment\n", "import os\n", "\n", "# URL of the audio file\n", "url = \"https://s2.q4cdn.com/299287126/files/doc_financials/2024/q3/Amazon-Quarterly-Earnings-Report-Q3-2024-Full-Call-v2.wav\"\n", "\n", "# Create a temporary directory to store the files\n", "with tempfile.TemporaryDirectory() as temp_dir:\n", "    # Download the file\n", "    print(\"Downloading audio file...\")\n", "    response = requests.get(url)\n", "    temp_input_path = os.path.join(temp_dir, \"original.wav\")\n", "\n", "    # Save the downloaded file\n", "    with open(temp_input_path, \"wb\") as f:\n", "        f.write(response.content)\n", "\n", "    # Load the audio file\n", "    print(\"Processing audio file...\")\n", "    audio = AudioSegment.from_wav(temp_input_path)\n", "\n", "    # Calculate duration in milliseconds (11 minutes and 2 seconds = 662 seconds)\n", "    target_duration = 662 * 1000  # Convert to milliseconds\n", "\n", "    # Trim the audio\n", "    trimmed_audio = audio[:target_duration]\n", "\n", "    # Save the trimmed audio to a temporary file\n", "    temp_output_path = os.path.join(temp_dir, \"trimmed.wav\")\n", "    trimmed_audio.export(temp_output_path, format=\"wav\")\n", "\n", "    # Upload the trimmed file to S3\n", "    print(\"Uploading trimmed audio to S3...\")\n", "    object_name = \"bda/input/earnings-call-q3-2024-trimmed.wav\"\n", "\n", "    with open(temp_output_path, \"rb\") as file:\n", "        s3_client.upload_fileobj(file, bucket_name_kb, object_name)\n", "\n", "print(\"Process completed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Start BDA task audio"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start BDA task audio\n", "\n", "input_name = object_name\n", "output_name = \"bda/output/\"\n", "\n", "response_aud = bda_runtime_client.invoke_data_automation_async(\n", "    inputConfiguration={\"s3Uri\": f\"s3://{bucket_name_kb}/{input_name}\"},\n", "    outputConfiguration={\"s3Uri\": f\"s3://{bucket_name_kb}/{output_name}\"},\n", "    dataAutomationProfileArn=f\"arn:aws:bedrock:us-east-1:{account_id}:data-automation-profile/us.data-automation-v1\",\n", "    dataAutomationConfiguration={\n", "        \"dataAutomationProjectArn\": kb_project_arn,\n", "        \"stage\": \"DEVELOPMENT\",\n", "    },\n", ")\n", "response_aud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["invocation_audio_arn = response_aud.get(\"invocationArn\")\n", "print(\"BDA audio task started:\", invocation_audio_arn)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### We can monitor the progress status of BDA task execution, by running the code cell below"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["statusAudio, status_aud_response = None, None\n", "while (statusAudio not in [\"Success\", \"ServiceError\", \"ClientError\"]) and (\n", "    statusAudio not in [\"Success\", \"ServiceError\", \"ClientError\"]\n", "):\n", "    status_aud_response = bda_runtime_client.get_data_automation_status(\n", "        invocationArn=invocation_audio_arn\n", "    )\n", "    statusAudio = status_aud_response.get(\"status\")\n", "    clear_output(wait=True)\n", "    # print(f\"{datetime.now().strftime('%H:%M:%S')} : BDA kb audio task: {statusAudio}\")\n", "\n", "    clear_output(wait=True)\n", "    print(\n", "        f\"{datetime.now().strftime('%H:%M:%S')} : \" f\"BDA kb audio task: {statusAudio}\"\n", "    )\n", "    time.sleep(5)\n", "\n", "output_aud_config = status_aud_response.get(\"outputConfiguration\", {}).get(\"s3Uri\")\n", "print(\"Ouput configuration file:\", output_aud_config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Examine the BDA output for the processed audio file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out_aud_loc = (\n", "    status_aud_response[\"outputConfiguration\"][\"s3Uri\"]\n", "    .split(\"/job_metadata.json\", 1)[0]\n", "    .split(bucket_name_kb + \"/\")[1]\n", ")\n", "out_aud_loc += \"/0/standard_output/0/result.json\"\n", "print(out_aud_loc)\n", "s3_client.download_file(bucket_name_kb, out_aud_loc, \"result_aud.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_vid = json.load(open(\"result_aud.json\"))\n", "print(data_vid[\"audio\"][\"summary\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "Congrats! By following this notebook, you finished the BDA processing of video and audio files, and you are ready to build a robust Multimodal RAG application tailored to your specific needs in the next notebook: 02_audio_video_rag_kb."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "conda_python3", "language": "python", "name": "conda_python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 4}