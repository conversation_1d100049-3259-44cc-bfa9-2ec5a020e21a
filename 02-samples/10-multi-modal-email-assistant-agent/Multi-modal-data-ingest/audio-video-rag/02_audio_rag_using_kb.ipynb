{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["# Integrate with Amazon Bedrock Knowledge Bases:\n", "After processed the audio and video files with a BDA project, next it is time to integrate with Bedrock KB.\n", "## Steps involved in this integration: \n", "- Set up a knowledge base to parse documents using Amazon Bedrock Data Automation as the parser.\n", "- Ingest the processed data into the knowledge base for retrieval and response generation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-block alert-info\">\n", "<b>Note:</b> Please run this notebook after you finish running the first notebook: 01_data_prep_using_bda.ipynb, the notebook cell one at a time instead of using \"Run All Cells\" option.\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup notebook and boto3 clients\n", "\n", "In this step, we will import some necessary libraries that will be used throughout this notebook. To use Amazon Bedrock Data Automation (BDA) with boto3, you'll need to ensure you have the latest version of the AWS SDK for Python (boto3) installed. Version Boto3 1.35.96 of later is required.\n", "\n", "Note: At time of Public Preview launch, BDA is available in us-west-2 only."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -r ../requirements.txt --no-deps --quiet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# restart kernel\n", "from IPython.core.display import HTML\n", "\n", "HTML(\"<script>Jupyter.notebook.kernel.restart()</script>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "import time\n", "import pprint\n", "import random\n", "from pathlib import Path\n", "import logging\n", "import sys\n", "\n", "# Get current path and go up two parent directories\n", "current_path = Path().resolve()\n", "parent_path = current_path.parent  # Go up two levels\n", "\n", "# Add to sys.path if not already there\n", "if str(parent_path) not in sys.path:\n", "    sys.path.append(str(parent_path))\n", "\n", "# Now you can import from utils\n", "from utils.knowledge_base import BedrockKnowledgeBase"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Clients\n", "suffix = random.randrange(200, 900)\n", "\n", "sts_client = boto3.client(\"sts\")\n", "account_id = sts_client.get_caller_identity()[\"Account\"]\n", "\n", "region_name = (\n", "    \"us-east-1\"  # can be removed ones BDA is GA and available in other regions.\n", ")\n", "region = region_name\n", "\n", "s3_client = boto3.client(\"s3\", region_name=region_name)\n", "\n", "bda_client = boto3.client(\"bedrock-data-automation\", region_name=region_name)\n", "bda_runtime_client = boto3.client(\n", "    \"bedrock-data-automation-runtime\", region_name=region_name\n", ")\n", "\n", "bedrock_agent_client = boto3.client(\"bedrock-agent\")\n", "bedrock_agent_runtime_client = boto3.client(\"bedrock-agent-runtime\")\n", "\n", "logging.basicConfig(\n", "    format=\"[%(asctime)s] p%(process)s {%(filename)s:%(lineno)d} %(levelname)s - %(message)s\",\n", "    level=logging.INFO,\n", ")\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Copy local BDA output files to a S3 bucket for KB integration\n", "# Function to check if the bucket exists, if not, create the data_bucket\n", "from utils.knowledge_base_operators import bucket_exists\n", "\n", "suffix = random.randrange(200, 900)\n", "bucket_name_kb = f\"bedrock-bda-kb-{suffix}-1\"\n", "# Create S3 bucket for the KB if it doesn't exist\n", "if not bucket_exists(bucket_name_kb):\n", "    print(f\"Bucket '{bucket_name_kb}' does not exist. Creating it now...\")\n", "    if region == \"us-east-1\":\n", "        s3_client.create_bucket(Bucket=bucket_name_kb)\n", "    else:\n", "        s3_client.create_bucket(\n", "            Bucket=bucket_name_kb,\n", "            CreateBucketConfiguration={\"LocationConstraint\": region},\n", "        )\n", "    print(f\"Bucket '{bucket_name_kb}' created successfully.\")\n", "else:\n", "    print(f\"Bucket '{bucket_name_kb}' already exists.\")\n", "\n", "\n", "obj_audio = \"bda/dataset/result_aud.json\"\n", "s3_client.upload_file(\"result_aud.json\", bucket_name_kb, obj_audio)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get the current timestamp\n", "current_time = time.time()\n", "\n", "# Format the timestamp as a string\n", "timestamp_str = time.strftime(\"%Y%m%d%H%M%S\", time.localtime(current_time))[-7:]\n", "# Create the suffix using the timestamp\n", "suffix = f\"{timestamp_str}\"\n", "\n", "knowledge_base_name = f\"bedrock-multi-modal-kb-{suffix}\"\n", "knowledge_base_description = \"Multi-modal RAG knowledge base.\"\n", "\n", "foundation_model = \"anthropic.claude-3-sonnet-20240229-v1:0\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Start the Knowledge Base creation \n", "\n", "In this notebook, the process of creating a KB is simplified by using a wrapper function from the knowledge_base.py file in \"utils\" folder of this notebook. The whole process of creating data source, creating a KB, creating an embedding index, saving the index in a vector data store is simplified by using this function. \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Please uncomment the data sources that you want to add and update the placeholder values accordingly.\n", "\n", "# data=[{\"type\": \"S3\", \"bucket_name\": bucket_name, \"inclusionPrefixes\": [\"bda/dataset/\"]}]\n", "data = [{\"type\": \"S3\", \"bucket_name\": bucket_name_kb}]\n", "\n", "\n", "# {\"type\": \"SHAREPOINT\", \"tenantId\": \"888d0b57-69f1-4fb8-957f-e1f0bedf64de\", \"domain\": \"yourdomain\",\n", "#   \"authType\": \"OAUTH2_CLIENT_CREDENTIALS\",\n", "#  \"credentialsSecretArn\": f\"arn:aws::secretsmanager:{region_name}:secret:<<your_secret_name>>\",\n", "#  \"siteUrls\": [\"https://yourdomain.sharepoint.com/sites/mysite\"]\n", "# },\n", "\n", "\n", "pp = pprint.PrettyPrinter(indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 1 - Create Knowledge Base with Multi modality"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# For multi-modal RAG While instantiating BedrockKnowledgeBase, pass multi_modal= True and choose the parser you want to use\n", "\n", "knowledge_base = BedrockKnowledgeBase(\n", "    kb_name=f\"{knowledge_base_name}\",\n", "    kb_description=knowledge_base_description,\n", "    data_sources=data,\n", "    multi_modal=True,\n", "    parser=\"BEDROCK_DATA_AUTOMATION\",  #'BEDROCK_Data Automation service is used'\n", "    chunking_strategy=\"FIXED_SIZE\",\n", "    suffix=f\"{suffix}-f\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 2 - Start data ingestion job to KB\n", "\n", "Once the KB and data source(s) created, we can start the ingestion job for each data source. During the ingestion job, KB will fetch the documents from the data source, Parse the document to extract text, chunk it based on the chunking size provided, create embeddings of each chunk and then write it to the vector database, in this case OSS.\n", "\n", "NOTE: Currently, you can only kick-off one ingestion job at one time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ensure that the kb is available\n", "time.sleep(30)\n", "# sync knowledge base\n", "knowledge_base.start_ingestion_job()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# keep the kb_id for invocation later in the invoke request\n", "kb_id = knowledge_base.get_knowledge_base_id()\n", "%store kb_id"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 3 -  Test the Knowledge Base\n", "Now the Knowlegde Base is available we can test it out using the [**retrieve**](https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/bedrock-agent-runtime/client/retrieve.html) and [**retrieve_and_generate**](https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/bedrock-agent-runtime/client/retrieve_and_generate.html) functions. \n", "\n", "#### Testing Knowledge Base with Retrieve and Generate API\n", "\n", "Let's first test the knowledge base using the retrieve and generate API. With this API, Bedrock takes care of retrieving the necessary references from the knowledge base and generating the final answer using a foundation model from Bedrock.\n", "\n", "query = Give me the summary of the AWS Rethink podcast hosted by <PERSON> and <PERSON><PERSON>?\n", "\n", "The right response for this query is expected to fetch from a the audio transcript ingested in Knowledge Bases."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"Give me a summary of Amazon's earning\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["foundation_model = \"anthropic.claude-3-sonnet-20240229-v1:0\"\n", "# foundation_model = \"amazon.nova-micro-v1:0\"\n", "\n", "response = bedrock_agent_runtime_client.retrieve_and_generate(\n", "    input={\"text\": query},\n", "    retrieveAndGenerateConfiguration={\n", "        \"type\": \"KNOWLEDGE_BASE\",\n", "        \"knowledgeBaseConfiguration\": {\n", "            \"knowledgeBaseId\": kb_id,\n", "            \"modelArn\": \"arn:aws:bedrock:{}::foundation-model/{}\".format(\n", "                region, foundation_model\n", "            ),\n", "            \"retrievalConfiguration\": {\n", "                \"vectorSearchConfiguration\": {\"numberOfResults\": 5}\n", "            },\n", "        },\n", "    },\n", ")\n", "\n", "print(response[\"output\"][\"text\"], end=\"\\n\" * 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Conclusion\n", "\n", "By following this guide, you can effectively harness the power of Amazon Bedrock’s features to build a robust Multimodal RAG application tailored to your specific needs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optional: Clean up and Delete Knowledge Base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Delete Knowledge Base\n", "knowledge_base.delete_kb(delete_s3_bucket=True, delete_iam_roles_and_policies=True)"]}], "metadata": {"kernelspec": {"display_name": "conda_python3", "language": "python", "name": "conda_python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 4}