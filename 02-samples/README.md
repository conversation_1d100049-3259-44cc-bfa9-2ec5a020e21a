# Strands Agent Samples



| Example ID | Agent                                                               | Features showcased                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| ---------- | ------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1          | [Restaurant Assistant](./01-restaurant-assistant/)                  | A restaurant assistant to streamline and enhance customer interactions. The AI assistant enables customers to seamlessly reserve tables at multiple partner restaurants, inquire about menus, create new bookings, retrieve details of existing reservations, or cancel bookings-all via natural, conversational interfaces (web, mobile, or voice). This automation improves customer satisfaction, reduces staff workload, and increases booking efficiency, while providing restaurants with valuable insights into customer preferences and reservation trends. |
| 2          | [Scrum Master Assistant](./02-scrum-master-assistant/)              | An expert agile development assistant for JIRA specializing in breaking down meeting notes into actionable tasks.                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| 3          | [AWS Assistant MCP](./03-aws-assistant-mcp/)                        | AWS-focused assistant using Model Control Protocol (MCP)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| 4          | [Startup Advisor MCP](./04-startup-advisor-mcp/)                    | Startup advisory agent using Model Control Protocol (MCP)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 5          | [Personal Assistant (multi-agent system)](./05-personal-assistant/) | A comprehensive productivity assistant that integrates calendar management, web-search via Perplexity MCP Server, and development assistance through an intelligent multi-agent architecture.                                                                                                                                                                                                                                                                                                                                                                       |
| 6          | [Code Assistant](./06-code-assistant/)                              | The Code Assistant Agent is not just another tool—it's your personal AI-powered coding companion designed to supercharge your development workflow.                  
| 7          | [Fintech Assistant (WhatsApp Integration)](./07-whatsapp-fintech-sample/) | Fintech assistant bot on WhatsApp that coordinates customer support with tools. It loads daily promotions, based on the day of the week. Another agent is responsible for handle credit card operations, being able to load last X days of fake transactions and schedule card payment.                   |
| 8          | [Data Warehouse Optimizer (SQLite)](./07-data-warehouse-optimizer/) | A multi-agent system simulating a data warehouse query optimizer using SQLite. It includes Analyzer, Rewriter, and Validator agents collaborating to analyze SQL query plans (`EXPLAIN QUERY PLAN`), suggest optimizations, and validate improvements. Powered by Claude 3 Haiku on AWS Bedrock and logs observability with OpenTelemetry. Produces a final JSON report with analysis and validation results.                                                                                                                    |
| 9          | [Finance-Assistant Swarm Agent Collaboration](./09-finance-assistant-swarm-agent/) | Finance-Assistant Swarm Agent Collaboration is a modular, multi-agent system designed to autonomously generate comprehensive equity research reports from a single stock query. Built using the Strands SDK and powered by Amazon Bedrock, this assistant orchestrates a collaborative swarm of specialized agents—each responsible for a distinct financial research task including ticker resolution, company profiling, price analytics, financial health assessment, and sentiment analysis.                   |
| 10         | [Email Assistant with RAG and Image Generation](./10-multi-modal-email-assistant-agent/) | Multi-modal email assistant demonstrates the power of agent collaboration for enterprise communication, offering a scalable framework for automating professional content creation in domains such as marketing, reporting, and customer engagement.             |        
| 12        | [Medical Document Processing Assistant](./12-medical-document-processing-assistant/) | The Medical Document Processing Assistant is an AI-powered tool designed to extract, analyze, and enrich medical information from various document formats such as PDFs and images. This assistant specializes in processing clinical notes, pathology reports, discharge summaries, and other medical documents to provide structured data with standardized medical coding.             |
| 13        | [AWS infrastructure audit assistant](./13-aws-audit-assistant/) | AWS Audit Assistant is your AI-powered partner for ensuring AWS resource compliance with best practices. It provides intelligent insights and recommendations for security and efficiency improvements.     