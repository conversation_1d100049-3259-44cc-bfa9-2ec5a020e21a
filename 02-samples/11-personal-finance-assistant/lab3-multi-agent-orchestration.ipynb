{"cells": [{"cell_type": "markdown", "id": "e561c7c7", "metadata": {}, "source": ["> ⚠️ **EDUCATIONAL PURPOSE ONLY**: This lab demonstrates multi-agent coordination patterns using portfolio analysis as an example. This is NOT financial advice and should not be used for actual investment decisions. All portfolio analysis is for educational demonstration of AI agent coordination only.\n"]}, {"cell_type": "markdown", "id": "cell-2", "metadata": {}, "source": ["\n", "# Lab 3: Multi-Agent Portfolio Orchestrator\n", "\n", "## 📋 Lab Overview\n", "\n", "### What You'll Build\n", "In this hands-on lab, you'll create a sophisticated **Multi-Agent Portfolio Orchestrator** that demonstrates advanced AI coordination patterns using the Strands framework. This system combines multiple specialized AI agents to analyze market data, create investment strategies, and enable human oversight.\n", "\n", "## 🎯 Learning Objectives\n", "- Understand multi-agent coordination using Strands \"agents as tools\" pattern  \n", "- Implement human-in-the-loop decision making\n", "\n", "\n", "## 🏗️ Architecture Overview\n", "\n", "This system demonstrates a **Multi-Agent Portfolio Orchestrator** that:\n", "1. **Analyzes** historical market data to create portfolio strategies\n", "2. **Recommends** optimal portfolios using AI analysis  \n", "3. **Enables** human decision-making with natural language overrides\n", "\n", "### Agent Architecture\n", "The system consists of:\n", "- **Portfolio Orchestrator**: Coordinates all specialist agents\n", "- **6 Specialist Agents**: Each with focused responsibilities\n", "    - **Stock Data Agent** - Fetches and caches market data with dual CSV storage\n", "    - **Growth Strategy Agent** - Creates high-return, growth-focused portfolios  \n", "    - **Diversified Strategy Agent** - Builds balanced, risk-managed portfolios\n", "    - **Performance Calculator Agent** - Converts abstract returns into concrete $1000 projections\n", "    - **Visualization Agent** - Creates professional charts and comparisons\n", "    - **Validation Agent** - Tests strategies against actual market performance\n", "- **Human-in-the-Loop**: Allow for human intervention, return of control to the human and compare with choices made by the agent.\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "workflow-step-1", "metadata": {}, "source": ["---\n", "\n", "## 🔄 Step 1: Environment Setup\n", "\n", "Setting up the Strands framework, AWS Bedrock integration, and importing utility functions that handle the complex portfolio logic. This step prepares all the tools our multi-agent system will need."]}, {"cell_type": "code", "execution_count": null, "id": "e73b8ccd", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:11.950873Z", "iopub.status.busy": "2025-07-15T23:36:11.950544Z", "iopub.status.idle": "2025-07-15T23:36:13.607242Z", "shell.execute_reply": "2025-07-15T23:36:13.606531Z", "shell.execute_reply.started": "2025-07-15T23:36:11.950852Z"}}, "outputs": [], "source": ["# Install Strands using pip\n", "\n", "!pip install -q strands-agents strands-agents-tools matplotlib yfinance"]}, {"cell_type": "code", "execution_count": null, "id": "a30792da", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:16.041282Z", "iopub.status.busy": "2025-07-15T23:36:16.040934Z", "iopub.status.idle": "2025-07-15T23:36:16.045130Z", "shell.execute_reply": "2025-07-15T23:36:16.044620Z", "shell.execute_reply.started": "2025-07-15T23:36:16.041256Z"}}, "outputs": [], "source": ["# Import Strands Agents SDK\n", "from strands import Agent, tool\n", "from strands.models import BedrockModel\n", "\n", "# Import standard libraries\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "from typing import Dict, List, Any\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": null, "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:17.225643Z", "iopub.status.busy": "2025-07-15T23:36:17.225295Z", "iopub.status.idle": "2025-07-15T23:36:17.248427Z", "shell.execute_reply": "2025-07-15T23:36:17.247876Z", "shell.execute_reply.started": "2025-07-15T23:36:17.225619Z"}}, "outputs": [], "source": ["# Import utility functions from utils.py\n", "import sys\n", "import os\n", "import importlib\n", "sys.path.append(os.getcwd())\n", "\n", "# Import and reload the utils module to get latest changes\n", "import utils\n", "importlib.reload(utils)\n", "\n", "# Import all the functions from utils\n", "from utils import (\n", "    load_comprehensive_stock_data_from_csv,\n", "    load_simple_stock_data_from_csv,\n", "    get_stock_data,\n", "    get_stock_analysis,\n", "    create_growth_portfolio,\n", "    create_diversified_portfolio,\n", "    calculate_portfolio_performance,\n", "    visualize_portfolio_allocation,\n", "    visualize_performance_comparison,\n", "    validate_portfolio_performance,\n", "    compare_analysis_accuracy,\n", "    calculate_accuracy_metrics,\n", "    validation_agent,\n", "    # Import safe callback handler (based on Strands samples)\n", "    simple_multi_agent_tracker\n", ")\n", "\n", "print(\"✅ All utility functions imported successfully from utils.py!\")\n", "print(\"🔧 Safe multi-agent tracker imported (with error handling)!\")"]}, {"cell_type": "code", "execution_count": null, "id": "c79fb6bb", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:17.920821Z", "iopub.status.busy": "2025-07-15T23:36:17.920254Z", "iopub.status.idle": "2025-07-15T23:36:17.979055Z", "shell.execute_reply": "2025-07-15T23:36:17.978553Z", "shell.execute_reply.started": "2025-07-15T23:36:17.920802Z"}}, "outputs": [], "source": ["# Configure Claude 3.7 Sonnet model\n", "model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\"\n", ")\n", "\n", "print(\"✅ Claude 3.7 Sonnet configured for multi-agent coordination\")\n"]}, {"cell_type": "markdown", "id": "workflow-step-2", "metadata": {}, "source": ["---\n", "\n", "## 🔄 Step 2: Agent Creation\n", "\n", "Creating 6 specialized agents, each with focused responsibilities. Notice how each agent uses tools from utils.py to keep the code clean and educational. Each agent has a specific role in the portfolio analysis workflow."]}, {"cell_type": "markdown", "id": "qq0wss8r6fm", "metadata": {}, "source": ["### 📊 Stock Data Agent\n", "**Dual-purpose market specialist** - Uses `get_stock_data()` (daily prices + summary) or `get_stock_analysis()` (summary only) based on needs, with intelligent CSV caching for both formats."]}, {"cell_type": "code", "execution_count": null, "id": "b2cb9276", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:25.911598Z", "iopub.status.busy": "2025-07-15T23:36:25.911041Z", "iopub.status.idle": "2025-07-15T23:36:25.917199Z", "shell.execute_reply": "2025-07-15T23:36:25.916714Z", "shell.execute_reply.started": "2025-07-15T23:36:25.911575Z"}}, "outputs": [], "source": ["# Import Stock Data Tools from utils.py\n", "print(\"🔧 Importing Stock Data Tools from utils.py\")\n", "print(\"=\" * 50)\n", "\n", "# Import the complex functions from utils\n", "from utils import get_stock_data, get_stock_analysis\n", "\n", "# Stock Data Agent using both tools\n", "@tool\n", "def stock_data_agent(query: str) -> str:\n", "    \"\"\"Stock data specialist - chooses appropriate function based on user query\"\"\"\n", "    agent = Agent(\n", "        model=model,\n", "        tools=[get_stock_data, get_stock_analysis],\n", "        system_prompt=\"\"\"You are a Stock Data Specialist with two distinct tools:\n", "        \n", "        - get_stock_data() - Comprehensive: daily prices + summary metrics (annual_return, volatility)\n", "        - get_stock_analysis() - Simple: summary metrics only (return_pct, volatility_pct)\n", "        \n", "        Choose based on needs:\n", "        - Use get_stock_data() when daily price data is needed\n", "        - Use get_stock_analysis() for quick portfolio analysis\n", "        \n", "        Both automatically use CSV caching. Present analysis clearly showing what data was used.\n", "        Keep it simple and educational.\"\"\"\n", "    )\n", "    return str(agent(query))\n", "\n", "print(\"✅ Stock data functions imported from utils.py:\")\n", "print(\"  🛠️ get_stock_data() - Daily prices + summary (annual_return, volatility)\")\n", "print(\"  📊 get_stock_analysis() - Summary only (return_pct, volatility_pct)\")\n", "print(\"  📈 stock_data_agent() - Chooses appropriate tool\")\n"]}, {"cell_type": "markdown", "id": "2fae5244", "metadata": {}, "source": ["### 🚀 Growth Strategy Agent\n", "**High-growth portfolio specialist** - Uses `create_growth_portfolio()` to build portfolios focused on maximum return potential with higher risk tolerance.\n"]}, {"cell_type": "code", "execution_count": null, "id": "d002b96b", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:37.745995Z", "iopub.status.busy": "2025-07-15T23:36:37.745677Z", "iopub.status.idle": "2025-07-15T23:36:37.751533Z", "shell.execute_reply": "2025-07-15T23:36:37.750893Z", "shell.execute_reply.started": "2025-07-15T23:36:37.745975Z"}}, "outputs": [], "source": ["# Create Growth Strategy Agent (Portfolio Creation Logic in utils.py)\n", "print(\"🚀 Creating Growth Strategy Agent\")\n", "print(\"=\" * 40)\n", "\n", "# Streamlined Growth Strategy Agent - business logic is in utils.py\n", "@tool\n", "def growth_strategy_agent(query: str) -> str:\n", "    \"\"\"Streamlined growth strategy specialist - uses portfolio creation functions from utils.py\"\"\"\n", "    agent = Agent(\n", "        model=model,\n", "        tools=[create_growth_portfolio],\n", "        system_prompt=\"\"\"You are a Growth Strategy Specialist focused on high-growth portfolios.\n", "\n", "        Your approach:\n", "        1. Use create_growth_portfolio() with sophisticated allocation methods\n", "        2. Choose allocation method based on strategy:\n", "           - \"performance_weighted\" - Weight by relative returns (default, more aggressive)\n", "           - \"risk_adjusted\" - Weight by risk-adjusted returns (more balanced growth)\n", "           - \"equal_weight\" - Simple equal allocation (basic approach)\n", "        3. Present clear analysis and reasoning for growth-focused selections\n", "        \n", "        Available tool:\n", "        - create_growth_portfolio(allocation_method=\"performance_weighted\") - Creates growth portfolios with smart allocation\n", "        \n", "        Advanced usage:\n", "        - Use allocation_method parameter to control weighting strategy\n", "        - \"performance_weighted\" gives higher allocations to higher-return stocks\n", "        - \"risk_adjusted\" balances returns with volatility for smarter growth\n", "        \n", "        Format your final recommendation as:\n", "        PORTFOLIO: {allocation dictionary with varied percentages}\n", "        STRATEGY: Growth ({allocation_method})\n", "        EXPECTED RETURN: X%\n", "        RISK LEVEL: High/Moderate\n", "        ALLOCATION METHOD: {chosen method and reasoning}\n", "        \n", "        Focus on creating dynamic allocations, not just 25% equal weights.\"\"\"\n", "    )\n", "    return str(agent(query))\n", "\n", "print(\"✅ Growth Strategy Agent created!\")\n"]}, {"cell_type": "markdown", "id": "rs65yg1til", "metadata": {}, "source": ["### ⚖️ Diversified Strategy Agent\n", "**Balanced portfolio specialist** - Uses `create_diversified_portfolio()` and risk-adjusted metrics to build sector-diversified portfolios with moderate risk."]}, {"cell_type": "code", "execution_count": null, "id": "731y6t3l7ir", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:40.939833Z", "iopub.status.busy": "2025-07-15T23:36:40.939503Z", "iopub.status.idle": "2025-07-15T23:36:40.945014Z", "shell.execute_reply": "2025-07-15T23:36:40.944486Z", "shell.execute_reply.started": "2025-07-15T23:36:40.939811Z"}}, "outputs": [], "source": ["# Create Diversified Strategy Agent (Portfolio Creation Logic in utils.py)\n", "print(\"⚖️ Creating Diversified Strategy Agent\")\n", "print(\"=\" * 45)\n", "\n", "# Streamlined Diversified Strategy Agent - business logic is in utils.py\n", "@tool\n", "def diversified_strategy_agent(query: str) -> str:\n", "    \"\"\"Streamlined diversified strategy specialist - uses portfolio creation functions from utils.py\"\"\"\n", "    agent = Agent(\n", "        model=model,\n", "        tools=[create_diversified_portfolio],\n", "        system_prompt=\"\"\"You are a Diversified Strategy Specialist focused on balanced portfolios.\n", "\n", "        Your approach:\n", "        1. Use create_diversified_portfolio() to build risk-balanced portfolios\n", "        2. Focus on risk-adjusted returns and sector diversification\n", "        3. Present clear analysis and reasoning for balanced selections\n", "        \n", "        Available tool:\n", "        - create_diversified_portfolio() - Creates balanced portfolios (logic in utils.py)\n", "        \n", "        Format your final recommendation as:\n", "        PORTFOLIO: {allocation dictionary}\n", "        STRATEGY: Diversified\n", "        EXPECTED RETURN: X%\n", "        RISK LEVEL: Low/Moderate\n", "        SECTORS: X different sectors\n", "        \n", "        Keep it simple and educational - focus on the strategy, not the implementation.\"\"\"\n", "    )\n", "    return str(agent(query))\n", "\n", "print(\"✅ Diversified Strategy Agent created!\")\n"]}, {"cell_type": "markdown", "id": "9y2bmj4bfca", "metadata": {}, "source": ["### 💰 Performance Calculator Agent\n", "**Investment analysis specialist** - Uses `calculate_portfolio_performance()` to transform abstract returns into concrete $1000 investment projections and risk assessments."]}, {"cell_type": "code", "execution_count": null, "id": "uau90wl7xj", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:42.631057Z", "iopub.status.busy": "2025-07-15T23:36:42.630739Z", "iopub.status.idle": "2025-07-15T23:36:42.636638Z", "shell.execute_reply": "2025-07-15T23:36:42.636170Z", "shell.execute_reply.started": "2025-07-15T23:36:42.631035Z"}}, "outputs": [], "source": ["# Create Performance Calculator Agent (Calculation Logic in utils.py)\n", "print(\"💰 Creating Performance Calculator Agent\")\n", "print(\"=\" * 45)\n", "\n", "# Streamlined Performance Calculator Agent - business logic is in utils.py\n", "@tool\n", "def performance_calculator_agent(query: str) -> str:\n", "    \"\"\"Streamlined performance calculator specialist - uses calculation functions from utils.py\"\"\"\n", "    agent = Agent(\n", "        model=model,\n", "        tools=[calculate_portfolio_performance, create_growth_portfolio, create_diversified_portfolio],\n", "        system_prompt=\"\"\"You are a Performance Calculator Specialist focused on concrete investment analysis.\n", "\n", "        Your approach:\n", "        1. Use portfolio creation tools to get strategies for comparison\n", "        2. Use calculate_portfolio_performance() to analyze returns and make concrete projections\n", "        3. Present clear comparison between strategies with concrete $1000 projections\n", "        \n", "        Available tools:\n", "        - calculate_portfolio_performance() - Calculates performance metrics (logic in utils.py)\n", "        - create_growth_portfolio() - Get growth portfolio for analysis\n", "        - create_diversified_portfolio() - Get diversified portfolio for analysis\n", "        \n", "        Format your final summary as:\n", "        PERFORMANCE ANALYSIS:\n", "        Strategy 1: $X final value (Y% return, Z risk)\n", "        Strategy 2: $X final value (Y% return, Z risk)\n", "        \n", "        WINNER: [Strategy] - [Reasoning]\n", "        \n", "        Make abstract returns tangible through concrete dollar examples.\"\"\"\n", "    )\n", "    return str(agent(query))\n", "\n", "print(\"✅ Performance Calculator Agent created!\")\n"]}, {"cell_type": "markdown", "id": "72332f10", "metadata": {}, "source": ["### 🎨 Visualization Agent\n", "**Chart and visualization specialist** - Uses `visualize_portfolio_allocation()` and `visualize_performance_comparison()` to create professional charts that make portfolio data visually accessible and easy to interpret.\n"]}, {"cell_type": "code", "execution_count": null, "id": "dmus9e4vx8", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:36:44.005762Z", "iopub.status.busy": "2025-07-15T23:36:44.005447Z", "iopub.status.idle": "2025-07-15T23:36:44.011619Z", "shell.execute_reply": "2025-07-15T23:36:44.011090Z", "shell.execute_reply.started": "2025-07-15T23:36:44.005741Z"}}, "outputs": [], "source": ["# Create Visualization Agent (Visualization Logic in utils.py)\n", "print(\"🎨 Creating Visualization Agent\")\n", "print(\"=\" * 35)\n", "\n", "# Streamlined Visualization Agent - visualization logic is in utils.py\n", "@tool\n", "def visualization_agent(query: str) -> str:\n", "    \"\"\"Streamlined visualization specialist - uses visualization functions from utils.py\"\"\"\n", "    agent = Agent(\n", "        model=model,\n", "        tools=[visualize_portfolio_allocation, visualize_performance_comparison, create_growth_portfolio, create_diversified_portfolio],\n", "        system_prompt=\"\"\"You are a Visualization Specialist focused on creating clear, informative charts.\n", "\n", "        Your approach:\n", "        1. Use portfolio creation tools to get data for visualization\n", "        2. Use visualize_portfolio_allocation() for pie charts showing allocation breakdowns\n", "        3. Use visualize_performance_comparison() for bar charts showing performance metrics\n", "        4. Create clear, professional visualizations that enhance understanding\n", "        \n", "        Available tools:\n", "        - visualize_portfolio_allocation() - Creates pie charts (logic in utils.py)\n", "        - visualize_performance_comparison() - Creates bar charts (logic in utils.py)\n", "        - create_growth_portfolio() - Get growth portfolio data for visualization\n", "        - create_diversified_portfolio() - Get diversified portfolio data for visualization\n", "        \n", "        Focus on making data visually accessible and easy to interpret. Always provide context for what the charts show.\"\"\"\n", "    )\n", "    return str(agent(query))\n", "\n", "print(\"✅ Visualization Agent created!\")\n", "print(\"  🎯 Focused on multi-agent coordination pattern\")\n", "print(\"  📊 Visualization logic moved to utils.py\")\n", "print(\"  🔧 Much cleaner and more educational\")"]}, {"cell_type": "markdown", "id": "egrl01mxat9", "metadata": {}, "source": ["### 🔍 Validation Agent\n", "**Portfolio validation specialist Agent** - Uses `validate_portfolio_performance()`, `compare_analysis_accuracy()`, and `calculate_accuracy_metrics()` from utils.py to test portfolios against actual market data."]}, {"cell_type": "markdown", "id": "workflow-step-3", "metadata": {}, "source": ["---\n", "\n", "## 🔄 Step 3: Multi-Agent Coordination\n", "\n", "The Portfolio Orchestrator coordinates all specialist agents to analyze market data, create strategies, and provide visual comparisons. Watch how the master agent delegates tasks to specialists and synthesizes their outputs into a coherent recommendation."]}, {"cell_type": "markdown", "id": "9ce14c4a", "metadata": {}, "source": ["## 🚀 Multi-Agent Workflow Execution\n", "\n", "Let's run the complete multi-agent portfolio orchestration workflow!"]}, {"cell_type": "code", "execution_count": null, "id": "cell-7", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T23:38:02.701350Z", "iopub.status.busy": "2025-07-15T23:38:02.700898Z", "iopub.status.idle": "2025-07-15T23:43:40.901215Z", "shell.execute_reply": "2025-07-15T23:43:40.900597Z", "shell.execute_reply.started": "2025-07-15T23:38:02.701325Z"}}, "outputs": [], "source": ["# Execute the multi-agent workflow\n", "user_request = \"\"\"\n", "Create an optimal investment portfolio for me using your multi-agent system.\n", "\n", "I want you to:\n", "1. Analyze current market data using your specialist agents\n", "2. Create both growth and diversified portfolio strategies\n", "3. Show me the AI recommendation with visual comparisons\n", "4. Calculate concrete $1000 investment projections\n", "5. Allow me to review and potentially override the recommendation\n", "\n", "Please walk me through each step of the multi-agent coordination process.\n", "\"\"\"\n", "\n", "\n", "# Portfolio Orchestrator with all specialist agents as tools\n", "portfolio_orchestrator = Agent(\n", "    model=model,\n", "    system_prompt=\"\"\"\n", "    You are the Portfolio Orchestrator, the master agent coordinating specialist agents for portfolio creation.\n", "    \n", "    Your streamlined workflow:\n", "    1. Use stock_data_agent to ensure both CSV caches are populated with market data for year 2024\n", "    2. Use growth_strategy_agent to create high-growth portfolio\n", "    3. Use diversified_strategy_agent to create balanced portfolio  \n", "    4. Use performance_calculator_agent to calculate concrete $1000 investment projections\n", "    5. Use visualization_agent to create clear charts showing portfolio comparisons\n", "    6. Compare strategies and present clear recommendation with reasoning\n", "    7. For validation with validation_agent, use market data for year 2025 \n", "    8. Allow human review and potential override with natural language\n", "    \n", "    Available specialist agents:\n", "    - stock_data_agent: Ensures both comprehensive and simple data caches are available\n", "    - growth_strategy_agent: Creates high-growth portfolios using cached data\n", "    - diversified_strategy_agent: Creates balanced portfolios using cached data\n", "    - performance_calculator_agent: Calculates concrete investment returns and comparisons\n", "    - visualization_agent: Creates professional charts for portfolio analysis\n", "    \n", "    Key principles:\n", "    - All agents use efficient CSV caching for fast performance\n", "    - Present concrete $1000 investment examples to make returns tangible\n", "    - Use visualizations to enhance understanding of portfolio differences\n", "    - Provide clear reasoning for recommendations\n", "    - Allow human decision-making with natural language input\n", "    \n", "    \"\"\",    \n", "    tools=[\n", "        stock_data_agent,\n", "        growth_strategy_agent, \n", "        diversified_strategy_agent,\n", "        performance_calculator_agent,\n", "        visualization_agent,\n", "        validation_agent\n", "    ],\n", ")\n", "\n", "print(\"🎬 Starting Multi-Agent Portfolio Orchestration...\")\n", "print(\"=\" * 60)\n", "\n", "# Run the portfolio orchestrator\n", "result = portfolio_orchestrator(user_request)\n", "print(result)\n"]}, {"cell_type": "markdown", "id": "workflow-step-4", "metadata": {}, "source": ["---\n", "\n", "## 🔄 Step 4: Human-in-the-Loop Decision\n", "\n", "This is where you review the AI recommendation and either accept it or override with your own requirements using natural language. The system demonstrates transparent AI where humans retain final decision control."]}, {"cell_type": "markdown", "id": "cell-10", "metadata": {}, "source": ["## 👤 Human-in-the-Loop Decision Point\n", "\n", "This is where you can accept the AI recommendation or override with custom requirements."]}, {"cell_type": "code", "execution_count": null, "id": "cell-11", "metadata": {}, "outputs": [], "source": ["# Human decision point - customize this based on your preference\n", "human_decision = \"\"\"\n", "OPTION 1 - Accept AI Recommendation:\n", "\\\"Accept the AI recommendation\\\"\n", "\n", "OPTION 2 - Override with Custom Requirements (examples):\n", "\\\"Override: I want more technology focus\\\"\n", "\\\"Override: Make it more conservative\\\"\n", "\\\"Override: Add more dividend stocks\\\"\n", "\\\"Override: Reduce risk but keep some growth\\\"\n", "\n", "Choose your decision below:\n", "\"\"\"\n", "\n", "print(human_decision)\n", "\n", "# Uncomment and modify one of these lines based on your choice:\n", "# user_decision = \\\"Accept the AI recommendation\\\"\n", "# user_decision = \\\"Override: I want more technology focus\\\"\n", "# user_decision = \\\"Override: Make it more conservative\\\"\n", "\n", "user_decision = \"I want more technology focus\"  # Default for demo\n", "\n", "print(f\"\\n🎯 Your Decision: {user_decision}\")\n", "print(\"\\n📊 Processing your decision and creating final portfolio...\")\n", "\n", "final_result = portfolio_orchestrator(f\"\"\"\n", "The human has made their decision: \\\"{user_decision}\\\"\n", "\n", "Please:\n", "1. Process this decision appropriately\n", "2. Create or modify the portfolio based on their choice\n", "3. Use the validation agent to test against 2025 actual market data\n", "4. Create final visualizations showing the results\n", "5. Provide a summary of the complete multi-agent workflow\n", "\"\"\")\n", "\n", "print(final_result)"]}, {"cell_type": "markdown", "id": "cell-14", "metadata": {}, "source": ["## 🎓 Learning Summary\n", "\n", "### What We Learned\n", "\n", "1. **Multi-Agent Coordination**: How to coordinate specialist agents using the Strands \\\"agents as tools\\\" pattern\n", "\n", "2. **Human-in-the-Loop**: How to implement transparent AI recommendations with human oversight\n", "\n", "3. **Portfolio Analysis**: How to analyze historical market data to create investment strategies\n", "\n", "4. **Market Validation**: How to test analysis accuracy using actual market performance\n", "\n", "5. **Agent Specialization**: How each agent has focused responsibilities:\n", "   - Stock Data Agent: Market data and metrics\n", "   - Growth Strategy Agent: High-growth portfolios\n", "   - Diversified Strategy Agent: Balanced portfolios\n", "   - Performance Agent: Concrete $1000 projections\n", "   - Visualization Agent: Charts and comparisons\n", "   - Validation Agent: Market reality testing\n", "\n", "### Key Insights\n", "\n", "- **Historical Analysis ≠ Future Performance**: Our validation shows the limitations of historical analysis\n", "- **Human Judgment Matters**: AI provides intelligence, but humans retain decision control\n", "- **Transparency is Critical**: Users see AI reasoning before making decisions\n", "- **Concrete Examples Help**: $1000 projections make abstract returns tangible\n", "- **Agent Coordination Works**: Complex workflows through intelligent agent coordination\n", "\n", "This demonstrates responsible AI deployment where AI provides intelligence while humans retain control over final decisions."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}