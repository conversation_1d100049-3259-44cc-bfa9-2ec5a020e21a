{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Lab 1: Strands Agents Basics (10 minutes)\n", "\n", "In this lab, you will create a basic Agent, create tools that the Agent can use and integrate Amazon Bedrock Guardrails to add safety filters to the LLM outputs and inputs to the Agent. You will develop a budget analysis agent that can aid you in personal finance.\n", "\n", "## Learning Objectives - AWS Workshop\n", "\n", "**You'll Learn These Core Strands Patterns:**\n", "- ✅ Create your first Strands agent: `Agent(model=BedrockModel(...))`\n", "- ✅ Build custom tools: `@tool def my_function()`\n", "- ✅ Make agents use tools automatically\n", "- ✅ Create reusable visualization tools\n", "- ✅ Add safety filters by integrating Amazon Bedrock Guardrails\n"]}, {"cell_type": "markdown", "metadata": {"execution": {"iopub.execute_input": "2025-07-15T15:28:09.898427Z", "iopub.status.busy": "2025-07-15T15:28:09.898122Z", "iopub.status.idle": "2025-07-15T15:28:09.902428Z", "shell.execute_reply": "2025-07-15T15:28:09.901751Z", "shell.execute_reply.started": "2025-07-15T15:28:09.898404Z"}}, "source": ["> ⚠️ **EDUCATIONAL PURPOSE ONLY**: This lab demonstrates budget analysis and there are conversations with the agent about how to save and budget money. This is NOT financial advice and the soundness of the agent outputs should be verified against expert advice. All budget analysis is for educational demonstration of AI agents only."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Install and Import Strands\n", "\n", "**<PERSON>**: Import and configure"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install Strands using pip\n", "\n", "!pip install -q strands-agents strands-agents-tools matplotlib pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from strands import Agent, tool\n", "from strands.models import BedrockModel\n", "\n", "# Supporting libraries for our budget agent\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"🚀 Strands Agents SDK imported successfully!\")\n", "print(\"📊 AWS Workshop: Ready to build your first agent!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\"\n", "    # This connects to your AWS Bedrock service\n", ")\n", "\n", "print(\"✅ AWS Bedrock model configured for Strands!\")\n", "print(\"🎯 Ready to create your first agent\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Create Your First Strands Agent\n", "\n", "**Core <PERSON>s <PERSON>**: `Agent(model=model)`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["basic_agent = Agent(model=model)\n", "\n", "# Test your first Strands agent\n", "response = basic_agent(\"What is the 50/30/20 budgeting rule?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Add System Prompt for Specialization\n", "\n", "**Core Strands <PERSON>tern**: `Agent(model=model, system_prompt=\"...\")`\n", "\n", "The system prompt acts as a guiding framework to the LLM for interpreting user queries, structuring responses and helps the agent to maintain a consistent tone and style."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["financial_agent = Agent(\n", "    model=model,\n", "    system_prompt= \"\"\" You are a helpful personal finance assistant. You provide general strategies \n", "    to creating budgets, tips on finacial discipline to acheive financial milestones and analyse\n", "    financial trends. You do not provide any investment advice.\n", "    \n", "    Keep responses concise and actionable.\n", "    Always provide 2-3 specific steps the user can take.\n", "    Focus on practical budgeting and spending advice.\n", "    \"\"\"\n", ")\n", "\n", "# Test the specialized agent\n", "print(\"💰 Specialized Financial Agent Response:\")\n", "\n", "# TASK: Modify the user query and test the Agent\n", "user_query = \"I spend $800/month on dining out. Is this too much for someone making $5000/month?\"\n", "response = financial_agent(user_query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🎯 Learning Milestone: How is this specialised agent different from one without a system prompt? Does it always provide concrete steps and avoid providing investment advice? "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Create Custom Tools\n", "\n", "**Core Strands Pattern**: `@tool` decorator for custom functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@tool\n", "def calculate_budget(monthly_income: float) -> str:\n", "    \"\"\"Calculate 50/30/20 budget breakdown.\"\"\"\n", "    needs = monthly_income * 0.50\n", "    wants = monthly_income * 0.30  \n", "    savings = monthly_income * 0.20\n", "    return f\"💰 Budget for ${monthly_income:,.0f}/month:\\n• Needs: ${needs:,.0f} (50%)\\n• Wants: ${wants:,.0f} (30%)\\n• Savings: ${savings:,.0f} (20%)\"\n", "\n", "@tool\n", "def create_financial_chart(data_dict: dict, chart_title: str = \"Financial Chart\") -> str:\n", "    \"\"\"Universal chart creator\"\"\"\n", "    if not data_dict:\n", "        return \"❌ No data provided for chart\"\n", "    \n", "    # Create pie chart\n", "    labels = list(data_dict.keys())\n", "    values = list(data_dict.values())\n", "    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3']\n", "    \n", "    plt.figure(figsize=(8, 6))\n", "    plt.pie(values, labels=labels, autopct='%1.1f%%', colors=colors[:len(values)], startangle=90)\n", "    plt.title(f'📊 {chart_title}', fontsize=14, fontweight='bold')\n", "    plt.axis('equal')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return f\"✅ {chart_title} visualization created!\"\n", "\n", "@tool\n", "def generate_sample_data() -> str:\n", "    \"\"\"Generate sample financial data.\"\"\"\n", "    # Create sample spending data\n", "    spending_data = {\n", "        'Groceries': 400,\n", "        'Dining': 300, \n", "        'Transportation': 200,\n", "        'Entertainment': 150,\n", "        'Utilities': 250,\n", "        'Shopping': 200\n", "    }\n", "    \n", "    # Save for other labs to use\n", "    global sample_spending_data\n", "    sample_spending_data = spending_data\n", "    \n", "    # Create user profile\n", "    user_profile = {\n", "        \"monthly_income\": 5000,\n", "        \"budget_goal\": 1000,\n", "        \"focus_areas\": [\"dining\", \"shopping\"],\n", "        \"created_date\": datetime.now().isoformat()\n", "    }\n", "    \n", "    with open('user_profile.json', 'w') as f:\n", "        json.dump(user_profile, f, indent=2)\n", "    \n", "    total_spending = sum(spending_data.values())\n", "    return f\"✅ Sample data created! Total monthly spending: ${total_spending:,}\"\n", "\n", "print(\"🛠️ Custom Strands tools created!\")\n", "print(\"🎯 Key Pattern: @tool decorator makes any function available to agents\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Create Agent with <PERSON>ls\n", "\n", "**Core Strands Pattern**: `Agent(model=model, tools=[tool1, tool2], system_prompt=\"...\")`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["budget_agent = Agent(\n", "    model=model,\n", "    tools=[calculate_budget, create_financial_chart, generate_sample_data],\n", "    system_prompt=\"\"\"\n", "    You are a helpful personal finance assistant. You provide general strategies \n", "    to creating budgets, tips on finacial discipline to acheive financial milestones and analyse\n", "    financial trends. You do not provide any investment advice.\n", "    \n", "    Keep responses concise and actionable.\n", "    Always provide 2-3 specific steps the user can take.\n", "    Focus on practical budgeting and spending advice.\n", "\n", "    Your capabilities:\n", "    - Calculate 50/30/20 budgets using calculate_budget tool\n", "    - Create visual charts using create_financial_chart tool  \n", "    - Generate sample data using generate_sample_data tool\n", "\n", "    Always be concise and use tools when appropriate.\n", "    Provide visual output whenever possible.\n", "    \"\"\"\n", ")\n", "\n", "print(\"🎯 Budget Agent with Tools Ready!\")\n", "print(\"✨ This agent can automatically choose which tools to use!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Test Your Strands Agent with <PERSON>ls\n", "\n", "Watch your agent automatically choose and use tools!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 7: Test Autonomous Tool Selection\n", "print(\"🧪 Testing: Agent automatically chooses tools based on request\")\n", "print(\"=\"*60)\n", "\n", "# Test 1: Budget calculation\n", "print(\"🤖 Agent Response - Budget Request:\")\n", "\n", "sample_query = \"I make $6000 per month. Can you create a budget breakdown for me?\"\n", "response1 = budget_agent(sample_query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test 2: Data generation + visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 3: Create spending analysis chart\n", "\n", "print(\"\\n🤖 Agent Response - Chart Request:\")\n", "response3 = budget_agent(\"Can you create a pie chart showing my spending breakdown by category?\")"]}, {"cell_type": "markdown", "metadata": {"execution": {"iopub.execute_input": "2025-07-09T19:21:29.474157Z", "iopub.status.busy": "2025-07-09T19:21:29.473630Z", "iopub.status.idle": "2025-07-09T19:21:29.477315Z", "shell.execute_reply": "2025-07-09T19:21:29.476615Z", "shell.execute_reply.started": "2025-07-09T19:21:29.474132Z"}}, "source": ["## Safety - Layer Amazon Bedrock Guardrail as safeguards\n", "A LLM is guided by the system prompt but need not always align with the instructions in the system prompt. To ensure user safety from LLM based risks such as hallucinations, it is important to add additional safeguards like guardrails, and consider architectural patterns like Retrieval Augmented Generation to introduce contextual data and human in the loop to verify responses. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ⚠️ Test the budget agent to see if you breaks \"out of character\" and provides investment advice although we have instructed it not to do so. You can engage in multi-turn conversation and to break out of the chat, type \"exit\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ⚠️ If you use the query below and engage in multi-turn conversations with the budget_agent that you created, you might be able to \n", "# get the model to generate stock investment advice, which may be risky for the user. ⚠️\n", "\n", "# sample_query = budget_agent(\"How should I invest my savings to make it future proof?\")\n", "\n", "# Start conversation\n", "while True:\n", "    user_input = input(\"\\nYou: \")\n", "    \n", "    if user_input.lower() == \"exit\":\n", "        print(\"Goodbye!\")\n", "        break\n", "    \n", "    # Process the user input and get a response\n", "    response = budget_agent(user_input)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### To prevent the agent from providing stock investment advice in specific stocks, let's create safety filters in Amazon Bedrock Guardrails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure AWS clients\n", "import boto3\n", "bedrock_client = boto3.client('bedrock')\n", "bedrock_runtime = boto3.client('bedrock-runtime')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can now use the `create_guardrail` method to create the no-stock-investment-advice guardrail that will be used in our application. In this example, our guardrail will include a denied-topic filter for \"Stock Investment Advice\" that will block our agent from providing any advice on investing in specific stocks. We will also create content policy to filter innapropriated content and a word policy to detect specific pre-defined words."]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["# Create a Bedrock guardrail\n", "response = bedrock_client.create_guardrail(\n", "    name='guardrail-no-investment-advice',\n", "    description='Prevents the model from providing stock investment advice.',\n", "    topicPolicyConfig={\n", "        'topicsConfig': [\n", "            {\n", "                'name': 'Stock Investment Advice',\n", "                'definition': 'Providing personalized advice or recommendations on investing in financial instruments such as stocks or trusts in a fiduciary capacity or assuming related obligations and liabilities.',\n", "                'examples': [\n", "                    'What stocks should I invest in for my retirement?',\n", "                    'Is it a good idea to put my money in a mutual fund?',\n", "                    'How should I allocate my 401(k) investments?',\n", "                    'What type of trust fund should I set up for my children?',\n", "                    'Which stocks have the highest returns?'\n", "                ],\n", "                'type': 'DENY'\n", "            }\n", "        ]\n", "    },\n", "    contentPolicyConfig={\n", "        'filtersConfig': [\n", "            {\n", "                'type': 'SEXUAL',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'VIOLENCE',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'HATE',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'INSULTS',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'MISCONDUCT',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'PROMPT_ATTACK',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'NONE'\n", "            }\n", "        ]\n", "    },\n", "    wordPolicyConfig={\n", "        'wordsConfig': [\n", "            {'text': 'fiduciary advice'},\n", "            {'text': 'investment recommendations'},\n", "            {'text': 'stock picks'},\n", "            {'text': 'financial planning guidance'},\n", "            {'text': 'portfolio allocation advice'},\n", "            {'text': 'retirement fund suggestions'},\n", "            {'text': 'trust fund setup'},\n", "            {'text': 'investment strategy'},\n", "            {'text': 'financial advisor recommendations'}\n", "        ],\n", "        'managedWordListsConfig': [\n", "            {\n", "                'type': 'PROFANITY'\n", "            }\n", "        ]\n", "    },\n", "    blockedInputMessaging='I apologize, but I am not able to provide fiduciary advice. It is best to consult with trusted finance specialists to learn how to invest your money',\n", "    blockedOutputsMessaging='I apologize, but I am not able to provide fiduciary advice. For your privacy and security, please modify your input and try again without including financial, or restricted details.',\n", ")\n", "\n", "# Print the response to get the guardrail ID\n", "print(\"Guardrail ID:\", response.get('guardrailId'))\n", "print(\"Guardrail ARN:\", response.get('guardrailArn'))\n", "\n", "# Store the guardrail ID for later use"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["guardrail_id = response.get('guardrailId')\n", "guardrail_version = \"DRAFT\"  # Initial version is always 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Test the Guardrail directly\n", "# Test function to check if input/output is blocked by guardrail\n", "def test_guardrail(text, source_type='INPUT'):\n", "      response = bedrock_runtime.apply_guardrail(\n", "          guardrailIdentifier=guardrail_id,\n", "          guardrailVersion=guardrail_version,\n", "          source=source_type,  # can be 'INPUT' or 'OUTPUT'\n", "          content=[{\"text\": {\"text\": text}}]\n", "      )\n", "\n", "      # New response format uses different fields\n", "      print(f\"Action: {response.get('action')}\")\n", "      print(f\"Action Reason: {response.get('actionReason', 'None')}\")\n", "\n", "      # Check if content was blocked\n", "      is_blocked = response.get('action') == 'GUARDRAIL_INTERVENED'\n", "      print(f\"Content {source_type} blocked: {is_blocked}\")\n", "\n", "      if is_blocked:\n", "          # Print topic policies that were triggered\n", "          assessments = response.get('assessments', [])\n", "          if assessments and 'topicPolicy' in assessments[0]:\n", "              print(\"Blocked topics:\", [topic.get('name') for topic in\n", "  assessments[0]['topicPolicy'].get('topics', [])\n", "                                       if topic.get('action') == 'BLOCKED'])\n", "\n", "          # Print the modified output if available\n", "          if 'outputs' in response and response['outputs']:\n", "              print(\"Modified content:\", response['outputs'][0].get('text', 'None'))\n", "\n", "      return response\n", "\n", "# Test some safe input\n", "print(\" 🧪 Testing safe input 🧪:\")\n", "test_guardrail(\"Tell me about general financial literacy concepts.\")\n", "\n", "# Test input that should be blocked\n", "print(\"\\n ⚠️ Testing input that should be blocked ⚠️:\")\n", "test_guardrail(\"What stocks should I invest in for my retirement?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we confirmed the guardrail is working as expected, let's integrate Amazon Bedrock Guardrail with a Strands Agent. This is done via the Bedrock Model object, by setting the `guardrail_id`, `guardrail_version` and `guardrail_trace`. Once the model object is created you can use it to create your agent. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a Bedrock model with guardrail configuration\n", "bedrock_model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    guardrail_id=guardrail_id,\n", "    guardrail_version=guardrail_version,\n", "    # Enable trace info for debugging\n", "    guardrail_trace=\"enabled\"\n", ")\n", "\n", "# Create agent with the guardrail-protected model\n", "agent_with_guardrail = Agent(\n", "    model=bedrock_model,\n", "    tools=[calculate_budget, create_financial_chart, generate_sample_data],\n", "    system_prompt=\"\"\"\n", "        You are a helpful personal finance assistant. You provide general strategies \n", "    to creating budgets, tips on finacial discipline to acheive financial milestones and analyse\n", "    financial trends. You do not provide any investment advice.\n", "    \n", "    Keep responses concise and actionable.\n", "    Always provide 2-3 specific steps the user can take.\n", "    Focus on practical budgeting and spending advice.\n", "\n", "    Your capabilities:\n", "    - Calculate 50/30/20 budgets using calculate_budget tool\n", "    - Create visual charts using create_financial_chart tool  \n", "    - Generate sample data using generate_sample_data tool\n", "\n", "    Always be concise and use tools when appropriate.\n", "    Provide visual output whenever possible.\n", "    \"\"\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ⚠️ Test the budget agent to test the guardrail that blocks any investment advice related to specific stocks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Testing the Agent with Guardrail\n", "Sample_query =  \"I earn $3500. How should I set up a monthly savings and how should I invest these savings?\"\n", " \n", "# Process the user input and get a response\n", "response = agent_with_guardrail(Sample_query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Lab 1 Complete - AWS Workshop\n", "\n", "### 🎉 What You've Mastered:\n", "\n", "✅ **Core Strands Patterns:**\n", "- `Agent(model=BedrockModel(...))` - Basic agent creation\n", "- `@tool def function()` - Custom tool development  \n", "- `Agent(model=model, tools=[...], system_prompt=\"...\")` - Full agent setup\n", "- Autonomous tool selection by agents\n", "- Integrate Amazon Bedrock Guardrails to add safety filters\n", "\n", "✅ **Key Skills Gained:**\n", "- Created AWS Bedrock + Strands integration\n", "- Integrated Amazon Bedrock Guardrails to add safety filters to the agent\n", "- Made agents that automatically choose appropriate tools\n", "\n", "**🎯 You now understand the core building blocks of Strands Agents!**\n", "\n", "### 🚀 Next: Lab 2 - Memory Integration\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}