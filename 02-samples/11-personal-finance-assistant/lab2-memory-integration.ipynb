{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Lab 2: Memory Integration (15 minutes)\n", "In this Lab, you will look at core concepts such as conversation history, agent state and request state which help you understand short term memory of agents, to start building tailored agents. You will also learn how to integrate mem0.io for long term memory and store user preferences and maintain context in longer multi-turn conversations. \n", "\n", "## Learning Objectives\n", "- ✅ Understand short-term built-in Strands Agents memory\n", "- ✅ Integrate long-term memory using mem0.io\n", "- ✅ Store user preferences and reuse them to customize agent responses"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> ⚠️ **EDUCATIONAL PURPOSE ONLY**: This lab demonstrates budget analysis and there are conversations with the agent about how to save and budget money. This is NOT financial advice and the soundness of the agent outputs should be verified against expert advice. All budget analysis is for educational demonstration of AI agents only."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install Strands using pip\n", "\n", "!pip install -q strands-agents strands-agents-tools mcp\n", "\n", "from strands import Agent, tool\n", "from strands.models import BedrockModel\n", "import pandas as pd\n", "import json\n", "from datetime import datetime\n", "\n", "print(\"📚 Loading Strands SDK...\")\n", "\n", "# Configure AWS Bedrock model (same as Lab 1)\n", "model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Understanding State Management and Agent Memory\n", "\n", "Understanding how state works in Strands is essential for building agents that can maintain context across multi-turn interactions and workflows\n", "\n", "### Short Term Memory\n", "**Strands Agents state** is maintained in several forms:\n", "\n", "1. Conversation History: The sequence of messages between the user and the agent.\n", "\n", "<PERSON><PERSON> uses a conversation manager to handle conversation history effectively. The default is the `SlidingWindowConversationManager`, which keeps recent messages and removes older ones when needed\n", "   \n", "2. Agent State: Stateful information outside of conversation context such as temporary variables and state, maintained across multiple requests.\n", "\n", "   \n", "3. Request State: Contextual information maintained within a single request.\n", "\n", "Conversation history, agent state and request state form the short term memory of the Agent, which helps the LLM with immediate context to maintain the conversation.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"execution": {"iopub.execute_input": "2025-07-10T20:06:24.817628Z", "iopub.status.busy": "2025-07-10T20:06:24.816906Z", "iopub.status.idle": "2025-07-10T20:06:24.821032Z", "shell.execute_reply": "2025-07-10T20:06:24.820104Z", "shell.execute_reply.started": "2025-07-10T20:06:24.817603Z"}}, "source": ["#### 2.1 Short Term memory: Conversation History"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from strands.agent.conversation_manager import SlidingWindowConversationManager\n", "\n", "# Create a conversation manager with custom window size\n", "# By default, SlidingWindowConversationManager is used even if not specified\n", "conversation_manager = SlidingWindowConversationManager(\n", "    window_size=10,  # Maximum number of message pairs to keep\n", ")\n", "\n", "# Use the conversation manager with your agent\n", "memory_demo_agent = Agent(\n", "    model=model,\n", "    system_prompt=\"You are a financial assistant. Keep responses concise.\",\n", "    conversation_manager=conversation_manager\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📝 Test the conversational history of the Agent. Does the Agent retain context it learnt from earlier messages after message 10?\n", "\n", "⚠️ You can engage in multi-turn conversation and to break out of the chat, type \"exit\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["# Example prompt: \"I want to save $800 per month and focus on reducing my dining expenses\"\n", "\n", "# Interactive loop\n", "while True:\n", "    try:\n", "        user_input = input(\"\\n> \")\n", "\n", "        if user_input.lower() == \"exit\":\n", "            print(\"\\nGoodbye! 👋\")\n", "            break\n", "\n", "        # Call the memory agent\n", "        memory_demo_agent(user_input)\n", "        print(f\"\\n📝 Memory now: {len(memory_demo_agent.messages)} messages\")\n", "\n", "    except KeyboardInterrupt:\n", "        print(\"\\n\\nExecution interrupted. Exiting...\")\n", "        break\n", "    except Exception as e:\n", "        print(f\"\\nAn error occurred: {str(e)}\")\n", "        print(\"Please try a different request.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### The `agent.messages` list contains all user and assistant messages, including tool calls and tool results. This is the primary way to inspect what's happening in your agent's conversation and check it's short term memory."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["memory_demo_agent.messages"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.2 Short Term memory: Agent State\n", "\n", "Agent state provides key-value storage for stateful information that exists outside of the conversation context. Unlike conversation history, agent state is not passed to the model during inference but can be accessed and modified by tools and application logic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create an agent with initial state\n", "memory_demo_agent = Agent(\n", "    model=model,\n", "    system_prompt=\"You are a financial assistant. Keep responses concise.\",\n", "    state={\"user_preferences\": {\"saving_goal\": \"40%\", \"spending_goal\":\"20%\", \"fixed_expenses\":\"40%\"}},\n", "    conversation_manager=conversation_manager\n", ")\n", "\n", "# Access state values\n", "user_finance_goals = memory_demo_agent.state.get(\"user_preferences\")\n", "print(user_finance_goals) \n", "\n", "# Set new state values\n", "memory_demo_agent.state.set(\"session_count\", 0)\n", "\n", "# Get entire state\n", "all_state = memory_demo_agent.state.get()\n", "print(all_state)  # All state data as a dictionary\n", "\n", "# Delete state values\n", "memory_demo_agent.state.delete(\"last_action\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["🎯 **By storing information in the state, you can reuse it while designing tools for agents to customise the agent behavior for users**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.3 Short Term memory: Request State\n", "\n", "Each agent interaction maintains a request state dictionary that persists throughout the event loop cycles and is not included in the agent's context.\n", "\n", "The request state:\n", "\n", "- Is initialized at the beginning of each agent call\n", "- Persists through recursive event loop cycles\n", "- Can be modified by callback handlers\n", "- Is returned in the AgentResult object"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def custom_callback_handler(**kwargs):\n", "    # Access request state\n", "    if \"request_state\" in kwargs:\n", "        state = kwargs[\"request_state\"]\n", "        # Use or modify state as needed\n", "        if \"counter\" not in state:\n", "            state[\"counter\"] = 0\n", "        state[\"counter\"] += 1\n", "        print(f\"Callback handler event count: {state['counter']}\")\n", "\n", "memory_demo_agent = Agent(\n", "    model=model,\n", "    system_prompt=\"You are a financial assistant. Keep responses concise.\",\n", "    state={\"user_preferences\": {\"saving_goal\": \"40%\", \"spending_goal\":\"20%\", \"fixed_expenses\":\"40%\"}},\n", "    conversation_manager=conversation_manager,\n", "    callback_handler=custom_callback_handler\n", ")\n", "\n", "result = memory_demo_agent(\"Hi there!\")\n", "\n", "print(result.state)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Long Term Memory: Personalized Context Through Persistent Memory\n", "\n", "Now, let's integrate mem0.io and create a memory agent that can store user preferences and hold memories.\n", "\n", "**Memory Backend Options**\n", "The Mem0 Memory Tool supports three different backend configurations:\n", "\n", "1. OpenSearch (Recommended for AWS environments):\n", "\n", "- Requires AWS credentials and OpenSearch configuration\n", "- Set OPENSEARCH_HOST and optionally AWS_REGION (defaults to us-west-2)\n", "\n", "2. FAISS (Default for local development):\n", "\n", "- Uses FAISS as the local vector store backend\n", "- Requires faiss-cpu package for local vector storage\n", "- No additional configuration needed\n", "\n", "3. Mem0 Platform:\n", "\n", "- Uses the Mem0 Platform API for memory management\n", "- Requires a Mem0 API key : MEM0_API_KEY in the environment variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# For CPU version\n", "!pip install -q faiss-cpu mem0ai opensearch-py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import logging\n", "from dotenv import load_dotenv\n", "\n", "from strands import Agent\n", "from strands_tools import mem0_memory, use_llm\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "# Load environment variables from .env file if it exists\n", "load_dotenv()\n", "\n", "# We are using the default FAISS vector store for this demo\n", "USER_ID = \"mem0_user\""]}, {"cell_type": "markdown", "metadata": {"execution": {"iopub.execute_input": "2025-07-10T19:39:29.432144Z", "iopub.status.busy": "2025-07-10T19:39:29.431360Z", "iopub.status.idle": "2025-07-10T19:39:29.436597Z", "shell.execute_reply": "2025-07-10T19:39:29.435650Z", "shell.execute_reply.started": "2025-07-10T19:39:29.432115Z"}}, "source": ["### Tool Overview of Memory Agent\n", "The memory agent utilizes two primary tools:\n", "\n", "1. memory: Enables storing and retrieving information with capabilities for:\n", "\n", "- Storing user-specific information persistently\n", "- Retrieving memories based on semantic relevance\n", "- Listing all stored memories for a user\n", "- Setting relevance thresholds and result limits\n", "\n", "2. use_llm: Provides language model capabilities for:\n", "\n", "- Generating conversational responses based on retrieved memories\n", "- Creating natural, contextual answers using memory context"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# System prompt for the memory agent\n", "MEMORY_SYSTEM_PROMPT = f\"\"\"You are a personal finance assistant that maintains context by remembering user details.\n", "\n", "Capabilities:\n", "- Store new information using mem0_memory tool (action=\"store\")\n", "- Retrieve relevant memories (action=\"retrieve\")\n", "- List all memories (action=\"list\")\n", "- Provide personalized responses\n", "\n", "Key Rules:\n", "- Always include user_id={USER_ID} in tool calls\n", "- Be conversational and natural in responses\n", "- Format output clearly\n", "- Acknowledge stored information\n", "- Only share relevant information\n", "- Politely indicate when information is unavailable\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create an agent with memory capabilities\n", "memory_agent = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    system_prompt=MEMORY_SYSTEM_PROMPT,\n", "    tools=[mem0_memory, use_llm]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize some user preferences\n", "def initialize_user_preferences():\n", "    \"\"\"Initialize user preferences.\"\"\"\n", "    \n", "    content = \"\"\"My name is <PERSON>. I prefer to have a monthly budget of 40% for fixed expenses, 30% for wants and 30% saved. \n", "    I am planning a trip to South Korea next spring and would like to dedicate some portion from the savings to a vacation budget which over \n", "    12 months should amount to $4000. \n", "    My favourite hobbies are visiting new restaurants and look for discounts, \n", "    new openings which help me visit more restaurants within my budget.\"\"\"  # noqa\n", "    memory_agent.tool.mem0_memory(action=\"store\", content=content, user_id=USER_ID)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test the memory agent\n", "- Check if the Agent provides responses with <PERSON>'s preferences\n", "- If you input 'demo', the user preferences for <PERSON> will be initialised in the memory\n", "- To end the multi-turn conversation with the Agent, type 'exit'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ⚠️ You can engage in multi-turn conversation and to break out of the chat, type \"exit\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive loop\n", "\n", "# Sample_prompts: \"I got a promotion and my monthly salary has changed to $4700 - \n", "# How does it affect my vacation savings for the South Korea trip?\"\n", "\n", "# Sample_prompt: \"My wife earns $5000 and we have the same goals as a family. How early can we save for South Korea trip?\"\n", "while True:\n", "    try:\n", "        user_input = input(\"\\n> \")\n", "\n", "        if user_input.lower() == \"exit\":\n", "            print(\"\\nGoodbye! 👋\")\n", "            break\n", "        elif user_input.lower() == \"demo\":\n", "            initialize_user_preferences()\n", "            print(\"\\nUser preferences for <PERSON> initialized!\")\n", "            continue\n", "\n", "        # Call the memory agent\n", "        memory_agent(user_input)\n", "\n", "    except KeyboardInterrupt:\n", "        print(\"\\n\\nExecution interrupted. Exiting...\")\n", "        break\n", "    except Exception as e:\n", "        print(f\"\\nAn error occurred: {str(e)}\")\n", "        print(\"Please try a different request.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "### What You've Learned:\n", "- ✅ **Agent Short-Term Memory**: Conversartion History, agent state and request state\n", "- ✅ **mem0.io Integration**: Strands memory enabled agent class for storing user preferences\n", "- ✅ **Memory Based capabilities**: `mem0_memory` tool stores, retrieves and lists memories\n", "\n", "\n", "### Key Takeaways:\n", "1. Strands agents remembers messages through the conversation history\n", "2. Agent state provides key-value storage for stateful information to be stored\n", "3. Request state dictionary persists throughout the event loop cycles - Separate from agent context\n", "4. Tools can access stored user preferences and context from historic messages\n", "5. Memory makes agents truly personalized\n", "\n", "\n", "### Next: Lab 3 - Multi-Agent Teams\n", "Create multiple agents that work together!"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}