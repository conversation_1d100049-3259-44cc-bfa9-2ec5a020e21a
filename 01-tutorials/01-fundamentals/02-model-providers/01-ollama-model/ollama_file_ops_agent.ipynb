{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Building a Local Agent with Strands Agents and Ollama Model\n", "\n", "This notebook demonstrates how to create a personal agent using Strands Agent and Ollama. The agent will be capable of performing various local tasks such as file operations, web searches, and system commands.\n", "\n", "## What is <PERSON><PERSON><PERSON>?\n", "\n", "[Ollama](https://ollama.com/) is an open-source framework that allows you to run large language models (LLMs) locally on your machine. It provides a simple API for interacting with these models, making it ideal for privacy-focused applications where you don't want to send data to external services.\n", "\n", "Key benefits of Ollama:\n", "- **Privacy**: All processing happens locally on your machine\n", "- **No API costs**: Free to use as much as you want\n", "- **Offline capability**: Works without internet connection\n", "- **Customization**: Can be fine-tuned for specific use \n", "\n", "\n", "## Agent Details\n", "\n", "<div style=\"float: left; margin-right: 20px;\">\n", "    \n", "|Feature             |Description                                        |\n", "|--------------------|---------------------------------------------------|\n", "|Feature used        |Ollama Model - to create a file operations agent   |\n", "|Agent Structure     |Single agent architecture                          |\n", "\n", "\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Agent Architecture\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"images/architecture.png\" width=\"65%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Installation\n", "\n", "Before running this notebook, make sure you have:\n", "\n", "1. Install Ollama: Follow instructions at [https://ollama.com/download](https://ollama.com/download)\n", "2. Start the Ollama server: `ollama serve`\n", "3. Downloaded a model with Ollama: `ollama pull llama3.2:1b`\n", "\n", "Refer to the [documentation](https://cuddly-sniffle-lrmk2y7.pages.github.io/0.1.x/user-guide/concepts/model-providers/ollama/) for detailed instructions.\n", "\n", "In this notebook, we will download Ollama for the linux distribution for compatibility with SageMaker Studio. This is done for code execution during AWS lead workshops on Workshop Studio. If you are running this code locally, you should adjust the steps to download Ollama to your current enviroment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# this will work on linux computers\n", "!curl -fsSL https://ollama.com/install.sh | sh"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import subprocess\n", "subprocess.<PERSON><PERSON>(['ollama', 'serve'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["!ollama pull llama3.2:1b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "\n", "import requests\n", "\n", "# Import strands components\n", "from strands import Agent, tool\n", "from strands.models.ollama import OllamaModel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if <PERSON><PERSON><PERSON> is running:\n", "try:\n", "    response = requests.get(\"http://localhost:11434/api/tags\")\n", "    print(\"✅ Ollama is running. Available models:\")\n", "    for model in response.json().get(\"models\", []):\n", "        print(f\"- {model['name']}\")\n", "except requests.exceptions.ConnectionError:\n", "    print(\"❌ <PERSON><PERSON><PERSON> is not running. Please start <PERSON><PERSON><PERSON> before proceeding.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining Custom Tools\n", "\n", "Tools are functions that the agent can use to interact with the environment. Below, we define several useful tools for our personal agent."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# File Operation Tools\n", "\n", "\n", "@tool\n", "def file_read(file_path: str) -> str:\n", "    \"\"\"Read a file and return its content.\n", "\n", "    Args:\n", "        file_path (str): Path to the file to read\n", "\n", "    Returns:\n", "        str: Content of the file\n", "\n", "    Raises:\n", "        FileNotFoundError: If the file doesn't exist\n", "    \"\"\"\n", "    try:\n", "        with open(file_path, \"r\") as file:\n", "            return file.read()\n", "    except FileNotFoundError:\n", "        return f\"Error: File '{file_path}' not found.\"\n", "    except Exception as e:\n", "        return f\"Error reading file: {str(e)}\"\n", "\n", "\n", "@tool\n", "def file_write(file_path: str, content: str) -> str:\n", "    \"\"\"Write content to a file.\n", "\n", "    Args:\n", "        file_path (str): The path to the file\n", "        content (str): The content to write to the file\n", "\n", "    Returns:\n", "        str: A message indicating success or failure\n", "    \"\"\"\n", "    try:\n", "        # Create directory if it doesn't exist\n", "        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)\n", "\n", "        with open(file_path, \"w\") as file:\n", "            file.write(content)\n", "        return f\"File '{file_path}' written successfully.\"\n", "    except Exception as e:\n", "        return f\"Error writing to file: {str(e)}\"\n", "\n", "\n", "@tool\n", "def list_directory(directory_path: str = \".\") -> str:\n", "    \"\"\"List files and directories in the specified path.\n", "\n", "    Args:\n", "        directory_path (str): Path to the directory to list\n", "\n", "    Returns:\n", "        str: A formatted string listing all files and directories\n", "    \"\"\"\n", "    try:\n", "        items = os.listdir(directory_path)\n", "        files = []\n", "        directories = []\n", "\n", "        for item in items:\n", "            full_path = os.path.join(directory_path, item)\n", "            if os.path.isdir(full_path):\n", "                directories.append(f\"Folder: {item}/\")\n", "            else:\n", "                files.append(f\"File: {item}\")\n", "\n", "        result = f\"Contents of {os.path.abspath(directory_path)}:\\n\"\n", "        result += (\n", "            \"\\nDirectories:\\n\" + \"\\n\".join(sorted(directories))\n", "            if directories\n", "            else \"\\nNo directories found.\"\n", "        )\n", "        result += (\n", "            \"\\n\\nFiles:\\n\" + \"\\n\".join(sorted(files)) if files else \"\\nNo files found.\"\n", "        )\n", "\n", "        return result\n", "    except Exception as e:\n", "        return f\"Error listing directory: {str(e)}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the Ollama-powered Agent\n", "\n", "Now we'll create our agent using the Ollama model and the tools we defined above.\n", "\n", "Note: You can add more tools like `execute_commands`, `search_files` etc."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define a comprehensive system prompt for our agent\n", "system_prompt = \"\"\"\n", "You are a helpful personal assistant capable of performing local file actions and simple tasks for the user.\n", "\n", "Your key capabilities:\n", "1. Read, understand, and summarize files.\n", "2. Create and write to files.\n", "3. List directory contents and provide information on the files.\n", "4. Summarize text content\n", "\n", "When using tools:\n", "- Always verify file paths before operations\n", "- Be careful with system commands\n", "- Provide clear explanations of what you're doing\n", "- If a task cannot be completed, explain why and suggest alternatives\n", "\n", "Always be helpful, concise, and focus on addressing the user's needs efficiently.\n", "\"\"\"\n", "\n", "model_id = (\n", "    \"llama3.2:1b\"  # You can change this to any model you have pulled with <PERSON><PERSON><PERSON>.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Configure the Ollama model\n", "Make sure your Ollama service is running at http://localhost:11434 and your `model_id` is in the list of Ollama models printed above."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ollama_model = OllamaModel(\n", "    model_id=model_id,\n", "    host=\"http://localhost:11434\",\n", "    params={\n", "        \"max_tokens\": 4096,  # Adjust based on your model's capabilities\n", "        \"temperature\": 0.7,  # Lower for more deterministic responses, higher for more creative\n", "        \"top_p\": 0.9,  # Nucleus sampling parameter\n", "        \"stream\": True,  # Enable streaming responses\n", "    },\n", ")\n", "\n", "# Create the agent\n", "local_agent = Agent(\n", "    system_prompt=system_prompt,\n", "    model=ollama_model,\n", "    tools=[file_read, file_write, list_directory],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Testing the Agent\n", "\n", "Let's test our agent with some example tasks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_agent(\n", "    \"Read the file in the path `sample_file/Amazon-com-Inc-2023-Shareholder-Letter.pdf` and summarize it in 5 bullet points.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 2: List files in the current directory\n", "response = local_agent(\"Show me the files in the current directory\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 3: Create a sample file\n", "response = local_agent(\n", "    \"Create a file called 'sample.txt' with the content 'This is a test file created by my Ollama agent.'\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 4: create a readme file after reading and understanding multiple files\n", "response = local_agent(\"Create a readme.md for the current directory\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've created a local personal agent using Stands and Ollama. The agent can perform file operations (read, write, append) and Summarize/Analyze text\n", "\n", "This demonstrates the power of running AI models locally with Ollama, combined with the flexibility of strands' tool system. You can extend this agent by adding more tools or using different Ollama models based on your needs.\n", "\n", "### Next Steps (Ideas)\n", "\n", "- Try different Ollama models to see how they affect the agent's capabilities\n", "- Add more complex tools like web search, email sending, or calendar integration\n", "- Implement memory for the agent to remember past interactions\n", "- Create a simple UI for interacting with your agent"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}