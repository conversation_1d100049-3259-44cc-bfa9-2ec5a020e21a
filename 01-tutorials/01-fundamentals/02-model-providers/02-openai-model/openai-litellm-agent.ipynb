{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%% md\n"}}, "source": ["# Using OpenAI models with Strands Agent\n", "\n", "## Overview\n", "\n", "Strands Agents is an SDK that takes a model-driven approach to building and running AI agents in just a few lines of code. Strands supports multiple providers and models hosted anywhere.\n", "\n", "[LiteLLM](https://docs.litellm.ai/docs/) is a unified interface for various LLM providers that allows you to interact with models from Amazon, Anthropic, OpenAI, and many others through a single API. The Strands Agent SDK implements a LiteLLM provider, allowing you to run agents against any model LiteLLM supports.\n", "\n", "In this example, we will show you how to use `gpt-4.1-mini` model hosted in Microsoft Azure as the underlying model in your Strands Agent. We will use a simple agent use case with a weather and a get time tool.\n", "\n", "\n", "## Agent Details\n", "<div style=\"float: left; margin-right: 20px;\">\n", "    \n", "|Feature             |Description                                        |\n", "|--------------------|---------------------------------------------------|\n", "|Feature used        |LiteLLM model                                      |\n", "|Agent Structure     |Single agent architecture                          |\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Architecture\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"images/architecture.png\" width=\"65%\" />\n", "</div>\n", "\n", "## Key Features\n", "* **LiteLLM model**: using a model provided via LiteLLM"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and prerequisites\n", "\n", "### Prerequisites\n", "* Python 3.10+\n", "* Azure Account\n", "* gpt-4.1-mini access\n", "\n", "Let's now install the requirement packages for our Strands Agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# installing pre-requisites\n", "!pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing dependency packages\n", "\n", "Now let's import the dependency packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from datetime import datetime\n", "from datetime import timezone as tz\n", "from typing import Any\n", "from zoneinfo import ZoneInfo\n", "\n", "from strands import Agent, tool\n", "from strands.models.litellm import LiteLLMModel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting up Azure keys\n", "\n", "Let's now setup the Azure API Keys"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"AZURE_API_KEY\"] = \"<YOUR_API_KEY>\"\n", "os.environ[\"AZURE_API_BASE\"] = \"<YOUR_API_BASE>\"\n", "os.environ[\"AZURE_API_VERSION\"] = \"<YOUR_API_VERSION>\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting up custom tools\n", "\n", "Let's now setup two dummy tools to test our agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@tool\n", "def current_time(timezone: str = \"UTC\") -> str:\n", "    if timezone.upper() == \"UTC\":\n", "        timezone_obj: Any = tz.utc\n", "    else:\n", "        timezone_obj = ZoneInfo(timezone)\n", "\n", "    return datetime.now(timezone_obj).isoformat()\n", "\n", "\n", "@tool\n", "def current_weather(city: str) -> str:\n", "    # Dummy implementation. Please replace with actual weather API call.\n", "    return \"sunny\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining agent underlying LLM model\n", "\n", "Next let's define our agent underlying model using LiteLLM. We will set it to `gpt-4.1-mini`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = \"azure/gpt-4.1-mini\"\n", "litellm_model = LiteLLMModel(\n", "    model_id=model, params={\"max_tokens\": 32000, \"temperature\": 0.7}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining Agent\n", "\n", "Now that we have all the required information available, let's define our agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are a simple agent that can tell the time and the weather\"\n", "agent = Agent(\n", "    model=litellm_model,\n", "    system_prompt=system_prompt,\n", "    tools=[current_time, current_weather],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Testing agent\n", "\n", "Let's now invoke the agent to test it"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = agent(\"What time is it in Seattle? And how is the weather?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Analysing the agent's results\n", "\n", "Nice! We've invoked our agent for the first time! Let's now explore the results object. First thing we can see is the messages being exchanged by the agent in the agent's object"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent.messages"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we can take a look at the usage of our agent for the last query by analysing the result `metrics`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results.metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Congratulations!\n", "\n", "In this notebook you learned how to use LiteLLM with OpeanAi serving answers for weather agent."]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}