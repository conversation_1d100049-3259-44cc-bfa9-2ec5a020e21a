{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "# Safeguarding your Strands Agents with Amazon Bedrock Guardrails\n", "\n", "## Overview\n", "In this example we will guide you through how to create your first Strands Agent using [Amazon Bedrock Guardrails](https://aws.amazon.com/bedrock/guardrails/) to safeguard the underlying Amazon Bedrock LLM model.\n", "\n", "Amazon Bedrock Guardrails provides configurable safeguards to help safely build generative AI applications at scale. With a consistent and standard approach used across a wide range of foundation models (FMs) including FMs supported in Amazon Bedrock, fine-tuned models, and models hosted outside of Amazon Bedrock, Guardrails delivers industry-leading safety protections. \n", "\n", "With Strands Agents, you can add Amazon Bedrock Guardrails directly to you Amazon Bedrock models. If you are not using Amazon Bedrock Models, you can use the [Apply Guardrail API](https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-use-independent-api.html) to safeguard any model. In this case, you need to build the pipelines using the advance processing capabilities you just learned.\n", "\n", "\n", "## Agent Details\n", "<div style=\"float: left; margin-right: 20px;\">\n", "    \n", "|Feature             |Description                                        |\n", "|--------------------|---------------------------------------------------|\n", "|AWS Services used   |Amazon Bedrock Guardrails                          |\n", "|Custom tools created|get_customer_profile, list_customer_purchases, list_customer_tickets, update_customer_profile|\n", "|Agent Structure     |Single agent architecture                          |\n", "\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Architecture\n", "\n", "<div style=\"text-align:left\">\n", "    <img src=\"images/architecture.png\" width=\"85%\" />\n", "</div>\n", "\n", "## Key Features\n", "* **Single agent architecture**: this example creates a single agent that interacts with built-in and custom tools\n", "* **Custom tools**: lean how to create your own tools\n", "* **Amazon Bedrock Model as underlying LLM**: Used Anthropic Claude 3.7 from Amazon Bedrock as the underlying LLM model\n", "* **Amazon Bedrock Guardails as undeline safeguard**: Use Amazon Bedrock Guardrails to safe guard your agent application"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and prerequisites\n", "\n", "### Prerequisites\n", "* Python 3.10+\n", "* AWS account\n", "* Anthropic Claude 3.7 enabled on Amazon Bedrock\n", "* IAM role with permissions to create Amazon Bedrock Guardrails\n", "\n", "Let's now install the requirement packages for our Strands Agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -U -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing dependency packages\n", "\n", "Now let's import the dependency packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import boto3\n", "import os\n", "from strands import Agent, tool\n", "from strands.models import BedrockModel\n", "from customer_profile_tools import get_customer_profile, list_customer_purchases, list_customer_tickets, update_customer_profile\n", "from customer_profiles import CustomerProfileManager, generate_synthetic_profiles"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generating data\n", "\n", "Let's generate some synthetic data for this example. In reality, your agent would connect to an existing customer database, but we will use a JSON file for this example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" # Generate synthetic profiles if needed\n", "profile_manager = CustomerProfileManager()\n", "if not os.path.exists(\"customer_profiles.json\"):\n", "    print(\"Generating synthetic customer profiles\")\n", "    profiles = generate_synthetic_profiles(10)\n", "    print(f\"Generated {len(profiles)} synthetic customer profiles\")\n", "else:\n", "    print(\"Using existing customer profiles\")\n", "    profiles = list(profile_manager.profiles.values())\n", "    print(f\"Loaded {len(profiles)} customer profiles\")\n", "\n", "# Display sample profile for reference\n", "if profiles:\n", "    sample_profile = profiles[0]\n", "    print(f\"\\nSample Profile: {sample_profile.name}\")\n", "    print(f\"Email: {sample_profile.email}\")\n", "    print(f\"Customer ID: {sample_profile.customer_id}\")\n", "    print(f\"Location: {sample_profile.state}, {sample_profile.country}\")\n", "    print(f\"Purchases: {len(sample_profile.purchase_history)}\")\n", "    print(f\"Support Tickets: {len(sample_profile.support_tickets)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating an Amazon Bedrock Guardrail\n", "\n", "Let's now create an Amazon Bedrock Guardrail to prevent providing financial advice and filter for common challenges in a customer facing application. \n", "\n", "To create the guardrail, first we need to configure the Amazon Bedrock client:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure AWS clients\n", "bedrock_client = boto3.client('bedrock')\n", "bedrock_runtime = boto3.client('bedrock-runtime')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can now use the `create_guardrail` method to create the `no-investment-advice` guardrail that will be used in our application. In this example, our guardrail will include a topic deny for `Fiduciary Advice` that will block our agent to provide any fiduciary advice. The guardrail will also contain some basic content policy to filter innapropriated content and a word policy configuration to detect specific pre-defined words."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a Bedrock guardrail\n", "response = bedrock_client.create_guardrail(\n", "    name='no-investment-advice',\n", "    description='Prevents the model from providing fiduciary advice.',\n", "    topicPolicyConfig={\n", "        'topicsConfig': [\n", "            {\n", "                'name': 'Fiduciary Advice',\n", "                'definition': 'Providing personalized advice or recommendations on managing financial assets, investments, or trusts in a fiduciary capacity or assuming related obligations and liabilities.',\n", "                'examples': [\n", "                    'What stocks should I invest in for my retirement?',\n", "                    'Is it a good idea to put my money in a mutual fund?',\n", "                    'How should I allocate my 401(k) investments?',\n", "                    'What type of trust fund should I set up for my children?',\n", "                    'Should I hire a financial advisor to manage my investments?'\n", "                ],\n", "                'type': 'DENY'\n", "            }\n", "        ]\n", "    },\n", "    contentPolicyConfig={\n", "        'filtersConfig': [\n", "            {\n", "                'type': 'SEXUAL',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'VIOLENCE',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'HATE',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'INSULTS',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'MISCONDUCT',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'HIGH'\n", "            },\n", "            {\n", "                'type': 'PROMPT_ATTACK',\n", "                'inputStrength': 'HIGH',\n", "                'outputStrength': 'NONE'\n", "            }\n", "        ]\n", "    },\n", "    wordPolicyConfig={\n", "        'wordsConfig': [\n", "            {'text': 'fiduciary advice'},\n", "            {'text': 'investment recommendations'},\n", "            {'text': 'stock picks'},\n", "            {'text': 'financial planning guidance'},\n", "            {'text': 'portfolio allocation advice'},\n", "            {'text': 'retirement fund suggestions'},\n", "            {'text': 'wealth management tips'},\n", "            {'text': 'trust fund setup'},\n", "            {'text': 'investment strategy'},\n", "            {'text': 'financial advisor recommendations'}\n", "        ],\n", "        'managedWordListsConfig': [\n", "            {\n", "                'type': 'PROFANITY'\n", "            }\n", "        ]\n", "    },\n", "    blockedInputMessaging='I apologize, but I am not able to provide fiduciary advice. For your privacy and security, please modify your input and try again without including any financial, or restricted details.',\n", "    blockedOutputsMessaging='I apologize, but I am not able to provide fiduciary advice. For your privacy and security, please modify your input and try again without including financial, or restricted details.',\n", ")\n", "\n", "# Print the response to get the guardrail ID\n", "print(\"Guardrail ID:\", response.get('guardrailId'))\n", "print(\"Guardrail ARN:\", response.get('guardrailArn'))\n", "\n", "# Store the guardrail ID for later use\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["guardrail_id = response.get('guardrailId')\n", "guardrail_version = \"DRAFT\"  # Initial version is always 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Testing the guardrail directly\n", "\n", "To verify that the guardrail works as expected, we will create the `test_guardrail` support function. this function will use the `apply_guardrail` method to safeguard the input text and provide information about any actions taken by the guardrail "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test function to check if input/output is blocked by guardrail\n", "def test_guardrail(text, source_type='INPUT'):\n", "      response = bedrock_runtime.apply_guardrail(\n", "          guardrailIdentifier=guardrail_id,\n", "          guardrailVersion=guardrail_version,\n", "          source=source_type,  # can be 'INPUT' or 'OUTPUT'\n", "          content=[{\"text\": {\"text\": text}}]\n", "      )\n", "\n", "      # New response format uses different fields\n", "      print(f\"Action: {response.get('action')}\")\n", "      print(f\"Action Reason: {response.get('actionReason', 'None')}\")\n", "\n", "      # Check if content was blocked\n", "      is_blocked = response.get('action') == 'GUARDRAIL_INTERVENED'\n", "      print(f\"Content {source_type} blocked: {is_blocked}\")\n", "\n", "      if is_blocked:\n", "          # Print topic policies that were triggered\n", "          assessments = response.get('assessments', [])\n", "          if assessments and 'topicPolicy' in assessments[0]:\n", "              print(\"Blocked topics:\", [topic.get('name') for topic in\n", "  assessments[0]['topicPolicy'].get('topics', [])\n", "                                       if topic.get('action') == 'BLOCKED'])\n", "\n", "          # Print the modified output if available\n", "          if 'outputs' in response and response['outputs']:\n", "              print(\"Modified content:\", response['outputs'][0].get('text', 'None'))\n", "\n", "      return response\n", "\n", "# Test some safe input\n", "print(\"Testing safe input:\")\n", "test_guardrail(\"Tell me about general financial literacy concepts.\")\n", "\n", "# Test input that should be blocked\n", "print(\"\\nTesting input that should be blocked:\")\n", "test_guardrail(\"What stocks should I invest in for my retirement?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Integrating with Strands Agent\n", "\n", "Now that we confirmed the guardrail is working as expected, let's integrate it the Amazon Bedrock Guardrail with a Strands Agent. This is done via the Bedrock Model object, by setting the `guardrail_id`, `guardrail_version` and `guardrail_trace`. Once the model object is created you can use it to create your agent. We will use a couple of custom tools in this agent: `get_customer_profile`, `list_customer_purchases`, `list_customer_tickets`, `update_customer_profile`. To see their implementation check the `customer_profile_tools.py` file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["guardrail_id, guardrail_version"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a Bedrock model with guardrail configuration\n", "bedrock_model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    guardrail_id=guardrail_id,\n", "    guardrail_version=guardrail_version,\n", "    # Enable trace info for debugging\n", "    guardrail_trace=\"enabled\"\n", ")\n", "\n", "# Create agent with the guardrail-protected model\n", "agent = Agent(\n", "    system_prompt=\"You are a helpful assistant that provides customer support for retail products.\",\n", "    model=bedrock_model,\n", "    tools=[\n", "        get_customer_profile,\n", "        list_customer_purchases,\n", "        list_customer_tickets,\n", "        update_customer_profile\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Testing the Strands Agent with Guardrails\n", "\n", "Let's test our agent with both safe and risky inputs. To do so we will process the agent's response and check if the `stop_reason` is due to a guardrail intervention."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Helper function to test the agent and check for guardrail interventions\n", "def test_agent_with_guardrail(prompt):\n", "    print(f\"\\nUser: {prompt}\")\n", "\n", "    # Get agent response\n", "    response = agent(prompt)\n", "\n", "    # Check for guardrail intervention\n", "    if hasattr(response, 'stop_reason') and response.stop_reason == \"guardrail_intervened\":\n", "        print(\"\\n ⚠️ GUARDRAIL INTERVENED!\")\n", "        #print(f\"Response: {response}\")\n", "    else:\n", "        return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with a safe question \n", "test_agent_with_guardrail(\n", "    \" what is my latest purchase my Customer ID is CUST100?\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with a question that asks about investment recommendation, or hate or violence\n", "test_agent_with_guardrail(\n", "    \"My SSN is ***********. Can you help me understand how to protect my retirement account?\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspecting Conversation History\n", "\n", "Let's examine the conversation history to see how guardrails affected it:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print the conversation history\n", "print(f\"Conversation history: {json.dumps(agent.messages, indent=4)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Integrating Bedrock guardrails to a chatbot \n", "\n", "Amazon Bedrock provides a built-in guardrails framework that integrates directly with Strands Agents SDK. If a guardrail is triggered, the Strands Agents SDK will automatically overwrite the users input in conversation history. This is done so that follow-up questions are not also blocked by the same questions. This can be configured with the guardrail_redact_input boolean, and the guardrail_redact_input_message string to chage the overwrite message. Additionally, the same functionality is built for the model's output, but this is disabled by default. You can enable this with the guardrail_redact_output boolean, and change the overwrite message with the guardrail_redact_output_message string. Below is an example of how to leverage Bedrock guardrails in your code:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bedrock_model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    guardrail_id=guardrail_id,         # Your Bedrock guardrail ID\n", "    guardrail_version=guardrail_version,             # Guardrail version\n", "    guardrail_trace=\"enabled\",\n", "    guardrail_redact_output = True,        \n", "    guardrail_redact_input = True  ,\n", "    guardrail_redact_input_message = \"Guardrail Intervened and Redacted\"     \n", ")\n", "\n", "# Create agent with the guardrail-protected model\n", "agent = Agent(\n", "    system_prompt=\"You are a helpful assistant that provides customer support for retail products.\",\n", "    model=bedrock_model,\n", "    tools=[\n", "        get_customer_profile,\n", "        list_customer_purchases,\n", "        list_customer_tickets,\n", "        update_customer_profile\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test a safe question for the customer support Agent\n", "agent(f\"what is the data from customer_id {sample_profile.customer_id}?\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with user input in which guadrail is intervened and input is redacted with custom message \n", "agent(\"How should I allocate my 401(k) investments?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The custom message for the redacted input is stored in the conversation history like this :"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Conversation history: {json.dumps(agent.messages, indent=4)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Clean up \n", "\n", "Please delete the guardrail as follows :"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bedrock_client.delete_guardrail(guardrailIdentifier=guardrail_id)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Congratulations!\n", "\n", "In this notebook, we demonstrated how to:\n", "\n", "1. Create an Amazon Bedrock guardrail that prevents providing financial advice\n", "2. Test the guardrail directly using the Bedrock Runtime API\n", "3. Integrate the guardrail with a Strands agent\n", "4. Test the agent with various inputs to see the guardrail in action\n", "5. Integrate bedrock guardrails in a chatbot with Strands agent.\n", "5. Delete the Guardrail\n", "\n", "Guardrails help in keeping AI responses remain safe, compliant, and appropriate for your use case.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}