{"cells": [{"cell_type": "markdown", "id": "8797de2a", "metadata": {}, "source": ["# Persisting memory across Strands Agents sessions\n", "\n", "In this example you will learn how to persist memory across different sessions in your Strands Agents. \n", "\n", "We will use the use case of an agent that does web search using a `duckduckgo` search API.\n", "\n", "In this notebook, we will:\n", "- Explore the capabilities of a memory-powered Strands agent.\n", "- Learn how to store, retrieve, and list memories.\n", "- Understand how to perform web searches via the agent.\n", "- Interact with the agent in an interactive loop.\n", "\n", "\n", "### Usage Examples\n", "\n", "Storing memories:\n", "```\n", "Remember that I prefer tea over coffee\n", "```\n", "\n", "Retrieving memories:\n", "```\n", "What do I prefer to drink?\n", "```\n", "\n", "Listing all memories:\n", "```\n", "Show me everything you remember about me\n", "```\n", "\n", "### Tips for Memory Usage\n", "\n", "- Be explicit when asking the agent to remember information\n", "- Use specific queries to retrieve relevant memories\n", "- Memory persistence enables more natural and contextual conversations"]}, {"cell_type": "markdown", "id": "c468364c", "metadata": {}, "source": ["## Setup and prerequisites\n", "\n", "### Prerequisites\n", "* Python 3.10+\n", "* AWS account and AWS credentials configured in the environment\n", "* Anthropic Claude 3.7 enabled on Amazon Bedrock\n", "* IAM role with permissions to create Amazon Bedrock Knowledge Base, Amazon S3 bucket and Amazon DynamoDB\n", "\n", "Let's now install the requirement packages for our Strands Agent"]}, {"cell_type": "code", "execution_count": null, "id": "ae029383", "metadata": {}, "outputs": [], "source": ["# Install the required packages\n", "!pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "11c92217", "metadata": {}, "outputs": [], "source": ["\n", "# Import Required Libraries\n", "import os\n", "from strands import Agent, tool\n", "from strands.models import bedrock\n", "from strands_tools import mem0_memory\n", "\n", "from ddgs import DDGS\n", "from ddgs.exceptions import DDGSException, RatelimitException\n", "\n", "bedrock.DEFAULT_BEDROCK_MODEL_ID = \"us.anthropic.claude-3-7-sonnet-********-v1:0\" #Optional: Set a default model for Bedrock"]}, {"cell_type": "markdown", "id": "265d4267", "metadata": {}, "source": ["## Mem0 Configuration\n", "\n", "### Memory Backend Options\n", "\n", "The Mem0 Memory Tool supports three different backend configurations:\n", "\n", "1. **[OpenSearch](https://aws.amazon.com/opensearch-service/features/serverless/)** (Recommended for AWS environments):\n", "   - Requires AWS credentials and OpenSearch configuration\n", "   - Set `OPENSEAR<PERSON>_HOST` and optionally `AWS_REGION` (defaults to us-west-2)\n", "\n", "2. **[FAISS]((https://faiss.ai/index.html))** (Default for local development):\n", "   - Uses FAISS as the local vector store backend\n", "   - Requires faiss-cpu package for local vector storage\n", "   - No additional configuration needed\n", "\n", "3. **Mem0 Platform**:\n", "   - Uses the [Mem0 Platform API](https://docs.mem0.ai/platform/quickstart) for memory management\n", "   - Requires a Mem0 API key : `MEM0_API_KEY` in the environment variables\n", "\n", "\n", "### Environment Configuration\n", "\n", "| Environment Variable | Description | Default | Required For |\n", "|---------------------|-------------|----------|--------------|\n", "| OPENSEARCH_HOST | OpenSearch Serverless Host URL | None | OpenSearch |\n", "| AWS_REGION | AWS Region for OpenSearch | us-west-2 | OpenSearch |\n", "| MEM0_API_KEY | Mem0 Platform API key | None | Mem0 Platform |\n", "| DEV | Enable development mode | false | All modes |\n", "\n", "\n", "For the scope of this lab, we can use 2 options as a backend for memory management:\n", "### Option 1. [Opensearch Serverless](https://aws.amazon.com/opensearch-service/features/serverless/) \n", "\n", "This will be our setup architecture for AOSS: \n", "\n", "<div style=\"text-align:left\">\n", "    <img src=\"images/arch_AOSS.png\" width=\"65%\" />\n", "</div>\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "0468a998-ebe9-4277-913f-6ea69de8e6e1", "metadata": {}, "outputs": [], "source": ["# You can manually define your Opensearch Host \n", "#os.environ[\"OPENSEARCH_HOST\"] = \"<your-opensearch-host>.<region>.aoss.amazonaws.com\""]}, {"cell_type": "code", "execution_count": null, "id": "1cc2bbe4-0a89-4b15-93f3-4baa9fc804c0", "metadata": {}, "outputs": [], "source": ["# OR - Run the script to Create Opensearch Serverless resource in your AWS Account\n", "!bash prereqs/deploy_OSS.sh"]}, {"cell_type": "code", "execution_count": null, "id": "075b3163-cd88-4054-969c-0075dfc1dd84", "metadata": {}, "outputs": [], "source": ["# Option 1: Opensearch Serverless\n", "from dotenv import load_dotenv\n", "load_dotenv() # take Opensearch environment variables"]}, {"cell_type": "markdown", "id": "7dc00d27-4a5b-44fb-9bfc-3b2e906c21f5", "metadata": {}, "source": ["\n", "### Option 2 [Mem0 Platform](https://docs.mem0.ai/platform):\n", "\n", "#### [NOTE]: This is not Needed if you have already deployed the Opensearch Serverless option.\n", "\n", "As an alternative, you can create Mem0 API keys by following the steps [here](https://docs.mem0.ai/platform/quickstart#2-api-key-setup) and add it as an environment variable **MEM0_API_KEY**.\n", "This would be the architecture of the setup for Mem0 Platform:\n", "\n", "<div style=\"text-align:left\">\n", "    <img src=\"images/arch_mem0.png\" width=\"65%\" />\n", "</div>\n", "\n", "\n", "To enable the agent's functionality, we need to configure environment variables for AWS credentials and OpenSearch / Mem0 Platform. These variables are used for memory storage and retrieval."]}, {"cell_type": "code", "execution_count": null, "id": "c99dae50-ed8b-4474-b5d6-9cc16981b436", "metadata": {}, "outputs": [], "source": ["# Option 2: Mem0 API key\n", "\n", "#os.environ[\"MEM0_API_KEY\"] = \"<your-mem0-api-key>\""]}, {"cell_type": "markdown", "id": "5679ce42", "metadata": {}, "source": ["## Define System Prompt\n", "\n", "The `SYSTEM_PROMPT` variable defines the behavior and capabilities of the memory agent. This prompt guides the agent to provide personalized responses based on stored memories and perform web searches when necessary."]}, {"cell_type": "code", "execution_count": null, "id": "01951bc0", "metadata": {}, "outputs": [], "source": ["# Define a focused system prompt for memory operations\n", "SYSTEM_PROMPT = \"\"\"You are a helpful personal assistant for a user. Your task is to assist the user by providing personalized responses based on their history. \n", "\n", "Capabilities:\n", "- You can store information using the mem0_memory tool (action=\"store\").\n", "- You can retrieve relevant memories using the mem0_memory tool (action=\"retrieve\").\n", "- You can use duckduckgo_search to find information on the web.\n", "\n", "Key Rules:\n", "- Be conversational and natural in your responses.\n", "- Always retrieve memories before responding to the user and use them to inform your response.\n", "- Store any new user information and user preferences in mem0_memory.\n", "- Only share relevant information.\n", "- Politely indicate when you don't have the information.\n", "\"\"\""]}, {"cell_type": "markdown", "id": "334a7192", "metadata": {}, "source": ["## Define Web Search Tool\n", "\n", "The `websearch` tool using [Duckduckgo Search API](https://github.com/deedy5/duckduckgo_search) function allows the agent to perform web searches. This function handles exceptions and returns search results or appropriate error messages."]}, {"cell_type": "code", "execution_count": null, "id": "8bbe681b", "metadata": {}, "outputs": [], "source": ["@tool\n", "def websearch(\n", "    keywords: str,\n", "    region: str = \"us-en\",\n", "    max_results: int | None = None,\n", ") -> str:\n", "    \"\"\"Search the web to get updated information.\n", "    Args:\n", "        keywords (str): The search query keywords.\n", "        region (str): The search region: wt-wt, us-en, uk-en, ru-ru, etc..\n", "        max_results (int | None): The maximum number of results to return.\n", "    Returns:\n", "        List of dictionaries with search results.\n", "    \"\"\"\n", "    try:\n", "        results = DDGS().text(keywords, region=region, max_results=max_results)\n", "        return results if results else \"No results found.\"\n", "    except RatelimitException:\n", "        return \"RatelimitException: Please try again after a short delay.\"\n", "    except DDGSException as d:\n", "        return f\"DuckDuckGoSearchException: {d}\"\n", "    except Exception as e:\n", "        return f\"Exception: {e}\""]}, {"cell_type": "markdown", "id": "3bd7828a", "metadata": {}, "source": ["## Create Memory Agent\n", "\n", "We will now initialize the memory-focused agent using the defined tools and system prompt. The Strands agent is capable of:\n", "1. Storing and retrieving memories based on context. It uses memory to create more personalized and contextual AI interactions.\n", "2. Performing web searches using DuckDuckGo to give updated information."]}, {"cell_type": "code", "execution_count": null, "id": "e860d7f3", "metadata": {}, "outputs": [], "source": ["# Create an agent with memory, websearch tool\n", "USER_ID = \"new_user\" # Replace with actual user ID\n", "\n", "memory_agent = Agent(\n", "    system_prompt=SYSTEM_PROMPT,\n", "    model=\"us.anthropic.claude-3-7-sonnet-********-v1:0\",  # Optional: Specify the model ID\n", "    tools=[mem0_memory, websearch],\n", ")"]}, {"cell_type": "markdown", "id": "8ea93f03", "metadata": {}, "source": ["## Demonstrate Memory Operations\n", "\n", "The following examples demonstrate how to store, retrieve, and list memories using the memory agent.\n", "\n", "- **store**: Save important information for later retrieval\n", "  - Store user preferences\n", "  - Remember important facts\n", "  - Maintain conversation context\n", "\n", "- **retrieve**: Access relevant memories based on queries\n", "  - Find previously stored information\n", "  - Provide personalized responses based on user history\n", "\n", "- **list**: View all stored memories\n", "  - See what information has been retained\n", "  - Audit stored memories"]}, {"cell_type": "code", "execution_count": null, "id": "825910bf", "metadata": {}, "outputs": [], "source": ["# Store initial memories to demonstrate retrieval\n", "memory_agent.tool.mem0_memory(\n", "    action=\"store\", content=f\"The user's name is {USER_ID}.\", user_id=USER_ID\n", ")\n", "memory_agent.tool.mem0_memory(\n", "    action=\"store\", \n", "    content=\"I like to drink tea more than coffee.\", \n", "    user_id=USER_ID\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f92c7f95", "metadata": {}, "outputs": [], "source": ["# Retrieve memories\n", "retrieved_memories = memory_agent.tool.mem0_memory(\n", "    action=\"retrieve\", query=\"What is the user's name?\", user_id=USER_ID\n", ")\n", "print(\"Retrieved Memories:\", retrieved_memories)"]}, {"cell_type": "code", "execution_count": null, "id": "c7047852", "metadata": {}, "outputs": [], "source": ["# Retrieve memories about preferences\n", "memory_agent.tool.mem0_memory(\n", "    action=\"retrieve\",\n", "    query=\"What are the my drink preferences?\",\n", "    user_id=USER_ID\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "26c5dc92", "metadata": {}, "outputs": [], "source": ["# Ask the agent a question\n", "response = memory_agent(\"What are the events happening in the New York today?\")\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "790d8d37", "metadata": {}, "outputs": [], "source": ["\n", "# List all stored memories\n", "print(\"All Stored Memories:\")\n", "all_memories = memory_agent.tool.mem0_memory(\n", "    action=\"list\", user_id=USER_ID\n", ")"]}, {"cell_type": "markdown", "id": "f5c72c91", "metadata": {}, "source": ["## Interactive Agent Usage\n", "\n", "Finally, we provide an interactive loop for users to interact with the memory agent. Users can store new memories, retrieve existing ones, or list all stored memories.\n", "\n", "To test interactive usage: \n", "1. Install the requirements: `pip install -r requirements.txt`\n", "1. Run the python file `personal_agent_with_memory.py`. "]}, {"cell_type": "markdown", "id": "856ea59c", "metadata": {}, "source": ["## Conclusion\n", "\n", "This notebook demonstrates how to create a personal agent with memory capabilities using the Strands framework. The agent can:\n", "\n", "1. Store information about the user\n", "2. Retrieve relevant memories based on context\n", "3. Search the web for additional information\n", "4. Provide personalized responses\n", "\n", "By combining these capabilities, the agent can maintain context across conversations and provide more personalized assistance over time."]}, {"cell_type": "markdown", "id": "be6fd03a-26cc-4190-bb09-872c6767f972", "metadata": {}, "source": ["### Cleanup\n", "\n", "Run this bash script to clean up the Opensearch Serverless resources. You don't need to run this if you used the \"MEM0_PLATFORM_API\"."]}, {"cell_type": "code", "execution_count": null, "id": "b6113d4f-45d2-46a5-9853-335b26da0a43", "metadata": {}, "outputs": [], "source": ["!sh prereqs/cleanup_OSS.sh"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}