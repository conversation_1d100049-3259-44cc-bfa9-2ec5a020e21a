{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%% md\n"}}, "source": ["# Advanced processing of Strands Agents Response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Strands Agents allows you to intercept and process events as they happen during agent execution using two methods: \n", "    \n", "- **Async iterators**: ideal for asynchronous frameworks like FastAPI, aiohttp, or Django Channels. For these environments, the SDK offers the `stream_async` method which returns an asynchronous iterator. \n", "- **Callback handlers**: allow you to intercept and process events as they happen during agent execution. This enables real-time monitoring, custom output formatting, and integration with external systems.\n", "\n", "In this example, we will show you how to use both methods to handle calls on your agent\n", "\n", "\n", "## Agent Details\n", "<div style=\"float: left; margin-right: 20px; \">\n", "    \n", "|Feature             |Description                                        |\n", "|--------------------|---------------------------------------------------|\n", "|Feature used        |async iterators, callback handlers                 |\n", "|Agent Structure     |single agent architecture                          |\n", "|Native tools used   |calculator                                         |\n", "|Custom tools created|Weather forecast                                   |\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Architecture\n", "\n", "<div style=\"text-align:left;\">\n", "    <img src=\"images/architecture.png\" width=\"65%\" />\n", "</div>\n", "\n", "## Key Features\n", "* Async Iterators for Streaming\n", "* Callback Handlers\n", "\n", "\n", "## Setup and prerequisites\n", "\n", "### Prerequisites\n", "* Python 3.10+\n", "* AWS account\n", "* Anthropic Claude 3.7 enabled on Amazon Bedrock\n", "\n", "Let's now install the requirement packages for our Strands Agent Agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# installing pre-requisites\n", "!pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing dependency packages\n", "\n", "Now let's import the dependency packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "\n", "import httpx\n", "import nest_asyncio\n", "import uvicorn\n", "from fastapi import FastAPI\n", "from fastapi.responses import StreamingResponse\n", "from pydantic import BaseModel\n", "from strands import Agent, tool\n", "from strands_tools import calculator"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Method 1 - Async Iterators for Streaming\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Strands Agents provides support for asynchronous iterators through the `stream_async` method, enabling real-time streaming of agent responses in asynchronous environments like web servers, APIs, and other async applications.\n", "\n", "Since we are show casing this example in a notebook, we need to apply `nest_asyncio` to allow nested use of `asyncio.run` and `loop.run_until_complete`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nest_asyncio.apply()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating and invoking agent with stream_async\n", "\n", "Let's now create our agent with a built-in calculator tool and no `callback_handler`. We will use the `stream_async` method to iteract over the streamed agent events"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize our agent without a callback handler\n", "agent = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",  # Optional: Specify the model ID\n", "    tools=[calculator], \n", "    callback_handler=None)\n", "\n", "# Async function that iterators over streamed agent events\n", "\n", "\n", "async def process_streaming_response():\n", "    agent_stream = agent.stream_async(\"Calculate 2+2\")\n", "    async for event in agent_stream:\n", "        print(event)\n", "\n", "\n", "# Run the agent\n", "asyncio.run(process_streaming_response())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["###  Tracking event loop lifecycle"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This example illustrates the event loop lifecycle and how events relate to each other. It's useful for understanding the flow of execution in the Strands Agent:\n", "\n", "Let's create some printing format code to better analyse the agent stream events. We will continue to use the same agent for it"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["# Async function that iterators over streamed agent events\n", "\n", "\n", "async def process_streaming_response():\n", "    agent_stream = agent.stream_async(\"What is the capital of France and what is 42+7?\")\n", "    async for event in agent_stream:\n", "        # Track event loop lifecycle\n", "        if event.get(\"init_event_loop\", False):\n", "            print(\"🔄 Event loop initialized\")\n", "        elif event.get(\"start_event_loop\", False):\n", "            print(\"▶️ Event loop cycle starting\")\n", "        elif event.get(\"start\", False):\n", "            print(\"📝 New cycle started\")\n", "        elif \"message\" in event:\n", "            print(f\"📬 New message created: {event['message']['role']}\")\n", "        elif event.get(\"force_stop\", False):\n", "            print(\n", "                f\"🛑 Event loop force-stopped: {event.get('force_stop_reason', 'unknown reason')}\"\n", "            )\n", "\n", "        # Track tool usage\n", "        if \"current_tool_use\" in event and event[\"current_tool_use\"].get(\"name\"):\n", "            tool_name = event[\"current_tool_use\"][\"name\"]\n", "            print(f\"🔧 Using tool: {tool_name}\")\n", "\n", "        # Show only a snippet of text to keep output clean\n", "        if \"data\" in event:\n", "            # Only show first 20 chars of each chunk for demo purposes\n", "            data_snippet = event[\"data\"][:20] + (\n", "                \"...\" if len(event[\"data\"]) > 20 else \"\"\n", "            )\n", "            print(f\"📟 Text: {data_snippet}\")\n", "\n", "    return event[\"result\"]\n", "\n", "\n", "# Run the agent\n", "asyncio.run(process_streaming_response())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FastAPI Integration\n", "\n", "You can also integrate your `stream_async` with FastAPI to create a streaming endpoint to your applications. For this, we will add a `weather_forecast` tool to our agent. The architecture update looks as following\n", "\n", "<div style=\"text-align:left;\">\n", "    <img src=\"images/architecture_2.png\" width=\"65%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tool definition\n", "\n", "\n", "@tool\n", "def weather_forecast(city: str, days: int = 3) -> str:\n", "    return f\"Weather forecast for {city} for the next {days} days...\"\n", "\n", "\n", "# FastAPI app\n", "app = FastAPI()\n", "\n", "\n", "class PromptRequest(BaseModel):\n", "    prompt: str\n", "\n", "\n", "@app.post(\"/stream\")\n", "async def stream_response(request: PromptRequest):\n", "    async def generate():\n", "        agent = Agent(tools=[calculator, weather_forecast], callback_handler=None)\n", "        try:\n", "            async for event in agent.stream_async(request.prompt):\n", "                if \"data\" in event:\n", "                    yield event[\"data\"]\n", "        except Exception as e:\n", "            yield f\"Error: {str(e)}\"\n", "\n", "    return StreamingResponse(generate(), media_type=\"text/plain\")\n", "\n", "\n", "# Function to start server without blocking\n", "\n", "\n", "async def start_server():\n", "    config = uvicorn.Config(app, host=\"0.0.0.0\", port=8001, log_level=\"info\")\n", "    server = uvicorn.Server(config)\n", "    await server.serve()\n", "\n", "\n", "# Run server as background task\n", "if \"server_task\" not in globals():\n", "    server_task = asyncio.create_task(start_server())\n", "    await asyncio.sleep(0.1)  # Give server time to start\n", "\n", "print(\"✅ Server is running at http://0.0.0.0:8001\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Invoking the FastAPI agent\n", "And we can now invoke the agent with a prompt "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def fetch_stream():\n", "    async with httpx.AsyncClient() as client:\n", "        async with client.stream(\n", "            \"POST\",\n", "            \"http://0.0.0.0:8001/stream\",\n", "            json={\"prompt\": \"What is weather in NYC?\"},\n", "        ) as response:\n", "            async for line in response.aiter_lines():\n", "                if line.strip():  # Skip empty lines\n", "                    print(\"Received:\", line)\n", "\n", "\n", "await fetch_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Method 2 - Callback Handlers for streaming"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Callback handlers are a powerful feature of the Strands Agents that allow you to intercept and process events as they happen during agent execution. This enables real-time monitoring, custom output formatting, and integration with external systems.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Callback handlers receive events in real-time as they occur during an agent's lifecycle:\n", "\n", "- Text generation from the model\n", "- Tool selection and execution\n", "- Reasoning process\n", "- Errors and completions\n", "\n", "\n", "Let's now create a custom callback handler function that formats the event inputs to highlight tool usage and model output. To do so, we will again use the agent with a calculator tool only\n", "\n", "<div style=\"text-align:left;\">\n", "    <img src=\"images/architecture.png\" width=\"65%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def custom_callback_handler(**kwargs):\n", "    # Process stream data\n", "    if \"data\" in kwargs:\n", "        print(f\"MODEL OUTPUT: {kwargs['data']}\")\n", "    elif \"current_tool_use\" in kwargs and kwargs[\"current_tool_use\"].get(\"name\"):\n", "        print(f\"\\nUSING TOOL: {kwargs['current_tool_use']['name']}\")\n", "\n", "\n", "# Create an agent with custom callback handler\n", "agent = Agent(   \n", "        model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",  # Optional: Specify the model ID\n", "        tools=[calculator], \n", "        callback_handler=custom_callback_handler)\n", "\n", "agent(\"Calculate 2+2\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Congratulations!\n", "\n", "In this notebook you learned how to stream your agents outputs using async iteractors and callback handlers. "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}