{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%% md\n"}}, "source": ["# Use Model Context Protocol (MCP) as tools with Strands Agent\n", "\n", "## Overview\n", "The [Model Context Protocol (MCP)](https://modelcontextprotocol.io/introduction) is an open protocol that standardizes how applications provide context to Large Language Models (LLMs). Strands AI SDK integrates with MCP to extend agent capabilities through external tools and services.\n", "\n", "MCP enables communication between agents and MCP servers that provide additional tools. The Strands Agent SDK includes built-in support for connecting to MCP servers and using their tools.\n", "\n", "In this example we will show you how to use MCP tools on your Strands Agent. We will use the [AWS Documentation MCP server](https://awslabs.github.io/mcp/servers/aws-documentation-mcp-server/) which provides tools to access AWS documentation, search for content, and get recommendations. This MCP server has 3 main features:\n", "\n", "- **Read Documentation**: Fetch and convert AWS documentation pages to markdown format\n", "- **Search Documentation**: Search AWS documentation using the official search API\n", "- **Recommendations**: Get content recommendations for AWS documentation pages\n", "\n", "\n", "\n", "## Agent Details\n", "<div style=\"float: left; margin-right: 20px;\">\n", "    \n", "|Feature             |Description                                        |\n", "|--------------------|---------------------------------------------------|\n", "|Feature used        |MCP Tools                                          |\n", "|Agent Structure     |Single agent architecture                          |\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Architecture\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"images/architecture.png\" width=\"65%\" />\n", "</div>\n", "\n", "## Key Features\n", "* **Single agent architecture**: this example creates a single agent that interacts with MCP tools\n", "* **MCP tools**: Integration of MCP tools with your agent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and prerequisites\n", "\n", "### Prerequisites\n", "* Python 3.10+\n", "* AWS account\n", "* Anthropic Claude 3.7 enabled on Amazon Bedrock\n", "\n", "Let's now install the requirement packages for our Strands Agent agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# installing pre-requisites\n", "!pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing dependency packages\n", "\n", "Now let's import the dependency packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import threading\n", "import time\n", "from datetime import timedelta\n", "\n", "from mcp import StdioServerParameters, stdio_client\n", "from mcp.client.streamable_http import streamablehttp_client\n", "from mcp.server import FastMCP\n", "from strands import Agent\n", "from strands.tools.mcp import MCPClient"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Connect to MCP server using stdio transport\n", "\n", "[Transports](https://modelcontextprotocol.io/specification/2025-03-26/basic/transports) in MCP provide the foundations for communication between clients and servers. It handles the underlying mechanics of how messages are sent and received. At the moment there are three standards transport implementations built-in in MCP:\n", "\n", "- **Standard Input/Output (stdio)**: enables communication through standard input and output streams. It is particularly useful for local integrations and command-line tools\n", "- **Streamable HTTP**: this replaces the HTTP+SSE transport from previous protocol version. In the Streamable HTTP transport, the server operates as an independent process that can handle multiple client connections. This transport uses HTTP POST and GET requests. Server can optionally make use of Server-Sent Events (SSE) to stream multiple server messages. This permits basic MCP servers, as well as more feature-rich servers supporting streaming and server-to-client notifications and requests.\n", "- **SSE**: legacy transport for HTTP-based MCP servers that use Server-Sent Events transport  \n", "\n", "Overall, you should use stdio for building command-line tools, implementing local integrations and working with shell scripts. You should use Streamable HTTP transports when you need a flexible and efficient way for AI agents to communicate with tools and services, especially when dealing with stateless communication or when minimizing resource usage is crucial.\n", "\n", "You can also use **custom transports** implementation for your specific needs. \n", "\n", "\n", "Let's now connect to the MCP server using stdio transport. First of all, we will use the class `MCPClient` to connect to the [AWS Documentation MCP Server](https://awslabs.github.io/mcp/servers/aws-documentation-mcp-server/). This server provides tools to access AWS documentation, search for content, and get recommendations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Connect to an MCP server using stdio transport\n", "stdio_mcp_client = MCPClient(\n", "    lambda: stdio_client(\n", "        StdioServerParameters(\n", "            command=\"uvx\", args=[\"awslabs.aws-documentation-mcp-server@latest\"]\n", "        )\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Setup agent configuration and invoke it\n", "\n", "Next we will set our agent configuration using the tools from the `stdio_mcp_client` object we just created. To do so, we need to list the tools available in the MCP server. We can use the `list_tools_sync` method for it. \n", "\n", "After that, we will ask a question to our agent."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create an agent with MCP tools\n", "with stdio_mcp_client:\n", "    # Get the tools from the MCP server\n", "    tools = stdio_mcp_client.list_tools_sync()\n", "\n", "    # Create an agent with these tools\n", "    agent = Agent(\n", "        model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "        tools=tools)\n", "\n", "    response = agent(\"What is Amazon Bedrock pricing model. Be concise.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Connect to MCP server using Streamable HTTP\n", "\n", "Let's now connect to the MCP server using Streamable HTTP transport. First let's start a simple MCP server using Streamable HTTP transport. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["For this example we will create our own MCP server. The architecture will look as following\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"images/architecture_2.png\" width=\"65%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create an MCP server\n", "mcp = FastMCP(\"Calculator Server\")\n", "\n", "# Define a tool\n", "\n", "\n", "@mcp.tool(description=\"Calculator tool which performs calculations\")\n", "def calculator(x: int, y: int) -> int:\n", "    return x + y\n", "\n", "\n", "@mcp.tool(description=\"This is a long running tool\")\n", "def long_running_tool(name: str) -> str:\n", "    time.sleep(25)\n", "    return f\"Hello {name}\"\n", "\n", "\n", "def main():\n", "    mcp.run(transport=\"streamable-http\", mount_path=\"mcp\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's now start a thread with the `streamable-http` server"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["thread = threading.Thread(target=main)\n", "thread.start()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Integrating Streamable HTTP client with Agent\n", "\n", "Now let's use `streamablehttp_client` integrate this server with a simple agent. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_streamable_http_transport():\n", "    return streamablehttp_client(\"http://localhost:8000/mcp\")\n", "\n", "\n", "streamable_http_mcp_client = MCPClient(create_streamable_http_transport)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Setup agent configuration and invoke it\n", "\n", "Next we will set our agent configuration using the tools from the `streamable_http_mcp_client` object we just created. To do so, we need to list the tools available in the MCP server. We can use the `list_tools_sync` method for it. \n", "\n", "After that, we will ask a question to our agent."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with streamable_http_mcp_client:\n", "    tools = streamable_http_mcp_client.list_tools_sync()\n", "\n", "    agent = Agent(\n", "        model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "        tools=tools)\n", "\n", "    response = str(agent(\"What is 2 + 2?\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Direct Tool Invocation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["While tools are typically invoked by the agent based on user requests, you can also call MCP tools directly. This can be useful for workflow scenarios where you orchestrate multiple tools together."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = {\"x\": 10, \"y\": 20}\n", "\n", "with streamable_http_mcp_client:\n", "    # direct tool invocation\n", "    result = streamable_http_mcp_client.call_tool_sync(\n", "        tool_use_id=\"tool-123\", name=\"calculator\", arguments=query\n", "    )\n", "\n", "    # Process the result\n", "    print(f\"Calculation result: {result['content'][0]['text']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can optionally also provide `read_timeout_seconds` while calling an MCP server tool to avoid it running for too long"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with streamable_http_mcp_client:\n", "    try:\n", "        result = streamable_http_mcp_client.call_tool_sync(\n", "            tool_use_id=\"tool-123\",\n", "            name=\"long_running_tool\",\n", "            arguments={\"name\": \"Amazon\"},\n", "            read_timeout_seconds=timed<PERSON>ta(seconds=30),\n", "        )\n", "\n", "        if result[\"status\"] == \"error\":\n", "            print(f\"Tool execution failed: {result['content'][0]['text']}\")\n", "        else:\n", "            print(f\"Tool execution succeeded: {result['content'][0]['text']}\")\n", "    except Exception as e:\n", "        print(f\"Tool call timed out or failed: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interacting with multiple MCP servers\n", "\n", "With Strands Agents you can also interact with multiple MCP servers using the same agent and configure tools setups such as the max number of tools that can be used in parallel (`max_parallel_tools`). Let's create a new agent to showcase this configuration:\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"images/architecture_3.png\" width=\"85%\" />\n", "</div>\n", "\n", "In this agent, we will again use the AWS Documentation MCP server and we will also use the [AWS CDK MCP Server](https://awslabs.github.io/mcp/servers/cdk-mcp-server/) which helps with AWS Cloud Development Kit (CDK) best practices, infrastructure as code patterns and security compliance with CDK Nag.\n", "\n", "First let's connect to the two MCP servers using the stdio transport"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Connect to an MCP server using stdio transport\n", "aws_docs_mcp_client = MCPClient(\n", "    lambda: stdio_client(\n", "        StdioServerParameters(\n", "            command=\"uvx\", args=[\"awslabs.aws-documentation-mcp-server@latest\"]\n", "        )\n", "    )\n", ")\n", "\n", "# Connect to an MCP server using stdio transport\n", "cdk_mcp_client = MCPClient(\n", "    lambda: stdio_client(\n", "        StdioServerParameters(command=\"uvx\", args=[\"awslabs.cdk-mcp-server@latest\"])\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Create Agent with MCP servers\n", "\n", "Next we will create the agent with the tools from both MCP servers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create an agent with MCP tools\n", "with aws_docs_mcp_client, cdk_mcp_client:\n", "    # Get the tools from the MCP server\n", "    tools = aws_docs_mcp_client.list_tools_sync() + cdk_mcp_client.list_tools_sync()\n", "\n", "    # Create an agent with these tools\n", "    agent = Agent(\n", "        model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "        tools=tools)\n", "\n", "    response = agent(\n", "        \"What is Amazon Bedrock pricing model. Be concise. Also what are the best practices related to CDK?\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Congratulations!\n", "\n", "In this notebook you learned how to connect with MCP servers using Strands Agent and two MCP transport protocols: stdio and Streamable HTTP. You also learned how to connect multiple MCP servers to the same agent. Next, let's see how to use different models with your agent"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}