{"cells": [{"cell_type": "markdown", "id": "8ab9a6cf-bdfd-4204-9d60-21bf65cc238c", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# Adding custom tools to your Strands Agents\n", "\n", "## Overview\n", "In this example we will guide you through the different ways to create custom tools using Strands Agents. We will build a personal assistant use case that connects with a local SQLite database to perform data tasks. As a bonus, we will also guide you through the usage of the reasoning capabilities on Claude Sonnet 3.7 using the `thinking` field of the `BedrockModel` class\n", "\n", "## Agent Details\n", "<div style=\"float: left; margin-right: 20px;\">\n", "    \n", "|Feature             |Description                                        |\n", "|--------------------|---------------------------------------------------|\n", "|Native tools used   |current_time, calculator                           |\n", "|Custom tools created|create_appointment, list_appointments              |\n", "|Agent Structure     |Single agent architecture                          |\n", "\n", "</div>\n"]}, {"cell_type": "markdown", "id": "8d0244b2-07af-4673-a36c-d7d1a37826f4", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## Architecture\n", "\n", "<div style=\"text-align:left\">\n", "    <img src=\"images/architecture.png\" width=\"85%\" />\n", "</div>\n", "\n", "## Key Features\n", "* **Single agent architecture**: this example creates a single agent that interacts with built-in and custom tools\n", "* **Built-in tools**: learn how to use Strands Agent's tools\n", "* **Custom tools**: lean how to create your own tools\n", "* **Bedrock Model as underlying LLM**: Used Anthropic Claude 3.7 from Amazon Bedrock as the underlying LLM model"]}, {"cell_type": "markdown", "id": "62b0823b-d4e0-495d-a75c-50cbada8e0b0", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## Setup and prerequisites\n", "\n", "### Prerequisites\n", "* Python 3.10+\n", "* AWS account\n", "* Anthropic Claude 3.7 enabled on Amazon Bedrock, [guide](https://docs.aws.amazon.com/bedrock/latest/userguide/model-access.html)\n", "* IAM role with permissions to create Amazon Bedrock Knowledge Base, Amazon S3 bucket and Amazon DynamoDB\n", "\n", "Let's now install the requirement packages for our Strands Agent"]}, {"cell_type": "code", "execution_count": null, "id": "cd150109-40be-452d-9fae-c0653205ccae", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["# installing pre-requisites\n", "!pip install -r requirements.txt"]}, {"cell_type": "markdown", "id": "80ef5b1f-7d01-42e9-a558-891792b1c5b7", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### Importing dependency packages\n", "\n", "Now let's import the dependency packages"]}, {"cell_type": "code", "execution_count": null, "id": "7092f7af-01e7-469d-96d0-3c80987c5183", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import json\n", "import sqlite3\n", "import uuid\n", "from datetime import datetime\n", "\n", "from strands import Agent, tool\n", "from strands.models import BedrockModel"]}, {"cell_type": "markdown", "id": "c5072db6-071b-4d06-909b-23fd38981b17", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## Defining custom tools\n", "Next let's define custom tools to interact with a local SQLite database:\n", "* **create_appointment**: create a new personal appointment with unique id, date, location, title and description \n", "* **list_appointment**: list all available appointments\n", "* **update_appointments**: update an appointment based on the appointment id"]}, {"cell_type": "markdown", "id": "540b2f03-9935-4b56-a9da-6f36c6f00e9a", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### Defining tools in the same file of your agent\n", "\n", "There are multiple ways to define tools with the Strands Agents SDK. The first one is to add a `@tool` decorator to your function and provide the documentation to it. In this case, Strands Agents will use the function documentation, typing and arguments to provide the tools to your agent. In this case, you can even define the tool in the same file as your agent"]}, {"cell_type": "code", "execution_count": null, "id": "e7785c6e-0525-4f28-9958-95eb562f07dc", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["@tool\n", "def create_appointment(date: str, location: str, title: str, description: str) -> str:\n", "    \"\"\"\n", "    Create a new personal appointment in the database.\n", "\n", "    Args:\n", "        date (str): Date and time of the appointment (format: YYYY-MM-DD HH:MM).\n", "        location (str): Location of the appointment.\n", "        title (str): Title of the appointment.\n", "        description (str): Description of the appointment.\n", "\n", "    Returns:\n", "        str: The ID of the newly created appointment.\n", "\n", "    Raises:\n", "        ValueError: If the date format is invalid.\n", "    \"\"\"\n", "    # Validate date format\n", "    try:\n", "        datetime.strptime(date, \"%Y-%m-%d %H:%M\")\n", "    except ValueError:\n", "        raise ValueError(\"Date must be in format 'YYYY-MM-DD HH:MM'\")\n", "\n", "    # Generate a unique ID\n", "    appointment_id = str(uuid.uuid4())\n", "\n", "    conn = sqlite3.connect(\"appointments.db\")\n", "    cursor = conn.cursor()\n", "\n", "    # Create the appointments table if it doesn't exist\n", "    cursor.execute(\n", "        \"\"\"\n", "    CREATE TABLE IF NOT EXISTS appointments (\n", "        id TEXT PRIMARY KEY,\n", "        date TEXT,\n", "        location TEXT,\n", "        title TEXT,\n", "        description TEXT\n", "    )\n", "    \"\"\"\n", "    )\n", "\n", "    cursor.execute(\n", "        \"INSERT INTO appointments (id, date, location, title, description) VALUES (?, ?, ?, ?, ?)\",\n", "        (appointment_id, date, location, title, description),\n", "    )\n", "\n", "    conn.commit()\n", "    conn.close()\n", "    return f\"Appointment with id {appointment_id} created\""]}, {"cell_type": "markdown", "id": "d0588544-d3ea-4a17-958e-17e734b098a9", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### Tool definition with Module-Based Approach\n", "\n", "You can also define your tools as a standalone file and import it to your agent. In this case you can still use the decorator approach or you could also define your function using a TOOL_SPEC dictionary. The formating is similar to the one used by the [Amazon Bedrock Converse API](https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use-examples.html) for tool usage. In this case you are more flexible to define the required parameters as well as the return of success and error executions and TOOL_SPEC definitions will work in this case."]}, {"cell_type": "markdown", "id": "65e70d34-08b7-44ce-920a-863d293c727d", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Decorator approach\n", "\n", "When defining your tool using a decorator in a standalone file, your process is very similar to the one in the same file as your agent, but you will need to import or agent tool later on."]}, {"cell_type": "code", "execution_count": null, "id": "824bd53a-2474-4a31-bfd3-415eb4ca4f38", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["%%writefile list_appointments.py\n", "import json\n", "import sqlite3\n", "import os\n", "from strands import tool\n", "\n", "@tool\n", "def list_appointments() -> str:\n", "    \"\"\"\n", "    List all available appointments from the database.\n", "    \n", "    Returns:\n", "        str: the appointments available \n", "    \"\"\"\n", "    # Check if database exists\n", "    if not os.path.exists('appointments.db'):\n", "        return \"No appointment available\"\n", "    \n", "    conn = sqlite3.connect('appointments.db')\n", "    conn.row_factory = sqlite3.Row  # This enables column access by name\n", "    cursor = conn.cursor()\n", "    \n", "    # Check if the appointments table exists\n", "    try:\n", "        cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table' AND name='appointments'\")\n", "        if not cursor.fetchone():\n", "            conn.close()\n", "            return \"No appointment available\"\n", "        \n", "        cursor.execute(\"SELECT * FROM appointments ORDER BY date\")\n", "        rows = cursor.fetchall()\n", "        \n", "        # Convert rows to dictionaries\n", "        appointments = []\n", "        for row in rows:\n", "            appointment = {\n", "                'id': row['id'],\n", "                'date': row['date'],\n", "                'location': row['location'],\n", "                'title': row['title'],\n", "                'description': row['description']\n", "            }\n", "            appointments.append(appointment)\n", "        \n", "        conn.close()\n", "        return json.dumps(appointments)\n", "    \n", "    except sqlite3.Error:\n", "        conn.close()\n", "        return []\n"]}, {"cell_type": "markdown", "id": "11873e74-e528-4a44-9879-6761b85a248c", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### TOOL_SPEC approach\n", "\n", "Alternativelly, you can use the TOOL_SPEC approach when defining your tool"]}, {"cell_type": "code", "execution_count": null, "id": "574198a3-4a65-4373-ae36-1a037eb12b49", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["%%writefile update_appointment.py\n", "import sqlite3\n", "from datetime import datetime\n", "import os\n", "from strands.types.tools import ToolResult, ToolUse\n", "from typing import Any\n", "\n", "TOOL_SPEC = {\n", "    \"name\": \"update_appointment\",\n", "    \"description\": \"Update an appointment based on the appointment ID.\",\n", "    \"inputSchema\": {\n", "        \"json\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"appointment_id\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The appointment id.\"\n", "                },\n", "                \"date\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Date and time of the appointment (format: YYYY-MM-DD HH:MM).\"\n", "                },\n", "                \"location\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Location of the appointment.\"\n", "                },\n", "                \"title\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Title of the appointment.\"\n", "                },\n", "                \"description\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Description of the appointment.\"\n", "                }\n", "            },\n", "            \"required\": [\"appointment_id\"]\n", "        }\n", "    }\n", "}\n", "# Function name must match tool name\n", "def update_appointment(tool: ToolUse, **kwargs: Any) -> ToolResult:\n", "    tool_use_id = tool[\"toolUseId\"]\n", "    appointment_id = tool[\"input\"][\"appointment_id\"]\n", "    if \"date\" in tool[\"input\"]:\n", "        date = tool[\"input\"][\"date\"]\n", "    else:\n", "        date = None\n", "    if \"location\" in tool[\"input\"]:\n", "        location = tool[\"input\"][\"location\"]\n", "    else:\n", "        location = None\n", "    if \"title\" in tool[\"input\"]:\n", "        title = tool[\"input\"][\"title\"]\n", "    else:\n", "        title = None\n", "    if \"description\" in tool[\"input\"]:\n", "        description = tool[\"input\"][\"description\"]\n", "    else:\n", "        description = None\n", "        \n", "    # Check if database exists\n", "    if not os.path.exists('appointments.db'): \n", "        return {\n", "            \"toolUseId\": tool_use_id,\n", "            \"status\": \"error\",\n", "            \"content\": [{\"text\": f\"Appointment {appointment_id} does not exist\"}]\n", "        } \n", "    \n", "    # Check if appointment exists\n", "    conn = sqlite3.connect('appointments.db')\n", "    cursor = conn.cursor()\n", "    \n", "    # Check if the appointments table exists\n", "    try:\n", "        cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table' AND name='appointments'\")\n", "        if not cursor.fetchone():\n", "            conn.close()\n", "            return {\n", "                \"toolUseId\": tool_use_id,\n", "                \"status\": \"error\",\n", "                \"content\": [{\"text\": f\"Appointments table does not exist\"}]\n", "            }\n", "        \n", "        cursor.execute(\"SELECT * FROM appointments WHERE id = ?\", (appointment_id,))\n", "        appointment = cursor.fetchone()\n", "        \n", "        if not appointment:\n", "            conn.close()\n", "            return {\n", "                \"toolUseId\": tool_use_id,\n", "                \"status\": \"error\",\n", "                \"content\": [{\"text\": f\"Appointment {appointment_id} does not exist\"}]\n", "            }\n", "        \n", "        # Validate date format if provided\n", "        if date:\n", "            try:\n", "                datetime.strptime(date, '%Y-%m-%d %H:%M')\n", "            except ValueError:\n", "                conn.close()\n", "                return {\n", "                    \"toolUseId\": tool_use_id,\n", "                    \"status\": \"error\",\n", "                    \"content\": [{\"text\": \"Date must be in format 'YYYY-MM-DD HH:MM'\"}]\n", "                }\n", "        \n", "        # Build update query\n", "        update_fields = []\n", "        params = []\n", "        \n", "        if date:\n", "            update_fields.append(\"date = ?\")\n", "            params.append(date)\n", "        \n", "        if location:\n", "            update_fields.append(\"location = ?\")\n", "            params.append(location)\n", "        \n", "        if title:\n", "            update_fields.append(\"title = ?\")\n", "            params.append(title)\n", "        \n", "        if description:\n", "            update_fields.append(\"description = ?\")\n", "            params.append(description)\n", "        \n", "        # If no fields to update\n", "        if not update_fields:\n", "            conn.close()\n", "            return {\n", "                \"toolUseId\": tool_use_id,\n", "                \"status\": \"success\",\n", "                \"content\": [{\"text\": \"No need to update your appointment, you are all set!\"}]\n", "            }\n", "        \n", "        # Complete the query\n", "        query = f\"UPDATE appointments SET {', '.join(update_fields)} WHERE id = ?\"\n", "        params.append(appointment_id)\n", "        \n", "        cursor.execute(query, params)\n", "        conn.commit()\n", "        conn.close()\n", "        \n", "        return {\n", "            \"toolUseId\": tool_use_id,\n", "            \"status\": \"success\",\n", "            \"content\": [{\"text\": f\"Appointment {appointment_id} updated with success\"}]\n", "        }\n", "    \n", "    except sqlite3.Error as e:\n", "        conn.close()\n", "        return {\n", "            \"toolUseId\": tool_use_id,\n", "            \"status\": \"error\",\n", "            \"content\": [{\"text\": str(e)}]\n", "        }\n"]}, {"cell_type": "markdown", "id": "0944e32e-a80e-44ee-b5cb-9d318c076ba9", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["let's now import `list_appointments` and `update_appointment` as a tool"]}, {"cell_type": "code", "execution_count": null, "id": "ccd37708-4a04-410e-a976-bd239b884003", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import list_appointments\n", "import update_appointment"]}, {"cell_type": "markdown", "id": "e8fbc3fe-326f-4fa7-b83c-1ecb55bb1d19", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## Creating Agent\n", "\n", "Now that we have created our custom tools, let's define our first agent. To do so, we need to create a system prompt that defines what the agent should and should not do. We will then define our agent's underlying LLM model and we will provide it with built-in and custom tools. \n", "\n", "#### Setting agent system prompt\n", "In the system prompt we will define the instructions for our agent"]}, {"cell_type": "code", "execution_count": null, "id": "397395ba-7c84-42f3-badf-4cb1734d0008", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["system_prompt = \"\"\"You are a helpful personal assistant that specializes in managing my appointments and calendar. \n", "You have access to appointment management tools, a calculator, and can check the current time to help me organize my schedule effectively. \n", "Always provide the appointment id so that I can update it if required\"\"\""]}, {"cell_type": "markdown", "id": "18fadc98-e823-4ead-b00f-ccb62d1653a3", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Defining agent underlying LLM model\n", "\n", "Next let's define our agent underlying model. Strands Agents natively integrate with Amazon Bedrock models, and provides the ability to configure how the model is called. Below, you can see a simple initialization of a `BedrockModel` provider, with some of the optional configurations commented out. You can learn more about configuration options, and default values, at [Strands Agents Bedrock Model Provider documentation](https://strandsagents.com/0.1.x/user-guide/concepts/model-providers/amazon-bedrock/). For our example, we will use the `Anthropic Claude 3.7 Sonnet` model from Bedrock."]}, {"cell_type": "code", "execution_count": null, "id": "ecaa6e8e-5550-451e-9507-5e25018bc77d", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    # region_name=\"us-east-1\",\n", "    # boto_client_config=Config(\n", "    #    read_timeout=900,\n", "    #    connect_timeout=900,\n", "    #    retries=dict(max_attempts=3, mode=\"adaptive\"),\n", "    # ),\n", "    # temperature=0.9,\n", "    # max_tokens=2048,\n", ")"]}, {"cell_type": "markdown", "id": "b68f9a35-7b75-43b9-81cb-1a6dacc5d753", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Import built-in tools\n", "\n", "The next step to build our agent is to import our Strands Agents built-in tools. Strands Agents provides a set of commonly used built-in tools in the optional package `strands-tools`. You have tools for RAG, memory, file operations, code interpretation and others available in this repo. For our example we will use the `current_time` tool to provide our agent with the information about the current time and the `calculator` tool to do some math"]}, {"cell_type": "code", "execution_count": null, "id": "b00643e6-f032-4c75-a031-47230ff57ea6", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from strands_tools import calculator, current_time"]}, {"cell_type": "markdown", "id": "5d21b669-3057-4f01-919c-cc3d43813fc9", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Defining Agent\n", "\n", "Now that we have all the required information available, let's define our agent"]}, {"cell_type": "code", "execution_count": null, "id": "e9cc4158-9c91-4814-8506-c8114221c9e4", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["agent = Agent(\n", "    model=model,\n", "    system_prompt=system_prompt,\n", "    tools=[\n", "        current_time,\n", "        calculator,\n", "        create_appointment,\n", "        list_appointments,\n", "        update_appointment,\n", "    ],\n", ")"]}, {"cell_type": "markdown", "id": "834748c9-d129-4074-b64c-1f5eeb979e97", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## Invoking agent\n", "\n", "Let's now invoke our restaurant agent with a greeting and analyse its results"]}, {"cell_type": "code", "execution_count": null, "id": "b5b8a00b-dfe6-49f8-acdd-89c350c82081", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["results = agent(\"How much is 2+2?\")"]}, {"cell_type": "markdown", "id": "34c4dd50-b9d4-493a-92d5-6b2db487a5b3", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Analysing the agent's results\n", "\n", "Nice! We've invoked our agent for the first time! Let's now explore the results object. First thing we can see is the messages being exchanged by the agent in the agent's object"]}, {"cell_type": "code", "execution_count": null, "id": "4ae1385b-4821-48c3-9fd8-956cc61de6ee", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["agent.messages"]}, {"cell_type": "markdown", "id": "e6f81a22-c289-4980-bccb-13f4235b69d8", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Next we can take a look at the usage of our agent for the last query by analysing the result `metrics`"]}, {"cell_type": "code", "execution_count": null, "id": "20c0dd86-8ecd-419c-a8a1-5add2b02b521", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["results.metrics"]}, {"cell_type": "markdown", "id": "beb5d753-d691-479e-8468-fe23927b6d63", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Invoking agent with follow up question\n", "Ok, let's now make an appointment for tomorrow"]}, {"cell_type": "code", "execution_count": null, "id": "d4ed9ea3-b50c-4740-ae8a-19bd7338da45", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["results = agent(\n", "    \"Book 'Agent fun' for tomorrow 3pm in NYC. This meeting will discuss all the fun things that an agent can do\"\n", ")"]}, {"cell_type": "markdown", "id": "3a6789db-9666-4aba-b941-1b4df9e5111c", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Updating appointment\n", "\n", "Let's now update this appointment"]}, {"cell_type": "code", "execution_count": null, "id": "26ac2e57-a5a0-483a-b70b-26688addf7c1", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["results = agent(\"Oh no! My bad, 'Agent fun' is actually happening in DC\")"]}, {"cell_type": "markdown", "id": "bb6a31ee-e3b7-4f1d-8372-3092175b6d78", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Analysing the agent's results\n", "Let's look at the agent messages and result metrics again"]}, {"cell_type": "code", "execution_count": null, "id": "1faa13ac-4809-4a4c-b94e-d57b733d7c1e", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["agent.messages"]}, {"cell_type": "code", "execution_count": null, "id": "f4fb28fc-f418-42c8-b37a-03c569101070", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["results.metrics"]}, {"cell_type": "markdown", "id": "86054d56-51e5-40c6-9133-cd9cf5a1cbc1", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#### Checking tool usage from messages\n", "\n", "Let's deep-dive into the tool usage in the messages dictionary. Later on we will show case how to observe and evaluate your agent's behavior, but this is the first step in this direction"]}, {"cell_type": "code", "execution_count": null, "id": "24912341-65ae-41c0-a6e2-74d548d68c99", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["for m in agent.messages:\n", "    for content in m[\"content\"]:\n", "        if \"toolUse\" in content:\n", "            print(\"Tool Use:\")\n", "            tool_use = content[\"toolUse\"]\n", "            print(\"\\tToolUseId: \", tool_use[\"toolUseId\"])\n", "            print(\"\\tname: \", tool_use[\"name\"])\n", "            print(\"\\tinput: \", tool_use[\"input\"])\n", "        if \"toolResult\" in content:\n", "            print(\"Tool Result:\")\n", "            tool_result = m[\"content\"][0][\"toolResult\"]\n", "            print(\"\\tToolUseId: \", tool_result[\"toolUseId\"])\n", "            print(\"\\tStatus: \", tool_result[\"status\"])\n", "            print(\"\\tContent: \", tool_result[\"content\"])\n", "            print(\"=======================\")"]}, {"cell_type": "markdown", "id": "8e88a95d-bc76-4519-bfdf-2a8ab3338c73", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### Validating that the action was performed correctly\n", "Let's now check our database to confirm that the operations where done correctly. The `Agent` class has the ability for directly calling tools the agent was initialized with by calling `agent.tool.<tool_name>(<tool_params>)`. Direct tool calls are great for giving the agent information from a tool without needing the agent to invoke that tool itself. We can use this direct tool invocation to list the current appointments:"]}, {"cell_type": "code", "execution_count": null, "id": "924e5b2f-a8bb-415c-b255-1da6403f6b8a", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["list_appointments_result = agent.tool.list_appointments()\n", "print(json.dumps(list_appointments_result, indent=2))"]}, {"cell_type": "markdown", "id": "c528d7db", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["We can see that the result of executing the tool is in the ToolResult format, including a `toolUseId`, an execution `status`, and the `content` of the response. We can better visualize the tool's result like this:"]}, {"cell_type": "code", "execution_count": null, "id": "bbc4dfc9", "metadata": {"pycharm": {"name": "#%% md\n"}}, "outputs": [], "source": ["list_appointments_result_text_content = list_appointments_result[\"content\"][0][\"text\"]\n", "print(json.dumps(json.loads(list_appointments_result_text_content), indent=2))"]}, {"cell_type": "markdown", "id": "b4b05375", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Finally, when executing tools using direct tool invocation, the agent records these executions in its messages history. By default this is enabled, but can be disabled with the `record_direct_tool_call` boolean flag attribute on the `Agent` class."]}, {"cell_type": "code", "execution_count": null, "id": "a1a70b75", "metadata": {"pycharm": {"name": "#%% md\n"}}, "outputs": [], "source": ["current_time_result = agent.tool.current_time()\n", "print(\"Current Time direct tool call result:\")\n", "print(current_time_result)\n", "current_time_direct_tool_messages = agent.messages[-4:]\n", "print(\"Current Time direct tool call messages:\")\n", "print(current_time_direct_tool_messages)\n", "\n", "agent.record_direct_tool_call = False # Set the record_direct_tool_call to False\n", "agent.tool.list_appointments()\n", "after_disable_record_messages = agent.messages[-4:]\n", "print(\"After disabling record direct tool call messages, history should not have changed:\")\n", "print(current_time_direct_tool_messages == after_disable_record_messages)"]}, {"cell_type": "markdown", "id": "191c283f", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## Extension: Extended Thinking\n", " \n", "Extended thinking enables supported Claude-family models the ability to leverage enhanced reasoning capabilities for complex tasks, providing transparent step-by-step thought processes before delivering final answers. To enable thinking, you can include the configuration below when configuring your Bedrock ModelProvider. You can learn more at [AWS's documentation on Extended Thinking](https://docs.aws.amazon.com/bedrock/latest/userguide/claude-messages-extended-thinking.html)."]}, {"cell_type": "code", "execution_count": null, "id": "38da1353", "metadata": {"pycharm": {"name": "#%% md\n"}}, "outputs": [], "source": ["thinking_model = BedrockModel(\n", "    model_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    additional_request_fields={\n", "        \"thinking\": {\n", "            \"type\": \"enabled\",\n", "            \"budget_tokens\": 2048,\n", "        }\n", "    },\n", ")"]}, {"cell_type": "markdown", "id": "3ef66fab", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["After defining the `thinking_model`, you can create and invoke a new `thinking_agent`:"]}, {"cell_type": "code", "execution_count": null, "id": "4f108899", "metadata": {"pycharm": {"name": "#%% md\n"}}, "outputs": [], "source": ["thinking_system_prompt = \"\"\"You are a helpful personal assistant that specializes in managing my appointments and calendar. \n", "You have access to appointment management tools, a calculator, and can check the current time to help me organize my schedule effectively. \n", "You think through your problem, step by step, to come up with an answer.\n", "Always provide the appointment id so that I can update it if required\"\"\"\n", "\n", "thinking_agent = Agent(\n", "    model=thinking_model,\n", "    system_prompt=thinking_system_prompt,\n", "    tools=[\n", "        current_time,\n", "        calculator,\n", "        create_appointment,\n", "        list_appointments,\n", "        update_appointment,\n", "    ],\n", ")\n", "\n", "thinking_result = thinking_agent(\"I want to add a new appointment for tomorrow at 2pm\")"]}, {"cell_type": "markdown", "id": "feba72d2", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["We can better analyze the extended thinking capabilities by printing out the agent's messages. Extended thinking is represented as `reasoningContent` blocks in the response from the agent."]}, {"cell_type": "code", "execution_count": null, "id": "9d1b3a6e-7253-4162-9b50-814d7700eaca", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["thinking_agent.messages"]}, {"cell_type": "markdown", "id": "c7bfbe54-a3fd-4b55-b11f-537b1763d0ee", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## Great work !\n", "See you in the next module. :)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}