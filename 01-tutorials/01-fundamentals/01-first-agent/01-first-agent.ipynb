{"cells": [{"cell_type": "markdown", "id": "8e394456", "metadata": {}, "source": ["# Quickstart Guide for Strands Agents\n", "\n", "Strands Agents is a powerful framework for building AI agents that can interact with AWS services and perform complex tasks. This quickstart guide will help you get started with creating your first Strands agent.\n", "\n", "## Prerequisites\n", "\n", "- Python 3.10 or later\n", "- AWS account configured with appropriate permissions\n", "- Basic understanding of Python programming\n", "\n", "Lets get started !"]}, {"cell_type": "code", "execution_count": null, "id": "5f7b62fd", "metadata": {}, "outputs": [], "source": ["# Install Strands using pip\n", "\n", "!pip install strands-agents strands-agents-tools"]}, {"cell_type": "markdown", "id": "79a45632", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["\n", "## Creating Your First Agent\n", "\n", "Lets get an overview of the agentic components needed.\n", "\n", "### 1. Create a simple Agent:\n", "\n", "This will create an agent with the default model provider, [Amazon Bedrock](https://aws.amazon.com/bedrock/), and the default model, Claude 3.7 Sonnet, in the region of your AWS setup. While the agent runs in the same local environment as it is being invoked, Amazon Bedrock models will run in an AWS account and your agent will invoke the model in the cloud account. The architecture looks as following:\n", "\n", "<div style=\"text-align:center\">\n", "    <img src=\"images/simple_agent.png\" width=\"75%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "8d549346", "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(action=\"ignore\", message=r\"datetime.datetime.utcnow\") \n", "\n", "from strands import Agent\n", "# Initialize your agent\n", "agent = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-********-v1:0\",  # Optional: Specify the model ID\n", "    system_prompt=\"You are a helpful assistant that provides concise responses.\"\n", ")\n", "\n", "# Send a message to the agent\n", "response = agent(\"Hello! Tell me a joke.\")"]}, {"cell_type": "markdown", "id": "cc305d76", "metadata": {}, "source": ["### 2. Add <PERSON>ls to the Agent:\n", "\n", "The [strands-agents-tools](https://github.com/strands-agents/tools) repository provides some in-built tools which you can import. You can also create custom tools using the `@tool` decorator. We can create agents with built-in and custom tools. For instance, adding the built-in tool of a calculator and a custom tool for getting the weather you get the following architecture:\n", "<div style=\"text-align:center\">\n", "    <img src=\"images/agent_with_tools.png\" width=\"75%\" />\n", "</div>\n", "\n", "Implementing this architecture you have the following:"]}, {"cell_type": "code", "execution_count": null, "id": "c84d9466", "metadata": {}, "outputs": [], "source": ["from strands import Agent, tool\n", "from strands_tools import calculator # Import the calculator tool\n", "\n", "# Create a custom tool \n", "@tool\n", "def weather():\n", "    \"\"\" Get weather \"\"\" # Dummy implementation\n", "    return \"sunny\"\n", "\n", "agent = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-********-v1:0\",  # Optional: Specify the model ID\n", "    tools=[calculator, weather],\n", "    system_prompt=\"You're a helpful assistant. You can do simple math calculation, and tell the weather.\")\n", "\n", "response = agent(\"What is the weather today?\")\n", "print(response)"]}, {"cell_type": "markdown", "id": "c96537ad-a5af-49d9-aaff-c9101ae31e73", "metadata": {}, "source": ["### Invoking tool directly\n", "\n", "For some applications it is important to directly call the tool. For instance, you might want to debug the tool, pre-populate the agent knowledge with your customer's information or using a tool inside of another tool. In Strands you can do it using the ``tool`` method of your agent followed by the tool name"]}, {"cell_type": "code", "execution_count": null, "id": "7743fe69", "metadata": {}, "outputs": [], "source": ["# Alternatively, you can invoke the tool directly like so:\n", "agent.tool.calculator(expression=\"sin(x)\", mode=\"derive\", wrt=\"x\", order=2)"]}, {"cell_type": "markdown", "id": "1f6bceed", "metadata": {}, "source": ["\n", "### 3. Changing the log level and format:\n", "\n", "Strands SDK uses Python's standard `logging` module to provide visibility into its operations.\n", "\n", "The Strands Agents SDK implements a straightforward logging approach:\n", "\n", "1. **Module-level Loggers**: Each module in the SDK creates its own logger using logging.getLogger(__name__), following Python best practices for hierarchical logging.\n", "2. **Root Logger**: All loggers in the SDK are children of the \"strands\" root logger, making it easy to configure logging for the entire SDK.\n", "3. **Default Behavior**: By default, the SDK doesn't configure any handlers or log levels, allowing you to integrate it with your application's logging configuration.\n", "\n", "To enable logging for the Strands Agents SDK, you can configure the **\"strands\"** logger. If you want to change the log level, for example during debugging, or modify the log format, you can set the logger configuration as follows:"]}, {"cell_type": "code", "execution_count": null, "id": "66415155", "metadata": {}, "outputs": [], "source": ["import logging\n", "from strands import Agent\n", "\n", "# Enables Strands debug log level\n", "logging.getLogger(\"strands\").setLevel(logging.DEBUG) # or logging.INFO\n", "\n", "# Sets the logging format and streams logs to stderr\n", "logging.basicConfig(\n", "    format=\"%(levelname)s | %(name)s | %(message)s\",\n", "    handlers=[logging.StreamHandler()]\n", ")\n", "\n", "agent = Agent(model=\"us.anthropic.claude-3-7-sonnet-********-v1:0\")  # Optional: Specify the model ID\n", "agent(\"Hello!\")"]}, {"cell_type": "markdown", "id": "7c5ddfed", "metadata": {}, "source": ["\n", "### 4. Model Provider\n", "\n", "The default model provider is [Amazon Bedrock](https://aws.amazon.com/bedrock/) and the default model is Claude 3.7 Sonnet in the region of your current AWS environment\n", "\n", "You can specify a different model in Amazon Bedrock providing the model ID string directly:"]}, {"cell_type": "code", "execution_count": null, "id": "207432b2", "metadata": {}, "outputs": [], "source": ["from strands import Agent\n", "\n", "agent = Agent(model=\"anthropic.claude-3-5-haiku-20241022-v1:0\")\n", "print(agent.model.config)"]}, {"cell_type": "markdown", "id": "88b79bb2", "metadata": {}, "source": ["\n", "For more control over the model configuration, you can create a `BedrockModel` provider instance:"]}, {"cell_type": "code", "execution_count": null, "id": "13767dd5", "metadata": {}, "outputs": [], "source": ["import boto3\n", "from strands import Agent\n", "from strands.models import BedrockModel\n", "\n", "# Create a BedrockModel\n", "bedrock_model = BedrockModel(\n", "    model_id=\"anthropic.claude-3-5-haiku-20241022-v1:0\",\n", "    region_name='us-west-2',\n", "    temperature=0.3,\n", ")\n", "\n", "agent = Agent(model=bedrock_model)"]}, {"cell_type": "markdown", "id": "538e5e20", "metadata": {}, "source": ["\n", "More details on the available model providers on the [Model Provider Quickstart page](https://strandsagents.com/0.1.x/user-guide/quickstart/#model-providers)\n"]}, {"cell_type": "markdown", "id": "037ba1d7", "metadata": {}, "source": ["**Congratulations !! Now you have learned how to build a simple agent using Strands!!**"]}, {"cell_type": "markdown", "id": "8ad2d9c3", "metadata": {}, "source": ["## [Optional] Lets Build a Task-Specific Agent - RecipeBot 🍽️\n", "\n", "Lets create a practical example of a task-specific agent. We create a `RecipeBot` that recommends recipes and answers any cooking related questions. Lets dive in !!\n", "\n", "Here's what we will create :"]}, {"cell_type": "markdown", "id": "bff74b3f", "metadata": {}, "source": ["<div style=\"text-align:center\">\n", "    <img src=\"images/interactive_recipe_agent.png\" width=\"75%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "c4d5761c", "metadata": {}, "outputs": [], "source": ["# Install the required packages\n", "%pip install ddgs # Also install strands-agents strands-agents-tools if you haven't already"]}, {"cell_type": "code", "execution_count": null, "id": "580312ca", "metadata": {}, "outputs": [], "source": ["from strands import Agent, tool\n", "from ddgs import DDGS\n", "from ddgs.exceptions import RatelimitException, DDGSException\n", "import logging\n", "\n", "# Configure logging\n", "logging.getLogger(\"strands\").setLevel(logging.INFO)\n", "\n", "# Define a websearch tool\n", "@tool\n", "def websearch(keywords: str, region: str = \"us-en\", max_results: int | None = None) -> str:\n", "    \"\"\"Search the web to get updated information.\n", "    Args:\n", "        keywords (str): The search query keywords.\n", "        region (str): The search region: wt-wt, us-en, uk-en, ru-ru, etc..\n", "        max_results (int | None): The maximum number of results to return.\n", "    Returns:\n", "        List of dictionaries with search results.\n", "    \"\"\"\n", "    try:\n", "        results = DDGS().text(keywords, region=region, max_results=max_results)\n", "        return results if results else \"No results found.\"\n", "    except RatelimitException:\n", "        return \"RatelimitException: Please try again after a short delay.\"\n", "    except DDGSException as d:\n", "        return f\"DuckDuckGoSearchException: {d}\"\n", "    except Exception as e:\n", "        return f\"Exception: {e}\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "0560c754", "metadata": {}, "outputs": [], "source": ["# Create a recipe assistant agent\n", "recipe_agent = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-********-v1:0\",  # Optional: Specify the model ID\n", "    system_prompt=\"\"\"You are <PERSON><PERSON><PERSON><PERSON><PERSON>, a helpful cooking assistant.\n", "    Help users find recipes based on ingredients and answer cooking questions.\n", "    Use the websearch tool to find recipes when users mention ingredients or to\n", "    look up cooking information.\"\"\",\n", "    # Import the websearch tool we created above\n", "    tools=[websearch],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6a6dafb2", "metadata": {}, "outputs": [], "source": ["response = recipe_agent(\"Suggest a recipe with chicken and broccoli.\")\n", "\n", "# Other examples:\n", "# response = recipe_agent(\"How do I cook quinoa?\")\n", "# response = recipe_agent(\"How can I substitute white wine in shrimp pasta?\")\n", "# response = recipe_agent(\"What are the health benefits of asparagus?\")\n", "print(response)"]}, {"cell_type": "markdown", "id": "9f92c8a4", "metadata": {}, "source": ["#### And that's it! We now have a running usecase agent with tools in just a few lines of code 🥳.\n", "\n", "For more detailed quickstart guide, check out the [Strands documentation](https://strandsagents.com/0.1.x/user-guide/quickstart/)."]}, {"cell_type": "markdown", "id": "0cae9558", "metadata": {}, "source": ["### [Optional] Run RecipeBot via CLI: \n", "you can run the agent in interactive mode via the command line (for instance using the terminal on SageMaker Studio) through the python script provided in `02_simple_interactive_usecase/recipe_bot.py`. This allows you to interact with the agent in a more dynamic way, sending messages and receiving responses via the CLI.\n", "Run these commands on a command line interface to run the agent in interactive mode:\n", "\n", "```cli\n", "cd samples/01-tutorials/01-fundamentals/01-first-agent/02-simple-interactive-usecase/\n", "pip install -r requirements.txt\n", "python recipe_bot.py\n", "```\n", "\n", "With this, you can talk to the bot via a command line interface(CLI)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}