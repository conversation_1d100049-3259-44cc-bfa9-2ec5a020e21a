{"cells": [{"cell_type": "markdown", "id": "b3fa745a-a393-498b-ab51-68804fadf751", "metadata": {}, "source": ["\n", "# Building Multi-Agent Systems with Strands Agent Graph\n", "Multi-agent systems leverage multiple specialized AI agents working together to solve complex problems through coordinated collaboration. Each agent has specific capabilities and roles, connected through explicit communication pathways.\n", "\n", "In this lab, you'll learn to build multi-agent systems using the Strands Agent SDK. We'll progress from basic concepts to advanced implementations, exploring different topologies and real-world applications.\n", "\n", "**Learning Objectives:**\n", "By the end of this notebook, you'll be able to:\n", "- Understand the three core components of agent graphs (nodes, edges, conditions)\n", "- Send targeted messages between specific agents\n", "- Monitor and control multi-agent networks\n", "- Design specialized agent systems for real-world scenarios\n", "\n", "## Prerequisites\n", "\n", "- Python 3.10+\n", "- AWS account with Anthropic Claude 3.7 enabled on Amazon Bedrock\n", "- IAM role with permissions to use Amazon Bedrock\n", "- Basic understanding of AI agents and prompt engineering"]}, {"cell_type": "markdown", "id": "afc1a597-0490-4ab1-9324-5498b5592662", "metadata": {}, "source": ["## Setup and Installation\n", "\n", "Before we start, let's install the requirement packages for `strands-agents` and `strands-agents-tools`"]}, {"cell_type": "code", "execution_count": null, "id": "e048e8cf-3fa7-495b-a761-7999936171c5", "metadata": {}, "outputs": [], "source": ["!pip install -r requirements.txt"]}, {"cell_type": "markdown", "id": "9a203754-ecd3-402d-be98-9ee8facc23d9", "metadata": {}, "source": ["### Importing required packages\n", "\n", "Next we can import the required packages"]}, {"cell_type": "code", "execution_count": null, "id": "680c68aa-36e9-48e7-a3ed-9d2e63a3b95c", "metadata": {}, "outputs": [], "source": ["from strands import Agent"]}, {"cell_type": "markdown", "id": "cb3b24d8-69d5-496d-92a5-24ff8705de1c", "metadata": {}, "source": ["## Understanding Agent Graph Components\n", "An agent graph is a structured network of interconnected AI agents designed to solve complex problems through coordinated collaboration. Each agent represents a specialized node with specific capabilities, and the connections between agents define explicit communication pathways.\n", "\n", "Before we start building, let's understand the three primary components of an agent graph:\n", "\n", "### 1. <PERSON><PERSON> (Agents)\n", "Each node represents an AI agent with:\n", "- **Identity**: Unique identifier within the graph\n", "- **Role**: Specialized function or purpose\n", "- **System Prompt**: Instructions defining the agent's behavior\n", "- **Tools**: Capabilities available to the agent\n", "\n", "### 2. <PERSON><PERSON> (Connections)\n", "Edges define communication pathways with:\n", "- **Direction**: One-way or bidirectional information flow\n", "- **Condition**: Optional function that determines if the edge should be traversed\n", "- **Dependencies**: Define execution order and data flow between nodes\n", "\n", "### 3. GraphBuilder\n", "The GraphBuilder provides a simple interface for constructing graphs:\n", "- **add_node()**: Add an agent or multi-agent system as a node\n", "- **add_edge()**: Create a dependency between nodes\n", "- **set_entry_point()**: Define starting nodes for execution\n", "- **build()**: Validate and create the Graph instance\n", "\n"]}, {"cell_type": "markdown", "id": "80c47290-ef84-4ab5-ad14-70d41f0ed40b", "metadata": {}, "source": ["### Basic processing\n", "\n", "Let's start with a simple example of one task processed by two different agents providing an output that will depend on their defined role. Take a look at the execution order of the nodes and also the fact that with STrands SDK you can explicitly get a response only from one single node if needed. Architecture looks as following:\n", "\n", "<div style=\"text-align:left\">\n", "    <img src=\"images/basic.png\" width=\"55%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "eba5fc53-8eca-46e8-84cf-9ca0f551140d", "metadata": {}, "outputs": [], "source": ["#Initialize an agent with agent_graph capability\n", "from strands.multiagent import GraphBuilder\n", "\n", "# Create specialized agents\n", "coordinator = Agent(name=\"coordinator\", system_prompt=\"You are a research team leader coordinating specialists. Provide a short analysis, no need for follow ups\")\n", "analyst = Agent(name=\"data_analyst\", system_prompt=\"You are a data analyst specializing in statistical analysis. Provide a short analysis, no need for follow ups\")\n", "domain_expert = Agent(name=\"domain_expert\", system_prompt=\"You are a domain expert with deep subject knowledge. Provide a short analysis, no need for follow ups\")\n", "\n", "# Build the graph\n", "builder = GraphBuilder()\n", "\n", "# Add nodes\n", "builder.add_node(coordinator, \"team_lead\")\n", "builder.add_node(analyst, \"analyst\")\n", "builder.add_node(domain_expert, \"expert\")\n", "\n", "# Add edges (dependencies)\n", "builder.add_edge(\"team_lead\", \"analyst\")\n", "builder.add_edge(\"team_lead\", \"expert\")\n", "\n", "# Set entry points (optional - will be auto-detected if not specified)\n", "builder.set_entry_point(\"team_lead\")\n", "\n", "# Build the graph\n", "graph = builder.build()\n", "\n", "#Execute task on newly built graph\n", "result = graph(\"Analyze the impact of remote work on employee productivity.Provide a short analysis, no need for follow ups\")\n", "print(\"\\n\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "\n", "print(f\"Response: {result}\")\n", "\n", "print(\"=============Node execution order:==========================\")\n", "print(\"============================================================\")\n", "\n", "# See which nodes were executed and in what order\n", "for node in result.execution_order:\n", "    print(f\"Executed: {node.node_id}\")\n", "\n", "print(\"=============Graph metrics:=================================\")\n", "print(\"============================================================\")\n", "\n", "\n", "# Get performance metrics\n", "print(f\"Total nodes: {result.total_nodes}\")\n", "print(f\"Completed nodes: {result.completed_nodes}\")\n", "print(f\"Failed nodes: {result.failed_nodes}\")\n", "print(f\"Execution time: {result.execution_time}ms\")\n", "print(f\"Token usage: {result.accumulated_usage}\")\n", "\n", "\n", "# Get results from specific nodes\n", "print(\"\\n\")\n", "print(\"=============Expert node results only:======================\")\n", "print(\"============================================================\")\n", "print(result.results[\"expert\"].result)"]}, {"cell_type": "markdown", "id": "de57dc8d-d748-4dc4-8380-ed789ba84d6b", "metadata": {}, "source": ["### Parallel processing\n", "\n", "Now let's create a topology when we will have 2 agents processing the request looking at 2 different aspect  of the problem and have them input into a final agent responsible for summarization and risk calculation based on provided input \n", "<div style=\"text-align:left\">\n", "    <img src=\"images/parallel.png\" width=\"55%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "7926cfeb-90c1-48cb-8d61-850ade47aebf", "metadata": {}, "outputs": [], "source": ["#Initialize an agent with agent_graph capability\n", "from strands.multiagent import GraphBuilder\n", "\n", "mesh_agent = Agent()\n", "# Create specialized agents\n", "\n", "financial_advisor = Agent(name=\"financial_advisor\", system_prompt=\"You are a financial advisor focused on cost-benefit analysis, budget implications, and ROI calculations. Engage with other experts to build comprehensive financial perspectives.\")\n", "technical_architect = Agent(name=\"technical_architect\", system_prompt=\"You are a technical architect who evaluates feasibility, implementation challenges, and technical risks. Collaborate with other experts to ensure technical viability.\")\n", "market_researcher = Agent(name=\"market_researcher\", system_prompt=\"You are a market researcher who analyzes market conditions, user needs, and competitive landscape. Work with other experts to validate market opportunities.\")\n", "risk_analyst = Agent(name=\"risk_analyst\", system_prompt=\"You are a risk analyst who identifies potential risks, mitigation strategies, and compliance issues. Collaborate with other experts to ensure comprehensive risk assessment.\")\n", "\n", "\n", "# Build the graph\n", "builder = GraphBuilder()\n", "\n", "# Add nodes\n", "builder.add_node(financial_advisor, \"finance_expert\")\n", "builder.add_node(technical_architect, \"tech_expert\")\n", "builder.add_node(market_researcher, \"market_expert\")\n", "builder.add_node(risk_analyst, \"risk_analyst\")\n", "\n", "# Add edges (dependencies)\n", "builder.add_edge(\"finance_expert\", \"tech_expert\")\n", "builder.add_edge(\"finance_expert\", \"market_expert\")\n", "builder.add_edge(\"tech_expert\", \"risk_analyst\")\n", "builder.add_edge(\"market_expert\", \"risk_analyst\")\n", "\n", "\n", "# Set entry points (optional - will be auto-detected if not specified)\n", "builder.set_entry_point(\"finance_expert\")\n", "\n", "# Build the graph\n", "graph = builder.build()\n", "\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "\n", "#Execute task on newly built graph\n", "result = graph(\"Our company is considering launching a new AI-powered customer service platform. Initial investment is \\$2M with projected 3-year ROI of 150%. What's your financial assessment?\")\n", "print(\"\\n\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "\n", "print(f\"Response: {result}\")\n", "\n", "print(\"=============Node execution order:==========================\")\n", "print(\"============================================================\")\n", "\n", "# See which nodes were executed and in what order\n", "for node in result.execution_order:\n", "    print(f\"Executed: {node.node_id}\")\n", "\n", "print(\"=============Graph metrics:=================================\")\n", "print(\"============================================================\")\n", "\n", "\n", "# Get performance metrics\n", "print(f\"Total nodes: {result.total_nodes}\")\n", "print(f\"Completed nodes: {result.completed_nodes}\")\n", "print(f\"Failed nodes: {result.failed_nodes}\")\n", "print(f\"Execution time: {result.execution_time}ms\")\n", "print(f\"Token usage: {result.accumulated_usage}\")\n", "\n", "\n", "# Get results from specific nodes\n", "\n", "print(\"Financial Advisor:\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "print(result.results[\"finance_expert\"].result)\n", "print(\"\\n\")\n", "\n", "print(\"Technical Expert:\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "print(result.results[\"tech_expert\"].result)\n", "print(\"\\n\")\n", "\n", "print(\"Market Researcher:\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "print(result.results[\"market_expert\"].result)\n", "print(\"\\n\")"]}, {"cell_type": "markdown", "id": "ae0eb88e-fdc1-4387-8eb0-4a9ae50ca2ea", "metadata": {}, "source": ["### Branching with conditions\n", "\n", "Let's create an agent graph that would classify the request and depending on conditions we define in the code - will route the request either to technical or business agent.\n", "\n", "Take a close look on differences between the node execution order and number of nodes executed in this graph based on two different prompts.\n", "\n", "<div style=\"text-align:left\">\n", "    <img src=\"images/conditional.png\" width=\"55%\" />\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "5058e219-8fb8-4503-8bbf-9e69dd06cf62", "metadata": {}, "outputs": [], "source": ["#Initialize an agent with agent_graph capability\n", "from strands.multiagent import GraphBuilder\n", "\n", "mesh_agent = Agent()\n", "# Create specialized agents\n", "\n", "classifier = Agent(name=\"classifier\", system_prompt=\"You are an agent responsible for classification of the report request, return only Technical or Business clasification.\")\n", "technical_report = Agent(name=\"technical_expert\", system_prompt=\"You are a technical expert htat focuses on providing short summary from technical perspective\")\n", "business_report = Agent(name=\"business_expert\", system_prompt=\"You are a business expert that focuses on providing short summary from business perspective\")\n", "\n", "# Build the graph\n", "builder = GraphBuilder()\n", "\n", "# Add nodes\n", "builder.add_node(classifier, \"classifier\")\n", "builder.add_node(technical_report, \"technical_report\")\n", "builder.add_node(business_report, \"business_report\")\n", "\n", "def is_technical(state):\n", "    classifier_result = state.results.get(\"classifier\")\n", "    if not classifier_result:\n", "        return False\n", "    result_text = str(classifier_result.result)\n", "    return \"technical\" in result_text.lower()\n", "\n", "def is_business(state):\n", "    classifier_result = state.results.get(\"classifier\")\n", "    if not classifier_result:\n", "        return False\n", "    result_text = str(classifier_result.result)\n", "    return \"business\" in result_text.lower()\n", "\n", "# Add edges (dependencies)\n", "builder.add_edge(\"classifier\", \"technical_report\", condition=is_technical)\n", "builder.add_edge(\"classifier\", \"business_report\", condition=is_business)\n", "\n", "# Set entry points (optional - will be auto-detected if not specified)\n", "builder.set_entry_point(\"classifier\")\n", "\n", "# Build the graph\n", "graph = builder.build()\n", "\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "\n", "#Execute task on newly built graph\n", "result = graph(\"Provide report on technical aspect of working from home, outline things to consider and key risk factors\")\n", "print(\"\\n\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "\n", "print(f\"Response: {result}\")\n", "\n", "print(\"=============Node execution order:==========================\")\n", "print(\"============================================================\")\n", "\n", "# See which nodes were executed and in what order\n", "for node in result.execution_order:\n", "    print(f\"Executed: {node.node_id}\")\n", "\n", "print(\"=============Graph metrics:=================================\")\n", "print(\"============================================================\")\n", "\n", "\n", "# Get performance metrics\n", "print(f\"Total nodes: {result.total_nodes}\")\n", "print(f\"Completed nodes: {result.completed_nodes}\")\n", "print(f\"Failed nodes: {result.failed_nodes}\")\n", "print(f\"Execution time: {result.execution_time}ms\")\n", "print(f\"Token usage: {result.accumulated_usage}\")\n", "\n", "# Get results from specific nodes\n", "\n", "print(\"Classifier:\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "print(result.results[\"classifier\"].result)\n", "print(\"\\n\")\n", "\n", "#Execute task on newly built graph\n", "result = graph(\"Provide report on business impact of working from home, outline things to consider and key risk factors\")\n", "print(\"\\n\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "\n", "print(f\"Response: {result}\")\n", "\n", "print(\"=============Node execution order:==========================\")\n", "print(\"============================================================\")\n", "\n", "# See which nodes were executed and in what order\n", "for node in result.execution_order:\n", "    print(f\"Executed: {node.node_id}\")\n", "\n", "print(\"=============Graph metrics:=================================\")\n", "print(\"============================================================\")\n", "\n", "\n", "# Get performance metrics\n", "print(f\"Total nodes: {result.total_nodes}\")\n", "print(f\"Completed nodes: {result.completed_nodes}\")\n", "print(f\"Failed nodes: {result.failed_nodes}\")\n", "print(f\"Execution time: {result.execution_time}ms\")\n", "print(f\"Token usage: {result.accumulated_usage}\")\n", "\n", "# Get results from specific nodes\n", "\n", "print(\"Classifier:\")\n", "print(\"============================================================\")\n", "print(\"============================================================\")\n", "print(result.results[\"classifier\"].result)\n", "print(\"\\n\")"]}, {"cell_type": "markdown", "id": "6d35f546-fe6a-4ce2-acf5-4ed8690c593d", "metadata": {}, "source": ["## Key Takeaways and Best Practices\n", "\n", "\n", "### Best Practices:\n", "\n", "**Design for acyclicity:** Ensure your graph has no cycles</p>\n", "**Use meaningful node IDs:** Choose descriptive names for nodes</p>\n", "**Validate graph structure:** The builder will check for cycles and validate entry points</p>\n", "**Handle node failures:** Consider how failures in one node affect the overall workflow</p>\n", "**Use conditional edges:** For dynamic workflows based on intermediate results</p>\n", "**Consider parallelism:** Independent branches can execute concurrently</p>\n", "**Nest multi-agent patterns:** Use Swarms within Graphs for complex workflows</p>\n", "**Leverage multi-modal inputs:** Use ContentBlocks for rich inputs including images</p>\n", "\n", "## Conclusion\n", "\n", "You've now mastered the fundamentals of building multi-agent systems with Strands Agent G<PERSON>h! You can create sophisticated networks of specialized AI agents that collaborate to solve complex problems.\n", "\n", "The key to successful multi-agent systems is:\n", "- Matching topology to use case\n", "- Defining clear agent roles and responsibilities  \n", "- Establishing proper communication patterns\n", "- Managing resources and cleanup effectively\n", "\n", "From here, you can build increasingly sophisticated systems for real-world applications in research, content creation, decision-making, customer service, and beyond."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}