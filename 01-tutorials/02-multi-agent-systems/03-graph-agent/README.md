### What is an Agent Graph?

An Agent Graph is a collection of AI agents organized in a specific topology where:

- **Nodes**: Individual agents with specific roles, identity, tools and system prompts
- **Edges**: Communication paths between agents
- **Conditional Edges**: Conditional logic for edges to create dynamic workflows



### Example

To get started with building agents with these patterns. Navigate to `graph.ipynb` to build a graph using the **star topology**.