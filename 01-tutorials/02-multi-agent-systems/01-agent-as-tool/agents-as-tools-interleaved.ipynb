{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Agents as Tools with Strands Agents SDK and Claude 4 Interleaved Thinking\n", "\n", "This notebook demonstrates how to use Strands Agents SDK with Claude 4's **interleaved thinking** capability to orchestrate intelligent workflows with specialist agents."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Understanding Interleaved Thinking\n", "\n", "### What is Interleaved Thinking?\n", "\n", "Interleaved thinking is a new capability in Claude 4 models that allows the model to:\n", "\n", "1. **Think between tool calls**: Process and reason about results before deciding next steps\n", "2. **Chain multiple tools with reasoning**: Make sophisticated multi-step decisions\n", "3. **Adapt strategies dynamically**: Change approach based on intermediate results\n", "\n", "### How It Works\n", "\n", "There are a lot of similarities between <PERSON>'s event loop implemented with and without interleaved thinking:\n", "```\n", "Query → LLM is thinking -> <PERSON><PERSON> decides to call a Tool -> Event Loop calls the Tool -> Ouput is sent back to LLM -> [ this continues until LLM no longer needs to call any tools - it rendered the Final Answer ]\n", "```\n", "\n", "The main difference you'll notice with the interleaved thinking is that Event loop is acting on LLM's \"thoughts\", rather than \"decisions\". Notice the second link in the loop above, called \"thinking\". In a traditional event loop, the thoughts are hidden. We have to wait until LLM renders either a decision to call a tool or produces the Final Answer. \n", "\n", "In case of interleaved thinking, LLM is \"leaking\" its thoughts into the even loop while it's still in that second step - \"LLM is thinking\" - and event loop is configured to executed the tools as soon as LLM \"thinks\" about doing it. What this means is that by the time LLM is done thinking, it actually has the Final Answer, on the very first \"decision\". \n", "\n", "\n", "### Enabling Interleaved Thinking\n", "\n", "To enable this feature with Strands and Bedrock:\n", "- Set `temperature=1` (required when thinking is enabled)\n", "- Add beta header: `\"anthropic_beta\": [\"interleaved-thinking-2025-05-14\"]`\n", "- Configure reasoning budget: `\"reasoning_config\": {\"type\": \"enabled\", \"budget_tokens\": 3000}`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from strands import Agent, tool\n", "from strands.models import BedrockModel\n", "from strands.models import bedrock\n", "\n", "bedrock.DEFAULT_BEDROCK_MODEL_ID = \"us.anthropic.claude-3-7-sonnet-20250219-v1:0\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Specialist Agents as <PERSON><PERSON>\n", "\n", "First, we are going to create four specialist agents using the Strands `@tool` decorator:\n", "- **Researcher**: <PERSON><PERSON>s factual information\n", "- **Data Analyst**: Processes and analyzes information\n", "- **Fact Checker**: Verifies information accuracy\n", "- **Report Writer**: Creates polished final documents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Specialist agents implemented as tools using Strands @tool decorator\n", "@tool\n", "def researcher(query: str) -> str:\n", "    \"\"\"\n", "    Research specialist that gathers factual information.\n", "    \n", "    Args:\n", "        query: Research question or topic to investigate\n", "        \n", "    Returns:\n", "        Research findings and sources\n", "    \"\"\"\n", "    # Create a focused research agent\n", "    # Note: Each call creates a fresh agent instance (stateless)\n", "    research_agent = Agent(\n", "        model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",  # Optional: Specify the model ID\n", "        system_prompt=\"You are a research specialist. Gather factual information and cite sources when possible. Keep responses under 200 words.\",\n", "        callback_handler=None  # No streaming for tool agents\n", "    )\n", "    \n", "    # Execute the research task\n", "    result = research_agent(f\"Research: {query}\")\n", "    return str(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@tool\n", "def data_analyst(data: str) -> str:\n", "    \"\"\"\n", "    Data analyst that processes and analyzes information.\n", "    \n", "    Args:\n", "        data: Raw data or research findings to analyze\n", "        \n", "    Returns:\n", "        Analysis with insights and patterns\n", "    \"\"\"\n", "    # Analyst agent focuses on extracting insights\n", "    analysis_agent = Agent(\n", "        model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "        system_prompt=\"You are a data analyst. Extract key insights, identify patterns, and provide analytical conclusions. Focus on actionable insights.\",\n", "        callback_handler=None\n", "    )\n", "    \n", "    # Analyze the provided data\n", "    result = analysis_agent(f\"Analyze this data and provide insights: {data}\")\n", "    return str(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@tool\n", "def fact_checker(information: str) -> str:\n", "    \"\"\"\n", "    Fact checker that verifies information accuracy.\n", "    \n", "    Args:\n", "        information: Claims or data to verify\n", "        \n", "    Returns:\n", "        Fact-check results with accuracy assessment\n", "    \"\"\"\n", "    # Fact-checking agent for verification\n", "    fact_check_agent = Agent(\n", "        model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "        system_prompt=\"You are a fact checker. Verify claims, assess credibility, and provide confidence levels. Identify any questionable statements.\",\n", "        callback_handler=None\n", "    )\n", "    \n", "    # Verify the information\n", "    result = fact_check_agent(f\"Fact-check this information: {information}\")\n", "    return str(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@tool\n", "def report_writer(analysis: str) -> str:\n", "    \"\"\"\n", "    Report writer that creates polished final documents.\n", "    \n", "    Args:\n", "        analysis: Analyzed data and insights\n", "        \n", "    Returns:\n", "        Formatted final report\n", "    \"\"\"\n", "    # Writer agent for professional output\n", "    writer_agent = Agent(\n", "        system_prompt=\"You are a professional report writer. Create clear, well-structured reports with executive summaries and actionable recommendations.\",\n", "        callback_handler=None\n", "    )\n", "    \n", "    # Create the report\n", "    result = writer_agent(f\"Create a professional report based on: {analysis}\")\n", "    return str(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON> 4 Orchestrator with Interleaved Thinking\n", "\n", "Now we create the orchestrator - a Claude 4 agent that uses interleaved thinking to intelligently coordinate the specialist agents.\n", "\n", "### How the Orchestrator Works:\n", "\n", "1. Receives a high-level task from the user\n", "2. **Thinks** about what information is needed\n", "3. Calls the researcher tool to gather initial data\n", "4. **Thinks** about the research results and what analysis is needed\n", "5. Calls the data analyst to process findings\n", "6. **Thinks** about accuracy and verification needs\n", "7. May call the fact checker if needed\n", "8. **Thinks** about how to present the findings\n", "9. Calls the report writer for final output\n", "10. **Reflects** on the complete workflow before responding"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Claude 4 Orchestrator with Interleaved Thinking using Strands\n", "class StrandsInterlevedWorkflowOrchestrator:\n", "    def __init__(self):\n", "        # Define the orchestrator system prompt for intelligent workflow coordination\n", "        self.system_prompt = \"\"\"You are an intelligent workflow orchestrator with access to specialist agents.\n", "\n", "        Your role is to intelligently coordinate a workflow using these specialist agents:\n", "        - researcher: <PERSON>athers factual information on any topic\n", "        - data_analyst: Analyzes data and extracts insights\n", "        - fact_checker: Verifies accuracy of information  \n", "        - report_writer: Creates polished final reports\n", "\n", "        \"\"\"\n", "    \n", "    def run_workflow(self, task: str, enable_interleaved_thinking: bool = True) -> str:\n", "        \"\"\"Execute an intelligent workflow for the given task.\n", "        \n", "        Args:\n", "            task: The task to complete\n", "            enable_interleaved_thinking: Whether to enable interleaved thinking (default: True)\n", "        \n", "        The orchestrator will:\n", "        1. Understand the task requirements\n", "        2. Think about the best approach\n", "        3. Coordinate specialist agents\n", "        4. Reflect on results between steps\n", "        5. Produce a comprehensive output\n", "        \"\"\"\n", "        thinking_mode = \"WITH interleaved thinking\" if enable_interleaved_thinking else \"WITHOUT interleaved thinking\"\n", "        print(f\"\\nStarting intelligent workflow {thinking_mode} for: {task}\")\n", "        print(\"=\" * 70)\n", "        \n", "        # Configure Claude 4 with or without interleaved thinking via Bedrock\n", "        if enable_interleaved_thinking:\n", "            claude4_model = BedrockModel(\n", "                model_id=\"us.anthropic.claude-sonnet-4-20250514-v1:0\",\n", "                max_tokens=4096,\n", "                temperature=1,  # Required to be 1 when thinking is enabled\n", "                additional_request_fields={\n", "                    # Enable interleaved thinking beta feature\n", "                    \"anthropic_beta\": [\"interleaved-thinking-2025-05-14\"],\n", "                    # Configure reasoning parameters\n", "                    \"reasoning_config\": {\n", "                        \"type\": \"enabled\",  # Turn on thinking\n", "                        \"budget_tokens\": 3000  # Thinking token budget\n", "                    }\n", "                }\n", "            )\n", "        else:\n", "            claude4_model = BedrockModel(\n", "                model_id=\"us.anthropic.claude-sonnet-4-20250514-v1:0\",\n", "                max_tokens=4096,\n", "                temperature=1\n", "            )\n", "        \n", "        # Create the orchestrator agent with Claude 4 and specialist tools\n", "        orchestrator = Agent(\n", "            model=claude4_model,\n", "            system_prompt=self.system_prompt,\n", "            tools=[researcher, data_analyst, fact_checker, report_writer]\n", "        )\n", "        \n", "        prompt = f\"\"\"Complete this task using intelligent workflow coordination: {task}\n", "\n", "        Instructions:\n", "        1. Think carefully about what information you need to accomplish this task\n", "        2. Use the specialist agents strategically - each has unique strengths\n", "        3. After each tool use, reflect on the results and adapt your approach\n", "        4. Coordinate multiple agents as needed for comprehensive results\n", "        5. Ensure accuracy by fact-checking when appropriate\n", "        6. Provide a comprehensive final response that addresses all aspects\n", "        \n", "        Remember: Your thinking between tool calls helps you make better decisions.\n", "        Use it to plan, evaluate results, and adjust your strategy.\n", "        \"\"\"\n", "        \n", "        try:\n", "            result = orchestrator(prompt)\n", "            return str(result)\n", "        except Exception as e:\n", "            return f\"Workflow failed: {e}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the Demo\n", "\n", "Let's see the orchestrator in action! Watch how it thinking and making tool calling while it's thinking.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the orchestrator\n", "print(\"Strands Agents SDK: <PERSON> 4 Interleaved Thinking Workflow Demo\")\n", "print(\"=\" * 70)\n", "\n", "try:\n", "    orchestrator = StrandsInterlevedWorkflowOrchestrator()\n", "    print(\"✅ Orchestrator initialized successfully!\")\n", "except Exception as e:\n", "    print(f\"❌ Failed to initialize orchestrator: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the workflow with a test case\n", "test_case = \"Analyze the impact of remote work on productivity and provide strategic recommendations\"\n", "\n", "print(f\"📋 Task: {test_case}\\n\")\n", "\n", "try:\n", "    result = orchestrator.run_workflow(test_case)\n", "    \n", "    print(f\"\\n📊 Workflow Result:\")\n", "    print(\"=\" * 70)\n", "    print(result)\n", "except Exception as e:\n", "    print(f\"❌ Workflow execution failed: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Try without Interleaved Thinking\n", "\n", "Experiment with calling the orchestrator and disabling interleaved thinking. Observe the difference in the output."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Now let's try the same task WITHOUT interleaved thinking\n", "print(\"\\n\" + \"=\"*70)\n", "print(\"🔄 Now running the same task WITHOUT interleaved thinking\")\n", "print(\"=\"*70)\n", "\n", "try:\n", "    result_without_thinking = orchestrator.run_workflow(test_case, enable_interleaved_thinking=False)\n", "    \n", "    print(f\"\\n📊 Workflow Result (Without Interleaved Thinking):\")\n", "    print(\"=\" * 70)\n", "    print(result_without_thinking)\n", "except Exception as e:\n", "    print(f\"❌ Workflow execution failed: {e}\")"]}], "metadata": {"kernelspec": {"display_name": ".inter-strands", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}