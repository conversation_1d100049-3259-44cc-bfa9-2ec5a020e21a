{"cells": [{"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# Agents as Tools with Strands Agents\n"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%%\n"}}, "source": ["\"Agents as Tools\" is an architectural pattern in AI systems where specialized AI agents are wrapped as callable functions (tools) that can be used by other agents. This creates a hierarchical structure where:\n", "\n", "1. A primary \"orchestrator\" agent handles user interaction and determines which specialized agent to call\n", "\n", "2. Specialized \"tool agents\" perform domain-specific tasks when called by the orchestrator\n", "\n", "This approach mimics human team dynamics, where a manager coordinates specialists, each bringing unique expertise to solve complex problems. Rather than a single agent trying to handle everything, tasks are delegated to the most appropriate specialized agent.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["## Key Benefits and Core Principles"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["The \"Agents as Tools\" pattern offers several advantages:\n", "\n", "- Separation of concerns: Each agent has a focused area of responsibility, making the system easier to understand and maintain\n", "- Hierarchical delegation: The orchestrator decides which specialist to invoke, creating a clear chain of command\n", "- Modular architecture: Specialists can be added, removed, or modified independently without affecting the entire system\n", "- Improved performance: Each agent can have tailored system prompts and tools optimized for its specific task\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from strands import Agent, tool\n", "from strands_tools import file_write\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this module we will be creating an orchestrator based multi-agent workflow. \n", "\n", "<div style=\"text-align:left\">\n", "    <img src=\"images/architecture.png\" width=\"75%\" />\n", "</div>\n", "\n", "We will also explore `use_llm` which allows use to create nested agents."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Research Agent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lets first create a basic reasearch assistant with http_request tool. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RESEARCH_ASSISTANT_PROMPT = \"\"\"You are a specialized research assistant. Focus only on providing\n", "factual, well-sourced information in response to research questions.\n", "Always cite your sources when possible.\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["research_agent = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    system_prompt=RESEARCH_ASSISTANT_PROMPT,\n", "    # tools=[http_request]  # Here you can enable an agentic ai search tool\n", ")\n", "\n", "query = \"Overview of Amazon Bedrock and its features\"\n", "# Call the agent and return its response\n", "response = research_agent(query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can wrap this agent as a tool. Allowing other agents to interact with it. \n", "\n", "####  Best Practices for <PERSON> as <PERSON>ls\n", "\n", "When implementing the \"Agents as Tools\" pattern with Strandly AI:\n", "\n", "1. Clear tool documentation: Write descriptive docstrings that explain the agent's expertise\n", "2. Focused system prompts: Keep each specialized agent tightly focused on its domain\n", "3. Proper response handling: Use consistent patterns to extract and format responses\n", "4. Tool selection guidance: Give the orchestrator clear criteria for when to use each specialized agen"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@tool\n", "def research_assistant(query: str) -> str:\n", "    \"\"\"\n", "    Process and respond to research-related queries.\n", "\n", "    Args:\n", "        query: A research question requiring factual information\n", "\n", "    Returns:\n", "        A detailed research answer with citations\n", "    \"\"\"\n", "    try:\n", "        # Strands agents makes it easy to create a specialized agent\n", "        research_agent = Agent(\n", "            model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "            system_prompt=RESEARCH_ASSISTANT_PROMPT,\n", "        )\n", "\n", "        # Call the agent and return its response\n", "        response = research_agent(query)\n", "        return str(response)\n", "    except Exception as e:\n", "        return f\"Error in research assistant: {str(e)}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now lets follow the best practices and create `product_recommendation_assistant`, `trip_planning_assistant`, and `orchestrator` agent."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Product Recommendation Assistant"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@tool\n", "def product_recommendation_assistant(query: str) -> str:\n", "    \"\"\"\n", "    Handle product recommendation queries by suggesting appropriate products.\n", "\n", "    Args:\n", "        query: A product inquiry with user preferences\n", "\n", "    Returns:\n", "        Personalized product recommendations with reasoning\n", "    \"\"\"\n", "    try:\n", "        product_agent = Agent(\n", "            model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",  # Optional: Specify the model ID\n", "            system_prompt=\"\"\"You are a specialized product recommendation assistant.\n", "            Provide personalized product suggestions based on user preferences. Always cite your sources.\"\"\",\n", "        )\n", "        # Call the agent and return its response\n", "        response = product_agent(query)\n", "\n", "        return str(response)\n", "    except Exception as e:\n", "        return f\"Error in product recommendation: {str(e)}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["product_recommendation_assistant(\"Product recommendations for flying cars\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Trip Planning Assistant"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@tool\n", "def trip_planning_assistant(query: str) -> str:\n", "    \"\"\"\n", "    Create travel itineraries and provide travel advice.\n", "\n", "    Args:\n", "        query: A travel planning request with destination and preferences\n", "\n", "    Returns:\n", "        A detailed travel itinerary or travel advice\n", "    \"\"\"\n", "    try:\n", "        travel_agent = Agent(\n", "            model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",  # Optional: Specify the model ID\n", "            system_prompt=\"\"\"You are a specialized travel planning assistant.\n", "            Create detailed travel itineraries based on user preferences.\"\"\",\n", "        )\n", "        # Call the agent and return its response\n", "        response = travel_agent(query)\n", "\n", "        return str(response)\n", "    except Exception as e:\n", "        return f\"Error in trip planning: {str(e)}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Orchestrator Agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define orchestrator system prompt with clear tool selection guidance\n", "MAIN_SYSTEM_PROMPT = \"\"\"\n", "You are an assistant that routes queries to specialized agents:\n", "- For research questions and factual information → Use the research_assistant tool\n", "- For product recommendations and shopping advice → Use the product_recommendation_assistant tool\n", "- For travel planning and itineraries → Use the trip_planning_assistant tool\n", "- For simple questions not requiring specialized knowledge → Answer directly\n", "\n", "Always select the most appropriate tool based on the user's query.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Strands Agents allows easy integration of agent tools\n", "orchestrator = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",  # Optional: Specify the model ID\n", "    system_prompt=MAIN_SYSTEM_PROMPT,\n", "    tools=[\n", "        research_assistant,\n", "        product_recommendation_assistant,\n", "        trip_planning_assistant,\n", "        file_write,\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: E-commerce Customer Service System\n", "customer_query = (\n", "    \"I'm looking for hiking boots. Write the final response to current directory.\"\n", ")\n", "\n", "os.environ[\"BYPASS_TOOL_CONSENT\"] = \"true\"\n", "\n", "# The orchestrator automatically determines this requires multiple specialized agents\n", "response = orchestrator(customer_query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lets look at the messages of the orchestrator. Here you can see the agent decided to use the sub-agent as tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orchestrator.messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["customer_query = \"Can you help me plan my trip to Patagonia\"\n", "\n", "response = orchestrator(customer_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orchestrator.messages"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calling multiple agents "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orchestrator.messages = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"Can you do a research on spain? Also help me plan a 7 day trip.\"\n", "\n", "orchestrator(query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Behind the scenes, the orchestrator will:\n", "1. First call the `research_assistant`\n", "2. Then call `trip_planning_assistant`\n", "3. Combine these specialized responses into a cohesive answer that addresses both the queries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sequential Agent Communication Pattern\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The agent tool can also combine multiple agents together. In this example we will provide output of `research_agent` to `summary_agent` and return the summarized response."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" # define the user query\n", "topic = \"generative Ai\"\n", "# Create a research agent\n", "research_agent = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    system_prompt=RESEARCH_ASSISTANT_PROMPT,\n", ")\n", "# Create a summarization agent\n", "summary_agent = Agent(\n", "    model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\n", "    system_prompt=\"\"\"\n", "    You are a summarization specialist focused on distilling complex information into clear, concise summaries.\n", "    Your primary goal is to take detailed information and extract the key points, main arguments, and critical data.\n", "    You should maintain the accuracy of the original content while making it more digestible.\n", "    Focus on clarity, brevity, and highlighting the most important aspects of the information.\n", "    \"\"\",\n", ")\n", "\n", "print(\"Multiple agents created successfully!\")\n", "print(f\"\\n🔍 RESEARCH AGENT working on: {topic}\\n\") \n", "try:\n", "    # Agent 1: Invoke research agent\n", "    research_response = research_agent(\n", "        f\"Please gather comprehensive information about {topic}.\"\n", "    )\n", "    research_text = research_response.message.content[0][\"text\"]\n", "    print(\"\\n✂️ SUMMARY AGENT distilling the research\\n\")\n", "    \n", "    # Agent 2: Ask the summary agent to create a concise summary\n", "    summary_response = summary_agent(\n", "        f\"Please create a concise summary of this research: {research_text}\"\n", "    )\n", "    summary_text = summary_response.message.content[0][\"text\"]\n", "    \n", "    print(summary_text)\n", "except Exception as e:\n", "    print(f\"Error in research assistant: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Congrats!\n", "\n", "You've learned how to use agents as tools in Strands Agents to create more complex agentic applications"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}