{"cells": [{"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# Creating Swarm of agents using Strands Agents"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%%\n"}}, "source": ["## Understanding Swarm Multi-Agent Systems\n", "\n", "A Swarm is a collaborative agent orchestration system where multiple agents work together as a team to solve complex tasks. Unlike traditional sequential or hierarchical multi-agent systems, a Swarm enables autonomous coordination between agents with shared context and working memory.\n", "\n", "* **Self-organizing agent teams** with shared working memory\n", "* **Tool-based coordination** between agents\n", "* **Autonomous agent collaboration** without central control\n", "* **Dynamic task distribution** based on agent capabilities\n", "* **Collective intelligence** through shared context\n", "* **Multi-modal input support** for handling text, images, and other content types\n", "\n", "## How Swarms Work\n", "\n", "Swarms operate on the principle of emergent intelligence - the idea that a group of specialized agents working together can solve problems more effectively than a single agent. Each agent in a Swarm:\n", "\n", "1. Has access to the full task context\n", "2. Can see the history of which agents have worked on the task\n", "3. Can access shared knowledge contributed by other agents\n", "4. Can decide when to hand off to another agent with different expertise\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Quick Start with Swarm tool"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The Strands Agents SDK provides a built-in swarm tool that simplifies the implementation of multi-agent systems, offering a quick start for users. This tool implements a flexible swarm intelligence system with autonomous coordination between specialized agents.\n", "    \n", "In this example:\n", "1. The agent uses the swarm tool to dynamically create a team of specialized agents. These might include a researcher, an analyst, and a technical writer\n", "2. Next the agent executes the swarm\n", "3. The swarm agents collaborate autonomously, handing off to each other as needed\n", "4. The agent analyzes the swarm results and provides a comprehensive response to the user\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from strands import Agent\n", "from strands_tools import swarm"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["## 1.1 Swarm as a Tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Agent initialization\n", "agent = Agent(model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",tools=[swarm])\n", "\n", "# Invocation through natural language\n", "result = str(agent(\n", "    \"Use a swarm of 4 agents to analyze the current market trend for generative ai based agents.\"\n", "))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The swarm tool implements a flexible swarm intelligence system built on the Strands SDK's native Swarm multi-agent pattern. This system enables users to define custom teams of specialized AI agents that collaborate autonomously through shared context and tool-based coordination.\n", "\n", "**Key Features:**\n", "- **Custom Agent Teams**: User-defined agent specifications with individual system prompts, per-agent tool configuration, and model settings\n", "- **Autonomous Coordination**: Built-in coordination tools (handoff_to_agent, complete_swarm_task) with shared working memory across all agents\n", "- **Advanced Configuration**: Individual model providers and settings per agent, customizable tool access, comprehensive timeout and safety mechanisms\n", "- **Emergent Collective Intelligence**: Agents autonomously decide when to collaborate or handoff, with dynamic task distribution based on capabilities\n", "\n", "The full implementation of the swarm tool can be found in the [Strands Tools repository](https://github.com/strands-agents/tools/blob/main/src/strands_tools/swarm.py).\n", "\n", "**Key Parameters:**\n", "- `task`: The main task to be processed by the swarm\n", "- `agents`: List of agent specifications with name, system_prompt, tools, model_provider, and model_settings\n", "- `max_handoffs`: Maximum number of agent handoffs allowed (default: 20)\n", "- `max_iterations`: Maximum total iterations across all agents (default: 20)\n", "- `execution_timeout`: Total execution timeout in seconds (default: 900.0)\n", "- `node_timeout`: Individual agent timeout in seconds (default: 300.0)\n", "- `repetitive_handoff_detection_window`: Number of recent nodes to check for ping-pong behavior\n", "- `repetitive_handoff_min_unique_agents`: Minimum unique nodes required in recent sequence\n", "\n", "**How the Swarm Tool Works:**\n", "1. **Agent Creation**: Creates specialized agents based on user specifications with individual model providers and tools\n", "2. **Swarm Initialization**: Sets up the Strands SDK Swarm with coordination tools and safety mechanisms\n", "3. **Autonomous Execution**: Agents collaborate through handoffs and shared context without central control\n", "4. **Result Aggregation**: Collects individual contributions and provides comprehensive execution metrics\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Creating a Swarm with Strands Agents"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Strands Agents SDK allows you to create swarms using existing Agent objects, even when they use different model providers or have different configurations. The Swarm system enables autonomous coordination between agents through auto handoff mechanisms and shared context, allowing agents to dynamically transfer control when they need specialized expertise from other agents.\n", "\n", "### 2.1 Agent Coordination with <PERSON> Handoff"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<p align=\"center\">\n", "    <img src=\"./images/swarm_example.png\">\n", "</p>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this coordination approach, agents use the Strands SDK's native Swarm pattern with built-in handoff tools to autonomously transfer control to other specialized agents when they need different expertise. Each agent is automatically equipped with coordination tools like `handoff_to_agent` and `complete_swarm_task`. The following example demonstrates a swarm of specialized agents collaborating through shared context and autonomous handoffs:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create specialized agents with different expertise\n", "research_agent = Agent(system_prompt=(\"\"\"You are a Research Agent specializing in gathering and analyzing information.\n", "Your role in the swarm is to provide factual information and research insights on the topic.\n", "You should focus on providing accurate data and identifying key aspects of the problem.\n", "When receiving input from other agents, evaluate if their information aligns with your research.\n", "\"\"\"), \n", "name=\"research_agent\",model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\")\n", "\n", "creative_agent = Agent(system_prompt=(\"\"\"You are a Creative Agent specializing in generating innovative solutions.\n", "Your role in the swarm is to think outside the box and propose creative approaches.\n", "You should build upon information from other agents while adding your unique creative perspective.\n", "Focus on novel approaches that others might not have considered.\n", "\"\"\"), \n", "name=\"creative_agent\",model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\")\n", "\n", "critical_agent = Agent(system_prompt=(\"\"\"You are a Critical Agent specializing in analyzing proposals and finding flaws.\n", "Your role in the swarm is to evaluate solutions proposed by other agents and identify potential issues.\n", "You should carefully examine proposed solutions, find weaknesses or oversights, and suggest improvements.\n", "Be constructive in your criticism while ensuring the final solution is robust.\n", "\"\"\"), \n", "name=\"critical_agent\",model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\")\n", "\n", "summarizer_agent = Agent(system_prompt=(\"\"\"You are a Summarizer Agent specializing in synthesizing information.\n", "Your role in the swarm is to gather insights from all agents and create a cohesive final solution.\n", "You should combine the best ideas and address the criticisms to create a comprehensive response.\n", "Focus on creating a clear, actionable summary that addresses the original query effectively.\n", "\"\"\"),name=\"summarizer_agent\",model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The agent coordination is implemented using the Strands SDK's native Swarm class, which automatically provides coordination tools and manages shared context:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from strands.multiagent import Swarm\n", "\n", "# Create a swarm with these agents\n", "swarm = Swarm(\n", "    [research_agent, creative_agent, critical_agent, summarizer_agent],\n", "    max_handoffs=20,\n", "    max_iterations=20,\n", "    execution_timeout=900.0,  # 15 minutes\n", "    node_timeout=300.0,       # 5 minutes per agent\n", "    repetitive_handoff_detection_window=8,  # There must be >= 3 unique agents in the last 8 handoffs\n", "    repetitive_handoff_min_unique_agents=3\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The swarm operates through autonomous agent coordination, with agents deciding when to handoff based on their expertise and the task requirements:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Execute the swarm on a task\n", "result = swarm(\"Create a blog post explaining Agentic AI then create a summary for a social media post.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Access the final result\n", "print(f\"Status: {result.status}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this example:\n", "\n", "* The research_agent might start by gathering information about Agentic AI\n", "* Handoff to the creative_agent to develop engaging content and structure\n", "* The critical_agent reviews the content for accuracy and completeness\n", "* Finally, the summarizer_agent synthesizes all contributions into a cohesive blog post\n", "* Agents can handoff multiple times as needed, with the swarm automatically managing coordination\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# See which agents were involved\n", "for node in result.node_history:\n", "    print(f\"Agent: {node.node_id}\")\n", "\n", "# Get results from specific nodes\n", "research_result = result.results[\"research_agent\"].result\n", "print(f\"Research: {research_result}\")\n", "\n", "# Get performance metrics\n", "print(f\"Total iterations: {result.execution_count}\")\n", "print(f\"Execution time: {result.execution_time}ms\")\n", "print(f\"Token usage: {result.accumulated_usage}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The swarm system automatically manages agent coordination, shared context, and handoff mechanisms. Agents can autonomously decide when to transfer control to other specialists, creating a seamless collaborative workflow with built-in safety features like timeout protection and repetitive handoff detection.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## When to use Swarm:\n", "\n", "- For complex tasks requiring diverse expertise and specialized knowledge\n", "- When you need multiple perspectives and collaborative problem-solving\n", "- For tasks that benefit from autonomous agent coordination and collective intelligence\n", "- When handling multi-modal inputs like text and images together\n", "- For projects requiring dynamic task distribution based on agent capabilities\n", "\n", "## Best Practices\n", "\n", "1. **Create specialized agents**: Define clear roles for each agent in your Swarm\n", "2. **Use descriptive agent names**: Names should reflect the agent's specialty\n", "3. **Set appropriate timeouts**: Adjust based on task complexity and expected runtime\n", "4. **Enable repetitive handoff detection**: Set appropriate values for `repetitive_handoff_detection_window` and `repetitive_handoff_min_unique_agents` to prevent ping-pong behavior\n", "5. **Include diverse expertise**: Ensure your Swarm has agents with complementary skills\n", "6. **Provide agent descriptions**: Add descriptions to your agents to help other agents understand their capabilities\n", "7. **Leverage multi-modal inputs**: Use ContentBlocks for rich inputs including images\n", "\n", "## Conclusion\n", "\n", "Multi-agent swarms solve complex problems through emergent collective intelligence. The Strands Agents SDK provides both a flexible swarm tool and native Swarm class for creating custom agent teams that collaborate autonomously. By distributing tasks across specialized agents with individual model providers, tools, and configurations, swarms achieve better results than single agents working alone. With built-in coordination tools, shared context, and comprehensive safety mechanisms, developers can create sophisticated multi-agent systems that handle complex, multi-faceted problems through self-organizing collaboration.\n", "\n", "For further details on Swarms check out the [Strands Documentation](https://strandsagents.com/latest/documentation/docs/user-guide/concepts/multi-agent/swarm/)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}