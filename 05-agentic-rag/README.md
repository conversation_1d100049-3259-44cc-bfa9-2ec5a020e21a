# Agentic RAG Samples

| Example ID | Agent                                              | Features showcased                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| ---------- | -------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1          | [Corrective RAG Agent](./1-corrective-rag-agent/) | A corrective Retrieval-Augmented Generation (RAG) system built using Strand Agents. The system is designed to identify low relevance responses to users questions responses and automatically refine queries or responses using multiple tools and web search using Tavily. |
| 2          | [Adaptive Structured RAG Agent](./2-adaptive-structured-rag-agent/)                                  | An Adaptive Structured RAG agent that converts natural language questions into SQL queries with self-correcting feedback loops. This agent features dual-mode support for both local SQLite databases and AWS Athena, wealth management domain-specific schema, and intelligent error handling. The agent includes knowledge base integration for schema information and can handle complex queries across client portfolios, investment analysis, and performance metrics.                                                                    |
| 3          | [Unstructured Structured RAG Agent](./3-unstructure-structured-rag-agent/)                                  | An intelligent RAG system that uses Strands Agents to automatically direct user queries to the appropriate knowledge base based on query type. The system features two Amazon Bedrock Knowledge Bases: one with OpenSearch Serverless as the vector store for handling unstructured data, and another connecting to Redshift Serverless as a data store to handle structured data. The agent seamlessly handles both document-based queries and quantitative data-based queries within a single conversational interface. |                                                                      |

