{"cells": [{"cell_type": "markdown", "id": "5f68b737", "metadata": {}, "source": ["# Unstructured and Structured RAG with Intelligent Query Routing\n", "\n", "This notebook demonstrates an advanced RAG pattern that intelligently routes queries between **structured** and **unstructured** knowledge bases using **Strands Agents** \n", "\n", "## Overview\n", "\n", "The system uses custom retrieval tools that connect to Amazon Bedrock Knowledge Bases:\n", "1. **Query Analysis**: Agent analyzes incoming query to determine type\n", "2. **Tool Selection**: Routes to unstructured (document) or structured (SQL) assistant\n", "3. **Retrieval**: Appropriate knowledge base retrieves relevant information\n", "4. **Response Generation**: Agent synthesizes and presents the results\n", "\n", "This approach enables handling both qualitative questions (business strategy, policies) and quantitative queries (financial metrics, data analysis) within a single conversational interface."]}, {"cell_type": "markdown", "id": "9e693a8a", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Prerequisites and Setup\n", "\n", "Before running this notebook, ensure you have completed:\n", "1. **`0-prerequisites-structured-kb.ipynb`** - Sets up Redshift-based structured Amazon Bedrock Knowledge Base\n", "2. **`1-prerequisites-unstructured-kb.ipynb`** - Sets up document-based unstructured Amazon Bedrock Knowledge Base\n", "\n", "\n", "Let's start by importing the required libraries:\n"]}, {"cell_type": "code", "execution_count": null, "id": "1ce61780", "metadata": {}, "outputs": [], "source": ["import os\n", "import boto3\n", "from strands import Agent, tool"]}, {"cell_type": "code", "execution_count": null, "id": "0a124047", "metadata": {}, "outputs": [], "source": ["# Set up AWS region and  Amazon Bedrock Agent Runtime client for knowledge base interactions\n", "region = boto3.Session().region_name \n", "bedrock_agent_runtime = boto3.client('bedrock-agent-runtime', region_name=region)"]}, {"cell_type": "markdown", "id": "7027308c", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Load Knowledge Base Configuration\n", "\n", "Load the knowledge base IDs from the prerequisite notebooks:\n"]}, {"cell_type": "code", "execution_count": null, "id": "e14a35ce", "metadata": {}, "outputs": [], "source": ["\n", "# Retrieve stored knowledge base IDs from prerequisite notebooks\n", "%store -r unstructured_kb_id\n", "%store -r structured_kb_id \n", "%store -r kb_region\n", "%store -r structured_kb_region\n", "\n", "# Use the stored values\n", "UNSTRUCTURED_KB_ID = unstructured_kb_id  # From 1-prerequisites-unstructured-kb.ipynb\n", "STRUCTURED_KB_ID = structured_kb_id      # From 0-prerequisites-structured-kb.ipynb\n", "\n", "\n", "print(\"=\"*60)\n", "print(f\"Unstructured KB ID: {UNSTRUCTURED_KB_ID}\")\n", "print(f\"Structured KB ID: {STRUCTURED_KB_ID}\")\n", "print(f\"Unstructured KB Region: {kb_region}\")\n", "print(f\"Structured KB Region: {structured_kb_region}\")\n"]}, {"cell_type": "markdown", "id": "5430596f", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Custom Retrieval Tools\n", "\n", "We will create two specialized tools using the Strands Agents `@tool` decorator. Each tool handles different types of queries by connecting to the appropriate knowledge base.\n", "\n", "### Unstructured Data Assistant Tool\n", "\n", "This tool handles document-based queries by retrieving information from the unstructured knowledge base (PDF documents, reports, policies):\n"]}, {"cell_type": "code", "execution_count": null, "id": "ab213cb6", "metadata": {}, "outputs": [], "source": ["@tool\n", "def unstructured_data_assistant(query: str) -> str:\n", "    \"\"\"\n", "    Handle document-based, narrative, and conceptual queries using the unstructured knowledge base.\n", "    \n", "    Args:\n", "        query: A question about business strategies, policies, company information, \n", "               or requiring document comprehension and qualitative analysis\n", "    \n", "    Returns:\n", "        Raw retrieve response from the unstructured knowledge base\n", "    \"\"\"\n", "    try:\n", "        retrieve_response = bedrock_agent_runtime.retrieve(\n", "            knowledgeBaseId=UNSTRUCTURED_KB_ID,\n", "            retrievalQuery={'text': query},\n", "            retrievalConfiguration={\n", "                'vectorSearchConfiguration': {\n", "                    'numberOfResults': 10,\n", "                }\n", "            }\n", "        )\n", "        \n", "        return retrieve_response\n", "        \n", "    except Exception as e:\n", "        return f\"Error in unstructured data assistant: {str(e)}\"\n"]}, {"cell_type": "markdown", "id": "c3ea5d94", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Structured Data Assistant Tool\n", "\n", "This tool handles data analysis queries by retrieving information from the structured knowledge base (SQL/Redshift database):\n"]}, {"cell_type": "code", "execution_count": null, "id": "05754788", "metadata": {}, "outputs": [], "source": ["@tool\n", "def structured_data_assistant(query: str) -> str:\n", "    \"\"\"\n", "    Handle data analysis, metrics, and quantitative queries using the structured knowledge base.\n", "    \n", "    Args:\n", "        query: A question requiring calculations, aggregations, statistical analysis,\n", "               or database operations on structured data\n", "    \n", "    Returns:\n", "        Raw retrieve response from the structured knowledge base\n", "    \"\"\"\n", "    try:\n", "        retrieve_response = bedrock_agent_runtime.retrieve(\n", "            knowledgeBaseId=STRUCTURED_KB_ID,\n", "            retrievalQuery={'text': query},\n", "            retrievalConfiguration={\n", "                'vectorSearchConfiguration': {\n", "                    'numberOfResults': 10,\n", "                }\n", "            }\n", "        )\n", "        \n", "        return retrieve_response\n", "        \n", "    except Exception as e:\n", "        return f\"Error in structured data assistant: {str(e)}\"\n"]}, {"cell_type": "markdown", "id": "0244b523", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Intelligent Agent with Query Routing\n", "\n", "Now we'll create the orchestrator agent with our custom tools and system prompt that intelligently routes queries to the appropriate tool based on the query type and content.\n"]}, {"cell_type": "code", "execution_count": null, "id": "********", "metadata": {}, "outputs": [], "source": ["# Create the orchestrator agent with both tools\n", "orchestrator = Agent(\n", "    system_prompt=\"\"\"You are an intelligent assistant that routes queries to the appropriate knowledge base. Choose the appropriate tool based on the query type. \n", "    The tools return raw data that you should analyze and present in a clear, helpful format.\"\"\",\n", "    tools=[\n", "        unstructured_data_assistant,\n", "        structured_data_assistant\n", "    ]\n", ")"]}, {"cell_type": "markdown", "id": "4ff1d2a7", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["Let's test our agent with different types of queries to observe how it routes them to the appropriate knowledge bases.\n", "\n", "### Example 1: Unstructured Query\n", "\n", "This query asks about qualitative, document-based information and should be routed to the unstructured knowledge base:\n"]}, {"cell_type": "code", "execution_count": null, "id": "e3f98e97", "metadata": {}, "outputs": [], "source": ["# EXAMPLE 1: Business Strategy Query (should use unstructured_data_assistant)\n", "print(\"=== EXAMPLE 1: BUSINESS STRATEGY QUERY ===\")\n", "print(\"Query: What is Octank Financial's business strategy?\")\n", "print()\n", "\n", "response = orchestrator(\"What is Octank Financial's business strategy?\")\n", "print(response)"]}, {"cell_type": "markdown", "id": "ee7c72e2", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Example 2: Structured Query\n", "\n", "This query asks for quantitative data analysis and should be routed to the structured knowledge base:\n"]}, {"cell_type": "code", "execution_count": null, "id": "174d11ac", "metadata": {}, "outputs": [], "source": ["# EXAMPLE 2: Financial Data Query (should use structured_data_assistant)\n", "print(\"=== EXAMPLE 2: FINANCIAL DATA QUERY ===\")\n", "print(\"Query: What is the total spending by all customers?\")\n", "print()\n", "\n", "response = orchestrator(\"What is the total spending by all customers?\")\n", "print(response)\n"]}, {"cell_type": "markdown", "id": "7abd3c16", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Agent Thinking Inspection\n", "\n", "One of the powerful features of Strands Agents is the ability to inspect the agent's internal reasoning process. Let's examine how the agent made its decisions:"]}, {"cell_type": "code", "execution_count": null, "id": "5a2dc0c9", "metadata": {}, "outputs": [], "source": ["# Inspect the complete conversation flow\n", "def inspect_message_flow(messages):\n", "    print(\"=== DETAILED MESSAGE FLOW ===\")\n", "    \n", "    for i, message in enumerate(messages):\n", "        print(f\"\\n--- Message {i+1} ---\")\n", "        print(f\"Role: {message['role']}\")\n", "        \n", "        for j, content in enumerate(message['content']):\n", "            print(f\"  Content {j+1}:\")\n", "            \n", "            if 'text' in content:\n", "                text = content['text']\n", "                # Truncate long text for readability\n", "                if len(text) > 200:\n", "                    text = text[:200] + \"...\"\n", "                print(f\"    Text: {text}\")\n", "            \n", "            elif 'toolUse' in content:\n", "                tool_use = content['toolUse']\n", "                print(f\"    Tool Use: {tool_use['name']}\")\n", "                print(f\"    Input: {tool_use['input']}\")\n", "                print(f\"    ID: {tool_use['toolUseId']}\")\n", "            \n", "            elif 'toolResult' in content:\n", "                tool_result = content['toolResult']\n", "                print(f\"    Tool Result: {tool_result['status']}\")\n", "                print(f\"    ID: {tool_result['toolUseId']}\")\n", "                # Don't print full content as it's very long\n", "                print(f\"    Content: [Raw KB Response - {len(str(tool_result['content']))} chars]\")\n", "\n", "# Run the inspection\n", "inspect_message_flow(orchestrator.messages)\n"]}, {"cell_type": "markdown", "id": "3e93e2fd", "metadata": {}, "source": ["## Clean up the resources\n", "\n", "When you have finished with this notebook, return to the previous notebook to delete the resources created and not incurr extra costs!"]}, {"cell_type": "code", "execution_count": null, "id": "f920c466", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai-on-aws", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}