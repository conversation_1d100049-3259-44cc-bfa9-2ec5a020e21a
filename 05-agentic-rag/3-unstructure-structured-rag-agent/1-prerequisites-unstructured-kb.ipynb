{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Create Unstructured Amazon Bedrock Knowledge Base for Octank Financial\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["This notebook demonstrates how to create and configure an Amazon Bedrock Knowledge Base for unstructured data.\n", "\n", "The Knowledge Base integrates Amazon S3 as the data source for the Octank Financial 10K document and uses Amazon OpenSearch Serverless as the vector store. It enables RAG by powering queries over unstructured financial and business content.\n", "\n", "This unstructured knowledge base will be used in conjunction with the structured knowledge base to create agentic RAG using Strands Agents.\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Setup and prerequisites\n", "\n", "### Prerequisites\n", "* Python 3.10+\n", "* AWS account\n", "* Amazon Bedrock foundation models enabled\n", "* IAM role with permissions to create Amazon Bedrock Knowledge Base, Amazon S3 bucket, Amazon OpenSearch Serverless\n", "\n", "Let's now install the requirement packages and define the needed clients to create our Amazon Bedrock Knowledge Base:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import time\n", "import uuid\n", "import boto3\n", "import logging\n", "import requests\n", "import botocore\n", "from datetime import datetime\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s3_client = boto3.client('s3')\n", "sts_client = boto3.client('sts')\n", "session = boto3.session.Session()\n", "region = session.region_name\n", "logging.basicConfig(format='[%(asctime)s] p%(process)s {%(filename)s:%(lineno)d} %(levelname)s - %(message)s', level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "bedrock_agent_runtime_client = boto3.client(\"bedrock-agent-runtime\")\n", "\n", "print(f\"AWS region: {region}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate unique suffix for resource names\n", "current_time = time.time()\n", "timestamp_str = time.strftime(\"%Y%m%d%H%M%S\", time.localtime(current_time))[-4:]\n", "suffix = f\"{timestamp_str}\"\n", "\n", "print(f\"Suffix: {suffix}\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Step 1: Download Amazon Bedrock Knowledge Bases helper\n", "To expedite the knowledge base configuration and creation we will be downloading the knowledge base utility file. This contains a helper to generate knowledge bases abstracting the multiple API calls that need to be used.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://raw.githubusercontent.com/aws-samples/amazon-bedrock-samples/main/rag/knowledge-bases/features-examples/utils/knowledge_base.py\"\n", "target_path = \"utils/knowledge_base.py\"\n", "os.makedirs(os.path.dirname(target_path), exist_ok=True)\n", "response = requests.get(url)\n", "with open(target_path, \"w\") as f:\n", "    f.write(response.text)\n", "print(f\"Downloaded Knowledge Bases utils to {target_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from utils.knowledge_base import BedrockKnowledgeBase"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Step 2: Create Amazon Bedrock Knowledge Base for Unstructured Data\n", "In this section we will configure the Amazon Bedrock Knowledge Base containing the Octank Financial 10K document. We will be using Amazon OpenSearch Serverless Service as the underlying vector store and Amazon S3 as the data source containing the PDF file.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["knowledge_base_name = f\"octank-financial-unstructured-kb-{suffix}\"\n", "knowledge_base_description = \"Octank Financial Unstructured Knowledge Base containing 10K financial document for business strategy and company information queries.\"\n", "foundation_model = \"us.anthropic.claude-3-7-sonnet-20250219-v1:0\""]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["For this notebook, we'll create a Knowledge Base with an Amazon S3 data source containing the Octank Financial 10K PDF.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_bucket_name = f'octank-financial-unstructured-{suffix}-bucket'\n", "data_sources = [{\"type\": \"S3\", \"bucket_name\": data_bucket_name}]\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Create the Amazon S3 bucket and upload the Octank Financial 10K document\n", "We'll create an S3 bucket and upload the Octank Financial 10K PDF document that will serve as our unstructured data source.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_s3_bucket(bucket_name, region=None):\n", "    s3 = boto3.client('s3', region_name=region)\n", "\n", "    try:\n", "        if region is None or region == 'us-east-1':\n", "            s3.create_bucket(Bucket=bucket_name)\n", "        else:\n", "            s3.create_bucket(\n", "                Bucket=bucket_name,\n", "                CreateBucketConfiguration={'LocationConstraint': region}\n", "            )\n", "        print(f\"Bucket '{bucket_name}' created successfully.\")\n", "    except botocore.exceptions.ClientError as e:\n", "        if e.response['Error']['Code'] == 'BucketAlreadyOwnedByYou':\n", "            print(f\"Bucket '{bucket_name}' already exists and is owned by you.\")\n", "        else:\n", "            print(f\"Failed to create bucket: {e.response['Error']['Message']}\")\n", "\n", "create_s3_bucket(data_bucket_name, region)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def upload_directory(path, bucket_name):\n", "    for root, dirs, files in os.walk(path):\n", "        for file in files:\n", "            file_to_upload = os.path.join(root, file)\n", "            print(f\"Uploading file {file_to_upload} to {bucket_name}\")\n", "            s3_client.upload_file(file_to_upload, bucket_name, file)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Upload the Octank Financial 10K document\n", "upload_directory(\"./sample_unstructured_data\", data_bucket_name)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Create the Unstructured Knowledge Base\n", "We are now going to create the Knowledge Base using the abstraction located in the helper function we previously downloaded.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unstructured_knowledge_base = BedrockKnowledgeBase(\n", "    kb_name=f'{knowledge_base_name}',\n", "    kb_description=knowledge_base_description,\n", "    data_sources=data_sources,\n", "    chunking_strategy=\"FIXED_SIZE\", \n", "    suffix=f'{suffix}-u' \n", ")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Start ingestion job\n", "Once the KB and data source are created, we can start the ingestion job for the data source. During the ingestion job, KB will fetch the documents in the data source, pre-process it to extract text, chunk it based on the chunking size provided, create embeddings of each chunk and then write it to the vector database (OpenSearch Serverless).\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ensure that the kb is available\n", "time.sleep(30)\n", "# Sync knowledge base\n", "unstructured_knowledge_base.start_ingestion_job()\n", "# Keep the kb_id for invocation later in the invoke request\n", "unstructured_kb_id = unstructured_knowledge_base.get_knowledge_base_id()\n", "print(f\"Unstructured Knowledge Base ID: {unstructured_kb_id}\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Test the Unstructured Knowledge Base\n", "We can now test the Knowledge Base to verify the Octank Financial 10K document has been ingested properly.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"What is Octank Financial's primary business strategy?\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["foundation_model = \"anthropic.claude-3-sonnet-20240229-v1:0\"\n", "\n", "response = bedrock_agent_runtime_client.retrieve_and_generate(\n", "    input={\n", "        \"text\": query\n", "    },\n", "    retrieveAndGenerateConfiguration={\n", "        \"type\": \"KNOWLEDGE_BASE\",\n", "        \"knowledgeBaseConfiguration\": {\n", "            'knowledgeBaseId': unstructured_kb_id,\n", "            \"modelArn\": \"arn:aws:bedrock:{}::foundation-model/{}\".format(region, foundation_model),\n", "            \"retrievalConfiguration\": {\n", "                \"vectorSearchConfiguration\": {\n", "                    \"numberOfResults\": 5\n", "                } \n", "            }\n", "        }\n", "    }\n", ")\n", "\n", "print(\"Response:\")\n", "print(response['output']['text'], end='\\\\n'*2)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Store the Knowledge Base ID\n", "Store the ID of the generated Unstructured Knowledge Base to use it in the main dual-knowledge-base RAG notebook.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kb_region = region\n", "%store unstructured_kb_id\n", "%store kb_region\n", "%store data_bucket_name\n", "\n", "print(\"=\"*60)\n", "print(f\"Unstructured Knowledge Base ID: {unstructured_kb_id}\")\n", "print(f\"Region: {kb_region}\")\n", "print(f\"S3 Bucket: {data_bucket_name}\")\n", "\n", "print(\"=\"*60)\n", "print(\"Configuration stored successfully!\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Clean up the resources\n", "**If you plan to use the other notebooks and continue with them, do not delete the Knowledge Base yet as it will be needed.**\n", "\n", "When you are finished with the other notebooks, to avoid additional costs, delete the resources created.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Uncomment to delete resources when finished\n", "\n", "# print(\"===============================Deleting Unstructured Knowledge Base and associated resources==============================\\\\n\")\n", "# unstructured_knowledge_base.delete_kb(delete_s3_bucket=True, delete_iam_roles_and_policies=True)\n", "# print(\"Cleanup completed successfully!\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["###  Summary\n", "If all the above cells executed successfully, you have:\n", "\n", "- Created an S3 bucket for unstructured data  \n", "- Uploaded the Octank Financial 10K PDF  \n", "- Created an Amazon Bedrock Knowledge Base  \n", "- Configured OpenSearch Serverless as the vector store  \n", "- Successfully ingested the document  \n", "- Tested a query with knowledge base's `RetrieveAndGenerate` API\n", "- Stored the Knowledge Base ID for use in the main notebook  \n", "\n", "You are now ready to proceed to the main `2-unstructured-structured-rag-agent.ipynb` notebook!\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai-on-aws", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 2}