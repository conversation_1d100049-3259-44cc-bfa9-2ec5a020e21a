{"cells": [{"cell_type": "markdown", "id": "e263e49a", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Create Structured Amazon Bedrock Knowledge Base with Redshift\n", "\n", "This notebook demonstrates how to create and configure an Amazon Bedrock Knowledge Base that uses Amazon Redshift Serverless as a source for structured data. \n", "\n", "The Knowledge Base integrates Amazon Redshift as the data source for e-commerce transactional data and enables RAG  by powering queries over structured business data including orders, payments, reviews, and customer analytics.\n", "\n", "This structured knowledge base will be used in conjunction with the unstructured knowledge base to create agentic RAG using Strands Agents\n"]}, {"cell_type": "markdown", "id": "c1bedd2b", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Setup and Prerequisites\n", "\n", "### Prerequisites\n", "* Python 3.10+\n", "* AWS account with appropriate permissions\n", "* Amazon Bedrock foundation models enabled\n", "* IAM permissions for Amazon Redshift Serverless, Amazon S3, and Amazon Bedrock\n", "\n", "### Required AWS Services\n", "- **Amazon Bedrock**: For knowledge base creation and LLM inference\n", "- **Amazon Redshift Serverless**: As the structured data source\n", "- **Amazon S3**: For data staging and intermediate storage\n", "- **AWS IAM**: For service permissions and roles\n", "\n", "Let's start by importing the required libraries and setting up AWS clients:\n"]}, {"cell_type": "code", "execution_count": null, "id": "725828ff", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import time\n", "import uuid\n", "import boto3\n", "import logging\n", "import requests\n", "from datetime import datetime\n"]}, {"cell_type": "code", "execution_count": null, "id": "14a61171", "metadata": {}, "outputs": [], "source": ["# Initialize AWS clients\n", "session = boto3.session.Session()\n", "region = session.region_name\n", "\n", "s3_client = boto3.client('s3')\n", "sts_client = boto3.client('sts')\n", "redshift_client = boto3.client('redshift-serverless', region_name=region)\n", "redshift_data_client = boto3.client('redshift-data', region_name=region)\n", "iam_client = boto3.client('iam')\n", "bedrock_agent_client = boto3.client('bedrock-agent')\n", "bedrock_agent_runtime_client = boto3.client(\"bedrock-agent-runtime\")"]}, {"cell_type": "code", "execution_count": null, "id": "1e5d40a8", "metadata": {}, "outputs": [], "source": ["# Generate unique suffix for resource names\n", "current_time = time.time()\n", "timestamp_str = time.strftime(\"%Y%m%d%H%M%S\", time.localtime(current_time))[-4:]\n", "suffix = f\"{timestamp_str}\"\n", "\n", "print(f\"Using suffix: {suffix}\")"]}, {"cell_type": "markdown", "id": "6d05e550", "metadata": {}, "source": ["## Step 1: Download Amazon Bedrock Knowledge Bases helper\n", "\n", "Lets download the structured knowledge base utility to help with Knowledge Base configuration and creation.\n"]}, {"cell_type": "code", "execution_count": null, "id": "91ab0fd5", "metadata": {}, "outputs": [], "source": ["url = \"https://raw.githubusercontent.com/aws-samples/amazon-bedrock-samples/main/rag/knowledge-bases/features-examples/utils/structured_knowledge_base.py\"\n", "target_path = \"utils/structured_knowledge_base.py\"\n", "os.makedirs(os.path.dirname(target_path), exist_ok=True)\n", "response = requests.get(url)\n", "with open(target_path, \"w\") as f:\n", "    f.write(response.text)\n", "print(f\"Downloaded structured KB utils to {target_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f62900bd", "metadata": {}, "outputs": [], "source": ["from utils.structured_knowledge_base import BedrockStructuredKnowledgeBase"]}, {"cell_type": "markdown", "id": "3fb2ccb8", "metadata": {}, "source": ["## Step 2: Set up Redshift Serverless Infrastructure\n", "\n", "Next we will create the necessary Redshift Serverless components: namespace and workgroup. This infrastructure will host our structured data that the Knowledge Base will query.\n", "\n", "- The namespace is a logical grouping of database objects and users. It contains the database, schemas, and other objects:\n", "- The workgroup provides compute resources and configuration settings for running queries against the namespace:\n"]}, {"cell_type": "code", "execution_count": null, "id": "7182df91", "metadata": {}, "outputs": [], "source": ["# Configuration for Redshift resources\n", "REDSHIFT_NAMESPACE = f'sds-ecommerce-{suffix}'\n", "REDSHIFT_WORKGROUP = f'sds-ecommerce-wg-{suffix}'\n", "REDSHIFT_DATABASE = f'sds-ecommerce'\n", "S3_BUCKET = f'sds-ecommerce-redshift-{suffix}'\n", "\n", "print(f\"Redshift Namespace: {REDSHIFT_NAMESPACE}\")\n", "print(f\"Redshift Workgroup: {REDSHIFT_WORKGROUP}\")\n", "print(f\"Database: {REDSHIFT_DATABASE}\")\n", "print(f\"S3 Bucket: {S3_BUCKET}\")"]}, {"cell_type": "markdown", "id": "7540efc9", "metadata": {}, "source": ["### Create IAM Role for Redshift\n", "\n", "Create an IAM role that allows Redshift to access S3 for data loading operations"]}, {"cell_type": "code", "execution_count": null, "id": "109b83d0", "metadata": {}, "outputs": [], "source": ["def create_iam_role_for_redshift():\n", "    \"\"\"Create IAM role for Redshift to access S3\"\"\"\n", "    try:\n", "        # Get account ID\n", "        account_id = sts_client.get_caller_identity()['Account']\n", "        \n", "        # Create IAM role if it doesn't exist\n", "        role_name = f'RedshiftS3AccessRole-{suffix}'\n", "        try:\n", "            role_response = iam_client.get_role(RoleName=role_name)\n", "            print(f'Role {role_name} already exists')\n", "            return f'arn:aws:iam::{account_id}:role/{role_name}'\n", "        except iam_client.exceptions.NoSuchEntityException:\n", "            trust_policy = {\n", "                \"Version\": \"2012-10-17\",\n", "                \"Statement\": [\n", "                    {\n", "                        \"Effect\": \"Allow\",\n", "                        \"Principal\": {\n", "                            \"Service\": \"redshift.amazonaws.com\"\n", "                        },\n", "                        \"Action\": \"sts:AssumeRole\"\n", "                    }\n", "                ]\n", "            }\n", "            \n", "            iam_client.create_role(\n", "                RoleName=role_name,\n", "                AssumeRolePolicyDocument=json.dumps(trust_policy)\n", "            )\n", "            \n", "            # Attach necessary policies\n", "            iam_client.attach_role_policy(\n", "                RoleName=role_name,\n", "                PolicyArn='arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess'\n", "            )\n", "            \n", "            print(f'Created role {role_name}')\n", "            return f'arn:aws:iam::{account_id}:role/{role_name}'\n", "            \n", "    except Exception as e:\n", "        print(f'<PERSON><PERSON><PERSON> creating IAM role: {str(e)}')\n", "        raise\n", "\n", "\n", "\n", "redshift_role_arn = create_iam_role_for_redshift()\n", "print(f\"Redshift IAM Role ARN: {redshift_role_arn}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "e59245e0", "metadata": {}, "outputs": [], "source": ["def create_redshift_namespace():\n", "    \"\"\"Create Redshift Serverless namespace\"\"\"\n", "    try:\n", "        # Check if namespace already exists\n", "        try:\n", "            response = redshift_client.get_namespace(namespaceName=REDSHIFT_NAMESPACE)\n", "            print(f'Namespace {REDSHIFT_NAMESPACE} already exists')\n", "            return response['namespace']\n", "        except redshift_client.exceptions.ResourceNotFoundException:\n", "            print(f'Creating namespace {REDSHIFT_NAMESPACE}...')\n", "        \n", "        # Create the namespace\n", "        response = redshift_client.create_namespace(\n", "            namespaceName=REDSHIFT_NAMESPACE,\n", "            adminUsername='admin',\n", "            adminUserPassword='TempPassword123!',  # Change this in production\n", "            dbName=REDSHIFT_DATABASE,\n", "            defaultIamRoleArn=redshift_role_arn,\n", "            iamRoles=[redshift_role_arn]\n", "        )\n", "        \n", "        print(f'Created namespace {REDSHIFT_NAMESPACE}')\n", "        \n", "        # Wait for namespace to be available\n", "        print('Waiting for namespace to be available...')\n", "        max_attempts = 30\n", "        for attempt in range(max_attempts):\n", "            try:\n", "                namespace_response = redshift_client.get_namespace(namespaceName=REDSHIFT_NAMESPACE)\n", "                status = namespace_response['namespace']['status']\n", "                if status == 'AVAILABLE':\n", "                    print(f'Namespace {REDSHIFT_NAMESPACE} is now available')\n", "                    return namespace_response['namespace']\n", "                else:\n", "                    print(f'Namespace status: {status}, waiting...')\n", "                    time.sleep(10)\n", "            except Exception as e:\n", "                print(f'Error checking namespace status: {str(e)}, retrying...')\n", "                time.sleep(10)\n", "        \n", "        print('Timeout waiting for namespace, but proceeding...')\n", "        return response['namespace']\n", "        \n", "    except Exception as e:\n", "        print(f'Error creating namespace: {str(e)}')\n", "        raise\n", "\n", "# Create namespace\n", "namespace = create_redshift_namespace()\n"]}, {"cell_type": "code", "execution_count": null, "id": "372e2dac", "metadata": {}, "outputs": [], "source": ["def create_redshift_workgroup():\n", "    \"\"\"Create Redshift Serverless workgroup\"\"\"\n", "    try:\n", "        # Check if workgroup already exists\n", "        try:\n", "            response = redshift_client.get_workgroup(workgroupName=REDSHIFT_WORKGROUP)\n", "            print(f'Workgroup {REDSHIFT_WORKGROUP} already exists')\n", "            return response['workgroup']\n", "        except redshift_client.exceptions.ResourceNotFoundException:\n", "            print(f'Creating workgroup {REDSHIFT_WORKGROUP}...')\n", "        \n", "        # Create the workgroup\n", "        response = redshift_client.create_workgroup(\n", "            workgroupName=REDSHIFT_WORKGROUP,\n", "            namespaceName=REDSHIFT_NAMESPACE,\n", "            baseCapacity=8,  # Minimum base capacity\n", "            enhancedVpcRouting=False,\n", "            publiclyAccessible=True,\n", "            configParameters=[\n", "                {\n", "                    'parameterKey': 'enable_user_activity_logging',\n", "                    'parameterValue': 'true'\n", "                }\n", "            ]\n", "        )\n", "        \n", "        print(f'Created workgroup {REDSHIFT_WORKGROUP}')\n", "        \n", "        # Wait for workgroup to be available\n", "        print('Waiting for workgroup to be available...')\n", "        max_attempts = 30\n", "        for attempt in range(max_attempts):\n", "            try:\n", "                workgroup_response = redshift_client.get_workgroup(workgroupName=REDSHIFT_WORKGROUP)\n", "                status = workgroup_response['workgroup']['status']\n", "                if status == 'AVAILABLE':\n", "                    print(f'Workgroup {REDSHIFT_WORKGROUP} is now available')\n", "                    return workgroup_response['workgroup']\n", "                else:\n", "                    print(f'Workgroup status: {status}, waiting...')\n", "                    time.sleep(10)\n", "            except Exception as e:\n", "                print(f'Error checking workgroup status: {str(e)}, retrying...')\n", "                time.sleep(10)\n", "        \n", "        print('Timeout waiting for workgroup, but proceeding...')\n", "        return response['workgroup']\n", "        \n", "    except Exception as e:\n", "        print(f'Error creating workgroup: {str(e)}')\n", "        raise\n", "\n", "# Create workgroup\n", "workgroup = create_redshift_workgroup()\n", "workgroup_arn = workgroup['workgroupArn']\n", "print(f\"Workgroup ARN: {workgroup_arn}\")\n"]}, {"cell_type": "markdown", "id": "f5a03caa", "metadata": {}, "source": ["## Step 3: Create S3 Bucket and Load Sample Data\n", "\n", "We will create an S3 bucket to stage our sample e-commerce data before loading it into Redshift tables."]}, {"cell_type": "code", "execution_count": null, "id": "9976c340", "metadata": {}, "outputs": [], "source": ["def create_s3_bucket():\n", "    \"\"\"Create S3 bucket for data staging\"\"\"\n", "    try:\n", "        s3_client.head_bucket(Bucket=S3_BUCKET)\n", "        print(f'Bucket {S3_BUCKET} already exists')\n", "    except:\n", "        try:\n", "            if region == 'us-east-1':\n", "                s3_client.create_bucket(Bucket=S3_BUCKET)\n", "            else:\n", "                s3_client.create_bucket(\n", "                    Bucket=S3_BUCKET,\n", "                    CreateBucketConfiguration={'LocationConstraint': region}\n", "                )\n", "            print(f'Created bucket {S3_BUCKET}')\n", "        except Exception as e:\n", "            print(f'Error creating bucket: {str(e)}')\n", "            raise\n", "\n", "# Create S3 bucket\n", "create_s3_bucket()"]}, {"cell_type": "code", "execution_count": null, "id": "1be47dd7", "metadata": {}, "outputs": [], "source": ["def upload_sample_data():\n", "    \"\"\"Upload sample CSV files to S3\"\"\"\n", "    data_files = ['orders.csv', 'order_items.csv', 'payments.csv', 'reviews.csv']\n", "    sds_directory = 'sample_structured_data'\n", "    \n", "    print(\"Uploading sample data files to S3...\")\n", "    files_found = 0\n", "    \n", "    for file_name in data_files:\n", "        local_path = os.path.join(sds_directory, file_name)\n", "        if os.path.exists(local_path):\n", "            # Get file size for informational purposes\n", "            file_size = os.path.getsize(local_path)\n", "            file_size_mb = file_size / (1024 * 1024)\n", "            \n", "            s3_client.upload_file(local_path, S3_BUCKET, file_name)\n", "            print(f'Uploaded {file_name} ({file_size_mb:.1f} MB) to S3')\n", "            files_found += 1\n", "        else:\n", "            print(f'Warning: {local_path} not found')\n", "    \n", "    if files_found == len(data_files):\n", "        print(f\"\\nSuccessfully uploaded all {files_found} data files to S3\")\n", "    else:\n", "        print(f\"\\nOnly {files_found} out of {len(data_files)} files were found and uploaded\")\n", "\n", "# Upload sample data\n", "upload_sample_data()\n"]}, {"cell_type": "markdown", "id": "15fd94c8", "metadata": {}, "source": ["## Step 4: Create Redshift Tables and Load Data\n", "\n", "Now we will create the database tables in Redshift and load our sample e-commerce data."]}, {"cell_type": "markdown", "id": "942215ae", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Define Redshift Data API Helper Functions\n", "\n", "These functions help us execute SQL statements using the Redshift Data API:\n"]}, {"cell_type": "code", "execution_count": null, "id": "1a4b8040", "metadata": {}, "outputs": [], "source": ["def wait_for_statement(statement_id):\n", "    \"\"\"Wait for a Redshift Data API statement to complete\"\"\"\n", "    max_attempts = 30\n", "    for attempt in range(max_attempts):\n", "        try:\n", "            response = redshift_data_client.describe_statement(Id=statement_id)\n", "            status = response['Status']\n", "            if status == 'FINISHED':\n", "                return response\n", "            elif status == 'FAILED':\n", "                raise Exception(f\"Statement failed: {response.get('Error', 'Unknown error')}\")\n", "            elif status == 'CANCELLED':\n", "                raise Exception(\"Statement was cancelled\")\n", "            else:\n", "                print(f\"Statement status: {status}, waiting...\")\n", "                time.sleep(5)\n", "        except Exception as e:\n", "            if 'Statement failed' in str(e) or 'cancelled' in str(e):\n", "                raise\n", "            print(f\"Error checking statement status: {str(e)}, retrying...\")\n", "            time.sleep(5)\n", "    \n", "    raise Exception(\"Timeout waiting for statement to complete\")\n", "\n", "def run_redshift_statement(sql_statement):\n", "    \"\"\"Execute a SQL statement in Redshift\"\"\"\n", "    try:\n", "        response = redshift_data_client.execute_statement(\n", "            WorkgroupName=REDSHIFT_WORKGROUP,\n", "            Database=REDSHIFT_DATABASE,\n", "            Sql=sql_statement\n", "        )\n", "        statement_id = response['Id']\n", "        print(f\"Executing statement: {statement_id}\")\n", "        result = wait_for_statement(statement_id)\n", "        print(f\"Statement completed successfully\")\n", "        return result\n", "    except Exception as e:\n", "        print(f\"Error executing statement: {str(e)}\")\n", "        raise\n"]}, {"cell_type": "markdown", "id": "d97d9649", "metadata": {}, "source": ["### Create Database Tables\n", "\n", "Create the database tables in Reshift to store structured data sample with appropriate schema "]}, {"cell_type": "code", "execution_count": null, "id": "6c7d3205", "metadata": {}, "outputs": [], "source": ["# Create tables in Redshift\n", "def create_tables():\n", "    \"\"\"Create all necessary tables in Redshift\"\"\"\n", "    \n", "    # Orders table\n", "    orders_sql = \"\"\"\n", "    CREATE TABLE IF NOT EXISTS orders (\n", "        order_id VARCHAR(255) PRIMARY KEY,\n", "        customer_id VARCHAR(255),\n", "        order_total DECIMAL(10,2),\n", "        order_status VARCHAR(50),\n", "        payment_method VARCHAR(50),\n", "        shipping_address TEXT,\n", "        created_at TIMESTAMP,\n", "        updated_at TIMESTAMP\n", "    );\n", "    \"\"\"\n", "    \n", "    # Order Items table\n", "    order_items_sql = \"\"\"\n", "    CREATE TABLE IF NOT EXISTS order_items (\n", "        order_item_id VARCHAR(255) PRIMARY KEY,\n", "        order_id VARCHAR(255),\n", "        product_id VARCHAR(255),\n", "        quantity INTEGER,\n", "        price DECIMAL(10,2)\n", "    );\n", "    \"\"\"\n", "    \n", "    # Payments table\n", "    payments_sql = \"\"\"\n", "    CREATE TABLE IF NOT EXISTS payments (\n", "        payment_id VARCHAR(255) PRIMARY KEY,\n", "        order_id VARCHAR(255),\n", "        customer_id VARCHAR(255),\n", "        amount DECIMAL(10,2),\n", "        payment_method VARCHAR(50),\n", "        payment_status VARCHAR(50),\n", "        created_at DATE\n", "    );\n", "    \"\"\"\n", "    \n", "    # Reviews table\n", "    reviews_sql = \"\"\"\n", "    CREATE TABLE IF NOT EXISTS reviews (\n", "        review_id VARCHAR(255) PRIMARY KEY,\n", "        product_id VARCHAR(255),\n", "        customer_id VARCHAR(255),\n", "        rating INTEGER,\n", "        created_at DATE\n", "    );\n", "    \"\"\"\n", "    \n", "    tables = {\n", "        'orders': orders_sql,\n", "        'order_items': order_items_sql,\n", "        'payments': payments_sql,\n", "        'reviews': reviews_sql\n", "    }\n", "    \n", "    for table_name, sql in tables.items():\n", "        print(f\"Creating table: {table_name}\")\n", "        run_redshift_statement(sql)\n", "        print(f\"Created table: {table_name}\")\n", "        print(\"-------------\")\n", "\n", "# Create tables\n", "create_tables()\n"]}, {"cell_type": "markdown", "id": "24f5f1ad", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Load Data from S3 into Redshift Tables\n", "\n", "Use the COPY command to efficiently load data from S3 CSV files into our Redshift tables:\n"]}, {"cell_type": "code", "execution_count": null, "id": "1b248290", "metadata": {}, "outputs": [], "source": ["# Load data from S3 into Redshift tables\n", "def load_data_from_s3():\n", "    \"\"\"Load data from S3 CSV files into Redshift tables\"\"\"\n", "    \n", "    tables_and_files = {\n", "        'orders': 'orders.csv',\n", "        'order_items': 'order_items.csv',\n", "        'payments': 'payments.csv',\n", "        'reviews': 'reviews.csv'\n", "    }\n", "    \n", "    for table_name, file_name in tables_and_files.items():\n", "        print(f\"Loading data into {table_name} from {file_name}\")\n", "        \n", "        copy_sql = f\"\"\"\n", "        COPY {table_name}\n", "        FROM 's3://{S3_BUCKET}/{file_name}'\n", "        IAM_ROLE '{redshift_role_arn}'\n", "        CSV\n", "        IGNOREHEADER 1\n", "        DELIMITER ','\n", "        REGION '{region}';\n", "        \"\"\"\n", "        \n", "        try:\n", "            run_redshift_statement(copy_sql)\n", "            print(f\"Loaded data into {table_name}\")\n", "        except Exception as e:\n", "            print(f\"Error loading data into {table_name}: {str(e)}\")\n", "\n", "# Load data from S3\n", "load_data_from_s3()"]}, {"cell_type": "markdown", "id": "eea721a6", "metadata": {}, "source": ["## Step 5: Create Bedrock Knowledge Base with Redshift Data Source\n", "\n", "Now we'll create the Bedrock Knowledge Base configured to use our Redshift data as a structured data source.\n"]}, {"cell_type": "code", "execution_count": null, "id": "7891a188", "metadata": {}, "outputs": [], "source": ["# Configure Knowledge Base parameters\n", "kb_name = f\"redshift-structured-kb-{suffix}\"\n", "kb_description = \"Structured Knowledge Base for e-commerce data queries using Redshift\"\n", "generation_model = \"anthropic.claude-3-5-haiku-20241022-v1:0\"\n", "\n", "print(f\"Knowledge Base Name: {kb_name}\")\n"]}, {"cell_type": "markdown", "id": "83d7f9bc", "metadata": {}, "source": ["Amazon Bedrock Knowledge Bases uses a service role to connect knowledge bases to structured data stores, retrieve data from these data stores, and generate SQL queries based on user queries and the structure of the data stores. There are several access patterns based on if you're using Redshift Serverless vs Redshift Provisioned Cluster. In this notebook, let's use `IAM Role + Redshift Serverless WorkGroup` access pattern."]}, {"cell_type": "markdown", "id": "3e04efa8", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Configure Knowledge Base Configuration Parameters\n", "\n", "Set up the parameters for creating our structured Knowledge Base using Redshift Serverless:\n"]}, {"cell_type": "code", "execution_count": null, "id": "65137e47", "metadata": {}, "outputs": [], "source": ["# Configure Knowledge Base parameters for Redshift Serverless with IAM authentication\n", "kb_config_param = {\n", "    \"type\": \"SQL\",\n", "    \"sqlKnowledgeBaseConfiguration\": {\n", "        \"type\": \"REDSHIFT\",\n", "        \"redshiftConfiguration\": {\n", "            \"storageConfigurations\": [{\n", "                \"type\": \"REDSHIFT\",\n", "                \"redshiftConfiguration\": {\n", "                    \"databaseName\": REDSHIFT_DATABASE\n", "                }\n", "            }],\n", "            \"queryEngineConfiguration\": {\n", "                \"type\": \"SERVERLESS\",\n", "                \"serverlessConfiguration\": {\n", "                    \"workgroupArn\": workgroup_arn,\n", "                    \"authConfiguration\": {}\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "kb_config_param['sqlKnowledgeBaseConfiguration']['redshiftConfiguration']['queryEngineConfiguration']['serverlessConfiguration']['authConfiguration']['type'] = \"IAM\"\n", "\n", "print(f\"Knowledge Base configuration: {kb_config_param}\")"]}, {"cell_type": "markdown", "id": "db08d944", "metadata": {}, "source": ["### Create the Structured Knowledge Base\n", "\n", "Use the BedrockStrcuturedKnowledgeBase utility to create the Knowledge Base with all necessary components"]}, {"cell_type": "code", "execution_count": null, "id": "932cac78", "metadata": {}, "outputs": [], "source": ["try:\n", "    structured_kb = BedrockStructuredKnowledgeBase(\n", "        kb_name=kb_name,\n", "        kb_description=kb_description,\n", "        workgroup_arn=workgroup_arn,\n", "        kbConfigParam=kb_config_param,\n", "        generation_model=generation_model,\n", "        suffix=suffix\n", "    )\n", "    \n", "    print(\"Knowledge Base created successfully!\")\n", "    kb_id = structured_kb.get_knowledge_base_id()\n", "    print(f\"Knowledge Base ID: {kb_id}\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error creating Knowledge Base: {str(e)}\")\n", "    raise\n"]}, {"cell_type": "markdown", "id": "8c22c74e", "metadata": {}, "source": ["## Step 6: Database Access Configuration for IAM Role + Redshift Serverless WorkGroup\n"]}, {"cell_type": "markdown", "id": "00549fb8", "metadata": {}, "source": ["For the IAM Role + Redshift Serverless WorkGroup access pattern, you must configure database-level permissions for the IAM role used by Bedrock Knowledge Base.\n", "\n", "1. **Create IAM-based database user**: Map the IAM role to a database user in Redshift\n", "2. **Grant appropriate permissions**: Provide SELECT access to the relevant schemas and tables\n"]}, {"cell_type": "code", "execution_count": null, "id": "260dbdbb", "metadata": {}, "outputs": [], "source": ["# Extract the IAM role name from the ARN for database user creation\n", "kb_details = structured_kb.knowledge_base\n", "\n", "bedrock_role_arn = kb_details['roleArn']\n", "bedrock_role_name = bedrock_role_arn.split('/')[-1]\n", "print(f\"   Extracted Role Name: {bedrock_role_name}\")"]}, {"cell_type": "markdown", "id": "0f8a6f6b", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Create IAM-based Database User in Redshift\n", "\n", "Create a database user mapped to the Bedrock Knowledge Base IAM role to enable database access\n"]}, {"cell_type": "code", "execution_count": null, "id": "0a7e47fb", "metadata": {}, "outputs": [], "source": ["\n", "# Create the IAM user in Redshift (this is the critical missing step!)\n", "create_user_sql = f'CREATE USER \"IAMR:{bedrock_role_name}\" WITH PASSWORD DISABLE;'\n", "\n", "try:\n", "    print(f\"Creating user: IAMR:{bedrock_role_name}\")\n", "    run_redshift_statement(create_user_sql)\n", "    print(\"IAM user created successfully!\")\n", "except Exception as e:\n", "    if \"already exists\" in str(e).lower():\n", "        print(\"User already exists, continuing...\")\n", "    else:\n", "        print(f\"Error creating user: {str(e)}\")\n", "        raise"]}, {"cell_type": "markdown", "id": "c64bdc9d", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Grant Database Permissions\n", "\n", "Grant SELECT permissions on all tables to the IAM-based database user\n"]}, {"cell_type": "code", "execution_count": null, "id": "53ab5fc1", "metadata": {}, "outputs": [], "source": ["# Grant SELECT on all tables in public schema\n", "grant_select_sql = f'GRANT SELECT ON ALL TABLES IN SCHEMA public TO \"IAMR:{bedrock_role_name}\";'\n", "\n", "try:\n", "    print(f\"Granting SELECT permissions to: IAMR:{bedrock_role_name}\")\n", "    run_redshift_statement(grant_select_sql)\n", "    print(\"SELECT permissions granted successfully!\")\n", "except Exception as e:\n", "    print(f\"Error granting permissions: {str(e)}\")\n", "    raise"]}, {"cell_type": "markdown", "id": "1f3bd6df", "metadata": {}, "source": ["## Step 7: Start Ingestion Job\n", "\n", "Now that the database permissions are properly configured, let's start the ingestion job to sync the data from the Redshift database."]}, {"cell_type": "code", "execution_count": null, "id": "0097a5f2", "metadata": {}, "outputs": [], "source": ["# Wait a bit for the Knowledge Base to be fully ready\n", "time.sleep(60)\n", "structured_kb.start_ingestion_job()"]}, {"cell_type": "markdown", "id": "514efa82", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Store Knowledge Base Configuration\n", "\n", "Store the Knowledge Base ID and related configuration for use in the main agentic RAG notebook\n"]}, {"cell_type": "code", "execution_count": null, "id": "8c43f74d", "metadata": {}, "outputs": [], "source": ["# Store the structured knowledge base configuration\n", "structured_kb_id = structured_kb.get_knowledge_base_id()\n", "structured_kb_region = region\n", "structured_workgroup_arn = workgroup_arn\n", "structured_database_name = REDSHIFT_DATABASE\n", "\n", "# Store variables for use in main notebook\n", "%store structured_kb_id\n", "%store structured_kb_region\n", "%store structured_workgroup_arn\n", "%store structured_database_name\n", "\n", "print(\"=\"*60)\n", "print(f\"Structured Knowledge Base ID: {structured_kb_id}\")\n", "print(f\"Region: {structured_kb_region}\")\n", "print(f\"Workgroup ARN: {structured_workgroup_arn}\")\n", "print(f\"Database Name: {structured_database_name}\")\n", "print(\"=\"*60)\n", "print(\"Configuration stored successfully!\")\n"]}, {"cell_type": "markdown", "id": "d8689903", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Clean Up Resources\n", "\n", "**Important**: To avoid ongoing AWS charges, uncomment and run the cells below to delete all created resources when you're finished with the project.\n", "\n", "### Delete Knowledge Base and Associated Resources\n", "\n", "This will delete the Knowledge Base, data sources, and IAM roles/policies:\n"]}, {"cell_type": "code", "execution_count": null, "id": "2299290b", "metadata": {}, "outputs": [], "source": ["# # Delete resources\n", "# print(\"===============================Deleteing resources ==============================\\n\")\n", "structured_kb.delete_kb( delete_iam_roles_and_policies=True)"]}, {"cell_type": "markdown", "id": "7c3082ad", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Delete Redshift Infrastructure\n", "\n", "This comprehensive cleanup function will delete all Redshift-related resources including workgroup, namespace, S3 bucket, and IAM roles:\n"]}, {"cell_type": "code", "execution_count": null, "id": "0c2089dc", "metadata": {}, "outputs": [], "source": ["def cleanup_redshift_environment():\n", "    \"\"\"\n", "    Delete all Redshift-related resources including workgroup, namespace, S3 bucket, and IAM role.\n", "    Uses the existing variables defined in the notebook.\n", "    \"\"\"\n", "    import boto3\n", "    import time\n", "    \n", "    # Initialize clients\n", "    session = boto3.session.Session()\n", "    region = session.region_name\n", "    redshift_client = boto3.client('redshift-serverless', region_name=region)\n", "    iam_client = boto3.client('iam')\n", "    s3 = boto3.resource('s3')\n", "    s3_client = boto3.client('s3')\n", "    \n", "    def wait_for_workgroup_deleted(name, poll_interval=10, max_attempts=60):\n", "        \"\"\"Wait until workgroup is completely deleted\"\"\"\n", "        print(f\"  Waiting for workgroup {name} to be deleted...\")\n", "        attempts = 0\n", "        while attempts < max_attempts:\n", "            try:\n", "                wg = redshift_client.get_workgroup(workgroupName=name)[\"workgroup\"]\n", "                status = wg[\"status\"]\n", "                print(f\"    Workgroup status: {status}\")\n", "                if status == \"DELETED\":\n", "                    break\n", "                time.sleep(poll_interval)\n", "                attempts += 1\n", "            except redshift_client.exceptions.ResourceNotFoundException:\n", "                print(\"    Workgroup deleted successfully\")\n", "                return\n", "        \n", "        if attempts >= max_attempts:\n", "            print(f\"    Warning: Timeout waiting for workgroup deletion after {max_attempts * poll_interval} seconds\")\n", "    \n", "    def wait_for_namespace_deleted(name, poll_interval=10, max_attempts=60):\n", "        \"\"\"Wait until namespace is completely deleted\"\"\"\n", "        print(f\"  Waiting for namespace {name} to be deleted...\")\n", "        attempts = 0\n", "        while attempts < max_attempts:\n", "            try:\n", "                redshift_client.get_namespace(namespaceName=name)\n", "                print(f\"    Namespace still exists, waiting...\")\n", "                time.sleep(poll_interval)\n", "                attempts += 1\n", "            except redshift_client.exceptions.ResourceNotFoundException:\n", "                print(\"    Namespace deleted successfully\")\n", "                return\n", "        \n", "        if attempts >= max_attempts:\n", "            print(f\"    Warning: Timeout waiting for namespace deletion after {max_attempts * poll_interval} seconds\")\n", "    \n", "    print(\"Starting Redshift environment cleanup...\")\n", "    print(\"=\" * 60)\n", "    \n", "    # 1. Delete Redshift workgroup first\n", "    print(f\"Step 1: Deleting Redshift workgroup {REDSHIFT_WORKGROUP}\")\n", "    try:\n", "        redshift_client.delete_workgroup(workgroupName=REDSHIFT_WORKGROUP)\n", "        print(\"  Workgroup deletion initiated\")\n", "        wait_for_workgroup_deleted(REDSHIFT_WORKGROUP)\n", "    except redshift_client.exceptions.ResourceNotFoundException:\n", "        print(\"  Workgroup already deleted or does not exist\")\n", "    except Exception as e:\n", "        print(f\"  Error deleting workgroup: {str(e)}\")\n", "    \n", "    # 2. Delete Redshift namespace\n", "    print(f\"\\nStep 2: Deleting Redshift namespace {REDSHIFT_NAMESPACE}\")\n", "    try:\n", "        redshift_client.delete_namespace(namespaceName=REDSHIFT_NAMESPACE)\n", "        print(\"  Namespace deletion initiated\")\n", "        wait_for_namespace_deleted(REDSHIFT_NAMESPACE)\n", "    except redshift_client.exceptions.ResourceNotFoundException:\n", "        print(\"  Namespace already deleted or does not exist\")\n", "    except Exception as e:\n", "        print(f\"  Error deleting namespace: {str(e)}\")\n", "    \n", "    # 3. Empty and delete S3 bucket\n", "    print(f\"\\nStep 3: Deleting S3 bucket {S3_BUCKET}\")\n", "    try:\n", "        bucket = s3.Bucket(S3_BUCKET)\n", "        \n", "        # Check if bucket exists\n", "        s3_client.head_bucket(Bucket=S3_BUCKET)\n", "        \n", "        # Delete all objects in the bucket\n", "        print(\"  Emptying bucket contents...\")\n", "        objects_to_delete = []\n", "        for obj in bucket.objects.all():\n", "            objects_to_delete.append({'Key': obj.key})\n", "        \n", "        if objects_to_delete:\n", "            bucket.delete_objects(Delete={'Objects': objects_to_delete})\n", "            print(f\"    Deleted {len(objects_to_delete)} objects\")\n", "        else:\n", "            print(\"    Bucket was already empty\")\n", "        \n", "        # Delete the bucket\n", "        print(\"  Deleting bucket...\")\n", "        bucket.delete()\n", "        print(\"  S3 bucket deleted successfully\")\n", "        \n", "    except s3_client.exceptions.NoSuchBucket:\n", "        print(\"  S3 bucket already deleted or does not exist\")\n", "    except Exception as e:\n", "        print(f\"  Error deleting S3 bucket: {str(e)}\")\n", "    \n", "    # 4. Delete IAM role and policies\n", "    print(f\"\\nStep 4: Deleting IAM role {redshift_role_arn.split('/')[-1]}\")\n", "    role_name = redshift_role_arn.split('/')[-1]\n", "    try:\n", "        # Check if role exists\n", "        iam_client.get_role(RoleName=role_name)\n", "        \n", "        # Detach managed policies\n", "        print(\"  Detaching managed policies...\")\n", "        attached_policies = iam_client.list_attached_role_policies(RoleName=role_name)['AttachedPolicies']\n", "        for policy in attached_policies:\n", "            policy_arn = policy['PolicyArn']\n", "            iam_client.detach_role_policy(RoleName=role_name, PolicyArn=policy_arn)\n", "            print(f\"    Detached policy: {policy['PolicyName']}\")\n", "            \n", "            # Delete custom policies (not AWS managed)\n", "            if not policy_arn.startswith('arn:aws:iam::aws:policy/'):\n", "                try:\n", "                    iam_client.delete_policy(PolicyArn=policy_arn)\n", "                    print(f\"    Deleted custom policy: {policy['PolicyName']}\")\n", "                except Exception as e:\n", "                    print(f\"    Could not delete policy {policy['PolicyName']}: {str(e)}\")\n", "        \n", "        # Delete inline policies\n", "        print(\"  Deleting inline policies...\")\n", "        inline_policies = iam_client.list_role_policies(RoleName=role_name)['PolicyNames']\n", "        for policy_name in inline_policies:\n", "            iam_client.delete_role_policy(RoleName=role_name, PolicyName=policy_name)\n", "            print(f\"    Deleted inline policy: {policy_name}\")\n", "        \n", "        # Delete the role\n", "        iam_client.delete_role(RoleName=role_name)\n", "        print(\"  <PERSON><PERSON> role deleted successfully\")\n", "        \n", "    except iam_client.exceptions.NoSuchEntityException:\n", "        print(\"  IAM role already deleted or does not exist\")\n", "    except Exception as e:\n", "        print(f\"  Error deleting IAM role: {str(e)}\")\n", "    \n", "    print(\"\\n\" + \"=\" * 60)\n", "    print(\"Redshift environment cleanup completed\")\n", "    print(\"\\nSummary of deleted resources:\")\n", "    print(f\"  - Redshift Workgroup: {REDSHIFT_WORKGROUP}\")\n", "    print(f\"  - Redshift Namespace: {REDSHIFT_NAMESPACE}\")\n", "    print(f\"  - S3 Bucket: {S3_BUCKET}\")\n", "    print(f\"  - IAM Role: {role_name}\")\n", "\n", "# Usage:\n", "cleanup_redshift_environment()"]}, {"cell_type": "markdown", "id": "acb88b79", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Summary\n", "\n", "If all the above cells executed successfully, you have:\n", "\n", "- Created Amazon Redshift Serverless namespace and workgroup infrastructure\n", "- Set up an S3 bucket and uploaded sample structured data  \n", "- Created database tables and loaded data from S3 using COPY commands\n", "- Created an Amazon Bedrock Knowledge Base configured for structured data queries\n", "- Configured IAM-based database access with proper permissions\n", "- Successfully completed the data ingestion job \n", "- Stored the Knowledge Base configuration for use in the main notebook\n", "\n", "\n", "You can now proceed to the main `1-prerequisites-unstructured-kb` notebook \n"]}, {"cell_type": "markdown", "id": "cd537f69", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}