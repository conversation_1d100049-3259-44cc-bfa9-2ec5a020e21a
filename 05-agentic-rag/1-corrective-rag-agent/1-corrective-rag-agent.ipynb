{"cells": [{"cell_type": "markdown", "id": "c5a91307-9084-4947-843a-8f6b1d9c7937", "metadata": {}, "source": ["# Build a Corrective RAG with Strand Agents\n", "\n", "In this notebook, we'll construct a corrective Retrieval-Augmented Generation (RAG) system using Strand Agents. We'll define new tools, integrate external APIs, and orchestrate them into an intelligent agent workflow.\n", "\n", "<div style=\"text-align:left\">\n", "    <img src=\"images/architecture.png\" width=\"100%\" />\n", "</div>"]}, {"cell_type": "markdown", "id": "2118dbad-3f9d-491a-be35-e821d320afb3", "metadata": {}, "source": ["## Setup and Prerequisites\n", "\n", "### Requirements\n", "\n", "Before running this notebook, ensure the following prerequisites are completed:\n", "\n", "- Run the `prerequisites` notebook in this folder\n", "- Python 3.10 or later\n", "- An AWS account\n", "- Anthropic Claude 3.7 enabled in Amazon Bedrock\n", "- IAM role with permissions for:\n", "  - Amazon Bedrock Knowledge Base\n", "  - Amazon S3\n", "  - Amazon OpenSearch Serverless\n", "\n", "We’ll start by installing all required packages."]}, {"cell_type": "code", "execution_count": null, "id": "79bb87b5-8223-4098-9e2b-417a28b172e0", "metadata": {}, "outputs": [], "source": ["%pip install -qr requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "f9e278a2-2a2f-40fd-bd31-838b67f42a70", "metadata": {}, "outputs": [], "source": ["import asyncio, re\n", "from strands import Agent, tool\n", "from langchain.schema import Document\n", "from strands_tools import agent_graph, retrieve\n", "from langchain_aws import ChatBedrockConverse, BedrockEmbeddings\n", "from ragas import SingleTurnSample\n", "from ragas.metrics import LLMContextPrecisionWithoutReference\n", "from ragas.llms import LangchainLLMWrapper\n", "from ragas.embeddings import LangchainEmbeddingsWrapper"]}, {"cell_type": "code", "execution_count": null, "id": "80d7d88b-4fac-4e45-b53c-dc3b8adc0bd8", "metadata": {}, "outputs": [], "source": ["eval_modelId = 'us.anthropic.claude-3-7-sonnet-20250219-v1:0'\n", "thinking_params= {\n", "    \"thinking\": {\n", "        \"type\": \"disabled\"\n", "    }\n", "}\n", "llm_for_evaluation = ChatBedrockConverse(model_id=eval_modelId, additional_model_request_fields=thinking_params)\n", "llm_for_evaluation = LangchainLLMWrapper(llm_for_evaluation)\n", "bedrock_embeddings = BedrockEmbeddings(model_id=\"amazon.titan-embed-text-v2:0\")\n", "bedrock_embeddings = LangchainEmbeddingsWrapper(bedrock_embeddings)"]}, {"cell_type": "markdown", "id": "2e9dde56-27c7-4e22-8285-7f7eeaad0d6e", "metadata": {}, "source": ["### Set Up Tavily Web Search API\n", "\n", "We will use the [Tavily](https://tavily.com/) API for external web searches when the knowledge base content is insufficient. You’ll be prompted to securely input your API key.\n"]}, {"cell_type": "code", "execution_count": null, "id": "77246855-1189-48b1-9e7a-27e3187d9ca3", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "def _set_env(key: str):\n", "    if key not in os.environ:\n", "        os.environ[key] = getpass.getpass(f\"{key}:\")\n", "        \n", "_set_env(\"TAVILY_API_KEY\")"]}, {"cell_type": "code", "execution_count": null, "id": "fae26029-f712-4e95-bb25-d95b2af0d98c", "metadata": {}, "outputs": [], "source": ["from langchain_community.tools.tavily_search import TavilySearchResults\n", "web_search_tool = TavilySearchResults(k=3)"]}, {"cell_type": "markdown", "id": "886317bd-4de2-4d28-a044-04378db53a04", "metadata": {}, "source": ["## Define <PERSON>ls for the Agent\n", "\n", "Next, we define the tools the agent will use. These include:\n", "\n", "- A relevance evaluator that scores the retrieved chunks using RAGAs\n", "- A web search fallback to enhance answers when KB content is not relevant\n"]}, {"cell_type": "code", "execution_count": null, "id": "9ddfc9d0-59c4-42cf-9ede-a30e949d4531", "metadata": {}, "outputs": [], "source": ["@tool\n", "def check_chunks_relevance(results: str, question: str):\n", "    \"\"\"\n", "    Evaluates the relevance of retrieved chunks to the user question using RAGAs.\n", "\n", "    Args:\n", "        results (str): Retrieval output as a string with 'Score:' and 'Content:' patterns.\n", "        question (str): Original user question.\n", "\n", "    Returns:\n", "        dict: A binary score ('yes' or 'no') and the numeric relevance score, or an error message.\n", "    \"\"\"\n", "    try:\n", "        if not results or not isinstance(results, str):\n", "            raise ValueError(\"Invalid input: 'results' must be a non-empty string.\")\n", "        if not question or not isinstance(question, str):\n", "            raise ValueError(\"Invalid input: 'question' must be a non-empty string.\")\n", "\n", "        # Extract content chunks using regex\n", "        pattern = r\"Score:.*?\\nContent:\\s*(.*?)(?=Score:|\\Z)\"\n", "        docs = [chunk.strip() for chunk in re.findall(pattern, results, re.DOTALL)]\n", "\n", "        if not docs:\n", "            raise ValueError(\"No valid content chunks found in 'results'.\")\n", "\n", "        # Prepare evaluation sample\n", "        sample = SingleTurnSample(\n", "            user_input=question,\n", "            response=\"placeholder-response\",  # required dummy response\n", "            retrieved_contexts=docs\n", "        )\n", "\n", "        # Evaluate using context precision metric\n", "        scorer = LLMContextPrecisionWithoutReference(llm=llm_for_evaluation)\n", "        score = asyncio.run(scorer.single_turn_ascore(sample))\n", "\n", "        print(\"------------------------\")\n", "        print(\"Context evaluation\")\n", "        print(\"------------------------\")\n", "        print(f\"chunk_relevance_score: {score}\")\n", "\n", "        return {\n", "            \"chunk_relevance_score\": \"yes\" if score > 0.5 else \"no\",\n", "            \"chunk_relevance_value\": score\n", "        }\n", "\n", "    except Exception as e:\n", "        return {\n", "            \"error\": str(e),\n", "            \"chunk_relevance_score\": \"unknown\",\n", "            \"chunk_relevance_value\": None\n", "        }"]}, {"cell_type": "code", "execution_count": null, "id": "6d4c9c91-e40c-4e54-bcd3-52405209e4b3", "metadata": {}, "outputs": [], "source": ["@tool\n", "def web_search(query):\n", "    \"\"\"\n", "    Perform web search based on the query and return results as Documents.\n", "    Only to be used if chunk_relevance_score is no.\n", "\n", "    Args:\n", "        query (str): The user question or rephrased query.\n", "\n", "    Returns:\n", "        dict: {\n", "            \"documents\": [Document, ...]  # list of Document objects with web results\n", "        }\n", "    \"\"\"\n", "\n", "    print(\"---WEB SEARCH---\")\n", "\n", "    # Perform web search\n", "    docs = web_search_tool.invoke({\"query\": query})\n", "\n", "    # Convert each result into a Document object\n", "    documents = [Document(page_content=d[\"content\"]) for d in docs]\n", "\n", "    return {\n", "        \"documents\": documents\n", "    }\n"]}, {"cell_type": "markdown", "id": "9da84487-d633-4873-a8dc-64d43ae8dd2d", "metadata": {}, "source": ["## Create the Agent\n", "\n", "We initialize the agent with access to our tools and provide it with a system prompt to define its purpose and reasoning logic.\n", "\n", "### Environment Configuration\n", "\n", "Set the Knowledge Base ID, AWS region, and relevance threshold score as environment variables for the **retrive** tool to fetch."]}, {"cell_type": "code", "execution_count": null, "id": "aa24e74f-8d37-4cc2-ae94-c221f58e8981", "metadata": {}, "outputs": [], "source": ["%store -r kb_id\n", "%store -r kb_region"]}, {"cell_type": "code", "execution_count": null, "id": "29c6b8c6-0bac-4660-9eb6-5058979665b7", "metadata": {}, "outputs": [], "source": ["os.environ[\"KNOWLEDGE_BASE_ID\"] = kb_id #Change if you are using a different KB than the created in the prerequisites notebook\n", "os.environ[\"AWS_REGION\"] = kb_region #Change if needed\n", "os.environ[\"MIN_SCORE\"] = \"0.2\""]}, {"cell_type": "markdown", "id": "5fc737bb-db78-4aa4-b501-0a1379aa46bd", "metadata": {}, "source": ["### Instantiate a simple RAG Agent\n", "Let's create an agent which only uses the retrieve tool so we can compare it's results with the corrective RAG agent."]}, {"cell_type": "code", "execution_count": null, "id": "90217769-a462-41cb-af1c-1edcf82dcbb8", "metadata": {}, "outputs": [], "source": ["simple_agent = Agent(\n", "    tools=[retrieve],\n", "    system_prompt=\"You are a rag agent. When a user asks you a question you will check it in your knowledge base. When you use the retrieve tool, do not modify the users question, pass as is.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fa13b4a7-c5c1-424b-b686-8401ecc0085f", "metadata": {}, "outputs": [], "source": ["result = simple_agent(\"Which is my medical insurance provider name and Allstate Insurance Company's phone number?\")"]}, {"cell_type": "markdown", "id": "6077d0f6-1329-4712-8856-76f44cb5b926", "metadata": {}, "source": ["You will notice the agent is not able to find the phone number in it's knowledge base. "]}, {"cell_type": "markdown", "id": "0fdc424d-8f27-4aab-a745-122fa8d6a7f8", "metadata": {}, "source": ["### Instantiate the corrective RAG Agent\n", "\n", "We define the agent’s behavior using a system prompt and register the custom tools. The agent follows this reasoning flow:\n", "\n", "1. Try to answer using the knowledge base\n", "2. Evaluate chunk relevance using RAGAS\n", "3. If content is not relevant, trigger web search\n"]}, {"cell_type": "code", "execution_count": null, "id": "22b865b0-5470-44fd-862d-1065fea338f9", "metadata": {}, "outputs": [], "source": ["corrective_agent = Agent(\n", "    tools=[web_search, retrieve, check_chunks_relevance],\n", "    system_prompt=\"You are a corrective rag agent. When a user asks you a question you will first check it in your knowledge base (if you can't answer it from the current conversation memory). You will evaluate if the returned chunks are relevant using a relevance score tool. If they are not relevant to the question you will use your web search tool to gather additional data to answer the question. You are an agent in charge of looking for information in your knoweldege base and if the results are not relevant using ragas, use a web search. When you use the retrieve tool, do not modify or break down the users question, pass as is.\",\n", ")"]}, {"cell_type": "markdown", "id": "e2595333-3ad9-4c38-a953-1e42ef77ce7a", "metadata": {}, "source": ["## Query the Agent\n", "\n", "Let’s ask the agent a question that may require both internal and external data sources. It will:\n", "\n", "1. Search the knowledge base\n", "2. Score the relevance of results\n", "3. Supplement with web search if needed\n", "4. Return a complete answer\n"]}, {"cell_type": "code", "execution_count": null, "id": "604d44a5-b0d1-4c39-98a1-e518ca80bee6", "metadata": {}, "outputs": [], "source": ["result = corrective_agent(\"Which is my medical insurance provider name and Allstate Insurance Company's phone number?\")"]}, {"cell_type": "markdown", "id": "0964851f-2911-4791-a02e-5997459c124b", "metadata": {}, "source": ["### View Execution Summary\n", "\n", "Retrieve evaluation metrics from the agent's response for transparency and performance monitoring.\n"]}, {"cell_type": "code", "execution_count": null, "id": "3370d99b-09e1-4d55-8445-6313ac13ba8c", "metadata": {}, "outputs": [], "source": ["result.metrics.get_summary()"]}, {"cell_type": "markdown", "id": "573d0664-d700-4a72-b465-b1b7c3f3bc70", "metadata": {}, "source": ["## Clean up the resources\n", "\n", "When you have finished with this notebook, return to the previous notebook to delete the Knowledge Base created and not incurr extra costs!"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 5}