{"cells": [{"cell_type": "markdown", "id": "02e77727-1e66-4cd6-995b-675812a04004", "metadata": {}, "source": ["# Create an Amazon Bedrock Knowledge Base"]}, {"cell_type": "markdown", "id": "b9797fa0-c13b-4700-befc-3c7a2d72cb08", "metadata": {}, "source": ["This notebook demonstrates how to create and configure an Amazon Bedrock Knowledge Base. \n", "\n", "The Knowledge Base integrates Amazon S3 as the data source for onboarding documents and uses Amazon OpenSearch Serverless as the vector store. It enables retrieval-augmented generation (RAG) by powering intelligent queries over structured and unstructured HR content like onboarding guides, policies, and FAQs.\n", "\n", "If you want to create the Knowledge Base with your own documents, change the documents uploaded in the **Create the Amazon S3 bucket and upload the sample documents** section. This component will be used in the next notebook of this folder."]}, {"cell_type": "markdown", "id": "ceb26d4a-69a3-4d5a-a726-73e0def1ec46", "metadata": {}, "source": ["## Setup and prerequisites\n", "\n", "### Prerequisites\n", "* Complete the prerequisites notebook located in this folder\n", "* Python 3.10+\n", "* AWS account\n", "* Amazon Nova Micro enabled on Amazon Bedrock\n", "* IAM role with permissions to create Amazon Bedrock Knowledge Base, Amazon S3 bucket, Amazon OpenSearch Serverless\n", "\n", "Let's now install the requirement packages and define the needed clients to create our Amazon Bedrock Knowledge Base:"]}, {"cell_type": "code", "execution_count": null, "id": "3e0fe3ca-559b-44f3-a2d7-7a44e418d1be", "metadata": {}, "outputs": [], "source": ["%pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "ffe117ad-bc1a-4eea-9e55-8fac15f32628", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import time\n", "import uuid\n", "import boto3\n", "import logging\n", "import requests\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "id": "25b6eff2-6f40-4255-af06-9f59c21c9916", "metadata": {}, "outputs": [], "source": ["s3_client = boto3.client('s3')\n", "sts_client = boto3.client('sts')\n", "session = boto3.session.Session()\n", "region =  session.region_name\n", "logging.basicConfig(format='[%(asctime)s] p%(process)s {%(filename)s:%(lineno)d} %(levelname)s - %(message)s', level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "bedrock_agent_runtime_client = boto3.client(\"bedrock-agent-runtime\")"]}, {"cell_type": "code", "execution_count": null, "id": "663ca680-3b4c-4263-9418-2ce4715ba003", "metadata": {}, "outputs": [], "source": ["# Get the current timestamp\n", "current_time = time.time()\n", "# Format the timestamp as a string\n", "timestamp_str = time.strftime(\"%Y%m%d%H%M%S\", time.localtime(current_time))[-7:]\n", "# Create the suffix using the timestamp\n", "suffix = f\"{timestamp_str}\""]}, {"cell_type": "markdown", "id": "d848ef59-073f-4862-b4e2-79037f234e4a", "metadata": {}, "source": ["## Download Amazon Bedrock Knowledge Bases helper\n", "To expedite the knowledge base configuration and creation we will be downloading the knowledge base utility file. This contains a helper to generate knowledge bases abstracting the multiple API calls that need to be used."]}, {"cell_type": "code", "execution_count": null, "id": "5b49fb14-ca06-4e3a-9865-e973e8f67820", "metadata": {}, "outputs": [], "source": ["url = \"https://raw.githubusercontent.com/aws-samples/amazon-bedrock-samples/main/rag/knowledge-bases/features-examples/utils/knowledge_base.py\"\n", "target_path = \"utils/knowledge_base.py\"\n", "os.makedirs(os.path.dirname(target_path), exist_ok=True)\n", "response = requests.get(url)\n", "with open(target_path, \"w\") as f:\n", "    f.write(response.text)\n", "print(f\"Downloaded Knowledge Bases utils to {target_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f20c4839-2b61-42a8-ba7e-6a03975b3b04", "metadata": {}, "outputs": [], "source": ["from utils.knowledge_base import BedrockKnowledgeBase"]}, {"cell_type": "markdown", "id": "746cafda-c29e-4c49-9581-8a221202ee09", "metadata": {}, "source": ["## Create Amazon Bedrock Knowledge Base\n", "In this section we will configure the Amazon Bedrock Knowledge Base containing the policy documents andn FAQs for employee onboarding. We will be using Amazon Opensearch Serverless Service as the underlying vector store and Amazon S3 as the data source containing the files."]}, {"cell_type": "code", "execution_count": null, "id": "8429af18-795c-46f8-9b32-3ad2dfd0a0a5", "metadata": {}, "outputs": [], "source": ["knowledge_base_name = f\"hr-agent-knowledge-base-{suffix}\"\n", "knowledge_base_description = \"HR Agent Knowledge Base containing onboarding and benefits documentation.\"\n", "foundation_model = \"us.anthropic.claude-3-7-sonnet-20250219-v1:0\""]}, {"cell_type": "markdown", "id": "ee751f31-0f15-4d6d-854e-138a7c163265", "metadata": {}, "source": ["For this notebook, we'll create a Knowledge Base with an Amazon S3 data source."]}, {"cell_type": "code", "execution_count": null, "id": "ba1a4bfa-283d-4a01-8ef2-c6d9ebe9db1c", "metadata": {}, "outputs": [], "source": ["data_bucket_name = f'bedrock-hr-agent-{suffix}-bucket' # replace it with your first bucket name.\n", "data_sources=[{\"type\": \"S3\", \"bucket_name\": data_bucket_name}]"]}, {"cell_type": "markdown", "id": "d78edb71-ba5c-412c-b215-f1a2d86bc91e", "metadata": {}, "source": ["### Create the Amazon S3 bucket and upload the sample documents\n", "For this notebook, we'll create a Knowledge Base with an Amazon S3 data source."]}, {"cell_type": "code", "execution_count": null, "id": "ce186a8f-fca4-45d2-b8ec-d8f5da539777", "metadata": {}, "outputs": [], "source": ["import botocore\n", "import os\n", "\n", "def create_s3_bucket(bucket_name, region=None):\n", "    s3 = boto3.client('s3', region_name=region)\n", "\n", "    try:\n", "        if region is None or region == 'us-east-1':\n", "            s3.create_bucket(Bucket=bucket_name)\n", "        else:\n", "            s3.create_bucket(\n", "                Bucket=bucket_name,\n", "                CreateBucketConfiguration={'LocationConstraint': region}\n", "            )\n", "        print(f\"✅ Bucket '{bucket_name}' created successfully.\")\n", "    except botocore.exceptions.ClientError as e:\n", "        print(f\"❌ Failed to create bucket: {e.response['Error']['Message']}\")\n", "\n", "create_s3_bucket(data_bucket_name, region)\n"]}, {"cell_type": "code", "execution_count": null, "id": "94ba7523-337d-4ce2-a2ec-e228ff0be194", "metadata": {}, "outputs": [], "source": ["def upload_directory(path, bucket_name):\n", "        for root,dirs,files in os.walk(path):\n", "            for file in files:\n", "                file_to_upload = os.path.join(root,file)\n", "                print(f\"uploading file {file_to_upload} to {bucket_name}\")\n", "                s3_client.upload_file(file_to_upload,bucket_name,file)"]}, {"cell_type": "code", "execution_count": null, "id": "81fb3715-0385-4079-8258-7f4d2b8f9b95", "metadata": {}, "outputs": [], "source": ["upload_directory(\"./onboarding_files\", data_bucket_name)"]}, {"cell_type": "markdown", "id": "c3b1ec48-6380-477a-aa02-128bc1a825f3", "metadata": {}, "source": ["### Create the Knowledge Base\n", "We are now going to create the Knowledge Base using the abstraction located in the helper function we previously downloaded."]}, {"cell_type": "code", "execution_count": null, "id": "265beb12-b37a-49f0-975d-1756c9a6ef2c", "metadata": {}, "outputs": [], "source": ["knowledge_base = BedrockKnowledgeBase(\n", "    kb_name=f'{knowledge_base_name}',\n", "    kb_description=knowledge_base_description,\n", "    data_sources=data_sources,\n", "    chunking_strategy = \"FIXED_SIZE\", \n", "    suffix = f'{suffix}-f'\n", ")"]}, {"cell_type": "markdown", "id": "2338477c-a927-4719-af48-541aea041e98", "metadata": {}, "source": ["### Start ingestion job\n", "Once the KB and data source created, we can start the ingestion job for the data source. During the ingestion job, KB will fetch the documents in the data source, pre-process it to extract text, chunk it based on the chunking size provided, create embeddings of each chunk and then write it to the vector database, in this case OSS."]}, {"cell_type": "code", "execution_count": null, "id": "5e4c9393-4948-4955-81c3-90bc94d601e8", "metadata": {}, "outputs": [], "source": ["# ensure that the kb is available\n", "time.sleep(30)\n", "# sync knowledge base\n", "knowledge_base.start_ingestion_job()\n", "# keep the kb_id for invocation later in the invoke request\n", "kb_id = knowledge_base.get_knowledge_base_id()"]}, {"cell_type": "markdown", "id": "924c2de6-20ac-410b-9733-876f95e098f5", "metadata": {}, "source": ["### Test the Knowledge Base\n", "We can now test the Knowledge Base to verify the documents have been ingested properly."]}, {"cell_type": "code", "execution_count": null, "id": "26c38aa4-7efa-48c2-857b-9956305b0cad", "metadata": {}, "outputs": [], "source": ["query = \"Who is the medical insurance provider name?\""]}, {"cell_type": "code", "execution_count": null, "id": "0d078fe5-f160-4a54-90cd-8d695683070e", "metadata": {}, "outputs": [], "source": ["foundation_model = \"amazon.nova-micro-v1:0\"\n", "\n", "response = bedrock_agent_runtime_client.retrieve_and_generate(\n", "    input={\n", "        \"text\": query\n", "    },\n", "    retrieveAndGenerateConfiguration={\n", "        \"type\": \"KNOWLEDGE_BASE\",\n", "        \"knowledgeBaseConfiguration\": {\n", "            'knowledgeBaseId': kb_id,\n", "            \"modelArn\": \"arn:aws:bedrock:{}::foundation-model/{}\".format(region, foundation_model),\n", "            \"retrievalConfiguration\": {\n", "                \"vectorSearchConfiguration\": {\n", "                    \"numberOfResults\":5\n", "                } \n", "            }\n", "        }\n", "    }\n", ")\n", "\n", "print(response['output']['text'],end='\\n'*2)"]}, {"cell_type": "markdown", "id": "cde116b9-d394-4575-9377-171f21e4c8ef", "metadata": {}, "source": ["### Store the Knowledge Base ID\n", "Store the ID of the generated Knowledge Base to use it in other notebooks of the repository."]}, {"cell_type": "code", "execution_count": null, "id": "773e8125-77e1-4aa5-8d64-88594165da38", "metadata": {}, "outputs": [], "source": ["kb_region = region\n", "%store kb_id\n", "%store kb_region"]}, {"cell_type": "markdown", "id": "1c691165-0b67-4fba-9931-2ecf65c7af01", "metadata": {}, "source": ["## Clean up the resources\n", "**If you plan to use other notebooks from this folder, do not delete yet the Knowledge Base as it will be needed.**\n", "\n", "When you are finished with the other notebooks, to avoid additional costs, delete the resources created."]}, {"cell_type": "code", "execution_count": null, "id": "ccb04408-3ff1-4ba1-95a5-85baa72cc31b", "metadata": {}, "outputs": [], "source": ["'''\n", "print(\"===============================Deleting Knowledge Base and associated resources==============================\\n\")\n", "knowledge_base.delete_kb(delete_s3_bucket=True, delete_iam_roles_and_policies=True)\n", "'''"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 5}