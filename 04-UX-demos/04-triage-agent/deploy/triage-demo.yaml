AWSTemplateFormatVersion: '2010-09-09'
Description: AI Triage Agent - React Frontend + FastAPI Backend with S3 and CloudFront

Parameters:
  InstanceType:
    Description: EC2 instance type for backend
    Type: String
    Default: t3.medium
    AllowedValues:
      - t3.small
      - t3.medium
      - t3.large
      - t3.xlarge
  VolumeSize:
    Description: EC2 instance volume size (GB)
    Type: Number
    Default: 20
  AssetsBucketName:
    Description: S3 bucket containing application package
    Type: String
    Default: ''
  AssetsPackageName:
    Description: Application package filename
    Type: String
    Default: 'triage-ui-package.zip'

Conditions:
  HasAssetsBucket:
    !Not [!Equals [!Ref AssetsBucketName, '']]

Resources:
  # S3 Bucket for Frontend
  FrontendBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Delete
    Properties:
      BucketEncryption:
        ServerSideEncryptionConfiguration: 
          - ServerSideEncryptionByDefault: 
              SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true



  CloudFrontOriginAccessControl:
    Type: AWS::CloudFront::OriginAccessControl
    Properties: 
      OriginAccessControlConfig:
        Description: OAC for triage frontend
        Name: !Ref AWS::StackName
        OriginAccessControlOriginType: s3
        SigningBehavior: always
        SigningProtocol: sigv4

  # VPC and Networking
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub ${AWS::StackName}-VPC

  InternetGateway:
    Type: AWS::EC2::InternetGateway
    Properties:
      Tags:
        - Key: Name
          Value: !Sub ${AWS::StackName}-IGW

  InternetGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      InternetGatewayId: !Ref InternetGateway
      VpcId: !Ref VPC

  PublicSubnet:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [0, !GetAZs '']
      CidrBlock: ********/24
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub ${AWS::StackName}-Public-Subnet

  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub ${AWS::StackName}-Public-Routes

  DefaultPublicRoute:
    Type: AWS::EC2::Route
    DependsOn: InternetGatewayAttachment
    Properties:
      RouteTableId: !Ref PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  PublicSubnetRouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PublicRouteTable
      SubnetId: !Ref PublicSubnet

  # Security Group
  BackendSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for backend instance
      SecurityGroupIngress:
        - Description: HTTP from anywhere
          IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
        - Description: SSH
          IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - Description: All outbound traffic
          IpProtocol: -1
          CidrIp: 0.0.0.0/0
      VpcId: !Ref VPC

  # IAM Role for EC2
  BackendInstanceRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
        - arn:aws:iam::aws:policy/AmazonBedrockFullAccess
      Policies:
        - PolicyName: S3FullAccess
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action: s3:*
                Resource: '*'

  BackendInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - !Ref BackendInstanceRole

  # EC2 Instance for Backend
  BackendInstance:
    Type: AWS::EC2::Instance
    Properties:
      SubnetId: !Ref PublicSubnet
      InstanceType: !Ref InstanceType
      IamInstanceProfile: !Ref BackendInstanceProfile
      ImageId: '{{resolve:ssm:/aws/service/canonical/ubuntu/server/jammy/stable/current/amd64/hvm/ebs-gp2/ami-id}}'
      SecurityGroupIds:
        - !Ref BackendSecurityGroup
      BlockDeviceMappings:
        - Ebs:
            VolumeSize: !Ref VolumeSize
            VolumeType: gp3
            DeleteOnTermination: true
            Encrypted: true
          DeviceName: /dev/sda1
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          apt-get update
          apt-get install -y python3-pip python3-venv git awscli unzip nodejs npm

          # Download and extract application package
          cd /home/<USER>
          if [ -n "${AssetsBucketName}" ]; then
            aws s3 cp s3://${AssetsBucketName}/${AssetsPackageName} ./package.zip
            unzip -q package.zip
            mv intelligent-triage-agent triage-agents 2>/dev/null || true
          else
            git clone https://github.com/aws-samples/intelligent-triage-agent.git triage-agents
          fi
          chown -R ubuntu:ubuntu triage-agents

          # Setup Python environment
          cd triage-agents/backend
          python3 -m venv venv
          source venv/bin/activate
          pip install -r requirements.txt

          # Modify main.py to run on port 80
          cd ../backend
          sed -i 's/port=8000/port=80/' main.py

          # Create systemd service
          cat > /etc/systemd/system/triage-backend.service << EOF
          [Unit]
          Description=AI Triage Agent Backend
          After=network.target

          [Service]
          Type=simple
          User=root
          WorkingDirectory=/home/<USER>/triage-agents/backend
          Environment=PATH=/home/<USER>/triage-agents/backend/venv/bin
          Environment=AWS_REGION=${AWS::Region}
          ExecStart=/home/<USER>/triage-agents/backend/venv/bin/python main.py
          Restart=always

          [Install]
          WantedBy=multi-user.target
          EOF

          systemctl enable triage-backend
          systemctl start triage-backend

      Tags:
        - Key: Name
          Value: !Sub ${AWS::StackName}-Backend

  # CloudFront Distribution
  CloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    DependsOn: FrontendBucket
    Properties:
      DistributionConfig:
        Enabled: true
        DefaultRootObject: index.html
        Origins:
          - Id: S3Origin
            DomainName: !GetAtt FrontendBucket.RegionalDomainName
            S3OriginConfig:
              OriginAccessIdentity: ''
            OriginAccessControlId: !GetAtt CloudFrontOriginAccessControl.Id
          - Id: BackendOrigin
            DomainName: !GetAtt BackendInstance.PublicDnsName
            CustomOriginConfig:
              HTTPPort: 80
              OriginProtocolPolicy: http-only
        DefaultCacheBehavior:
          TargetOriginId: S3Origin
          ViewerProtocolPolicy: redirect-to-https
          CachePolicyId: 658327ea-f89d-4fab-a63d-7e88639e58f6
        CacheBehaviors:
          - PathPattern: '/api/*'
            TargetOriginId: BackendOrigin
            ViewerProtocolPolicy: https-only
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - PATCH
              - POST
              - DELETE
          - PathPattern: '/chat'
            TargetOriginId: BackendOrigin
            ViewerProtocolPolicy: https-only
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - PATCH
              - POST
              - DELETE
          - PathPattern: '/models'
            TargetOriginId: BackendOrigin
            ViewerProtocolPolicy: https-only
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
          - PathPattern: '/health'
            TargetOriginId: BackendOrigin
            ViewerProtocolPolicy: https-only
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
          - PathPattern: '/mcp/*'
            TargetOriginId: BackendOrigin
            ViewerProtocolPolicy: https-only
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - PATCH
              - POST
              - DELETE
          - PathPattern: '/sessions/*'
            TargetOriginId: BackendOrigin
            ViewerProtocolPolicy: https-only
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - PATCH
              - POST
              - DELETE
        CustomErrorResponses:
          - ErrorCode: 404
            ResponseCode: 200
            ResponsePagePath: /index.html
          - ErrorCode: 403
            ResponseCode: 200
            ResponsePagePath: /index.html

Outputs:
  FrontendBucketName:
    Description: S3 bucket name for frontend
    Value: !Ref FrontendBucket

  BackendInstanceId:
    Description: Backend EC2 instance ID
    Value: !Ref BackendInstance

  BackendPublicIP:
    Description: Backend public IP
    Value: !GetAtt BackendInstance.PublicIp

  CloudFrontURL:
    Description: CloudFront distribution URL
    Value: !Sub https://${CloudFrontDistribution.DomainName}

  CloudFrontDistributionId:
    Description: CloudFront distribution ID
    Value: !Ref CloudFrontDistribution