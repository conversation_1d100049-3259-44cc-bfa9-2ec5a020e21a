{"nodes": {"start": {"id": "start", "topic": "AWS Health Triage Assistant", "question": "Welcome to AWS Health Triage Assistant! What's your main health concern today?", "ui_display": "Welcome to AWS Health Triage Assistant! What's your main health concern today?", "response_options": ["Emergency or life-threatening situation", "Pain or injury", "Feeling sick or unwell", "Mental health concerns", "Digestive issues", "Heart or chest symptoms", "Breathing difficulties", "Skin problems", "Neurological symptoms", "Women's health", "Men's health", "Child health concerns", "Medication questions", "Preventive care or check-ups", "Sleep problems", "Eye or vision problems", "Other health concern"], "should_reason": true, "reasoning_rules": "If emergency situation, skip personal info and go directly to emergency assessment. Otherwise, collect basic personal information first.", "additional_reasoning": "Emergency situations bypass personal info collection for immediate care", "required": true, "dependencies": [], "children": ["emergency_assessment", "age_collection"], "is_terminal": false, "outcome": null}, "age_collection": {"id": "age_collection", "topic": "Age Information", "question": "To provide you with the best care guidance, I'd like to know your age range.", "ui_display": "To provide you with the best care guidance, I'd like to know your age range.", "response_options": ["Child (0-12 years old)", "Teenager (13-17 years old)", "Young adult (18-30 years old)", "Adult (31-50 years old)", "Middle-aged adult (51-65 years old)", "Senior (65+ years old)", "Prefer not to specify age"], "should_reason": true, "reasoning_rules": "Collect age for appropriate triage. Route to age-specific assessments when relevant.", "additional_reasoning": "Age affects symptom interpretation and care recommendations", "required": true, "dependencies": [], "children": ["gender_collection"], "is_terminal": false, "outcome": null}, "gender_collection": {"id": "gender_collection", "topic": "Gender Information", "question": "What is your gender? This helps me provide more personalized health guidance.", "ui_display": "What is your gender? This helps me provide more personalized health guidance.", "response_options": ["Male", "Female", "Non-binary", "Prefer not to specify gender"], "should_reason": true, "reasoning_rules": "Collect gender for appropriate triage. Route to gender-specific assessments when relevant.", "additional_reasoning": "Gender affects symptom interpretation and care recommendations", "required": true, "dependencies": [], "children": ["pediatric_initial", "pain_injury_assessment", "illness_assessment", "mental_health_initial", "digestive_initial", "cardiac_assessment", "respiratory_initial", "skin_initial", "neurological_initial", "womens_health_initial", "mens_health_initial"], "is_terminal": false, "outcome": null}, "emergency_assessment": {"id": "emergency_assessment", "topic": "Emergency Assessment", "question": "This sounds urgent. Which of these best describes your situation?", "ui_display": "🚨 **Emergency Assessment** - This sounds urgent. Which of these best describes your situation?", "response_options": ["Chest pain or heart attack symptoms", "Difficulty breathing or can't breathe", "Severe bleeding that won't stop", "Loss of consciousness or severe confusion", "Severe allergic reaction", "Stroke symptoms (face drooping, arm weakness, speech difficulty)", "Severe head injury or trauma", "Poisoning or overdose", "Severe burns", "Suicide attempt or thoughts of self-harm", "Other life-threatening emergency"], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": true, "dependencies": [], "children": ["call_911_immediate", "call_911_immediate", "call_911_immediate", "call_911_immediate", "call_911_immediate", "call_911_immediate", "call_911_immediate", "call_911_immediate", "call_911_immediate", "mental_health_crisis", "call_911_immediate"], "is_terminal": false, "outcome": null}, "call_911_immediate": {"id": "call_911_immediate", "topic": "Call 911 Immediately", "question": "🚨 **CALL 911 IMMEDIATELY** - Based on your symptoms, you need emergency medical attention right away.", "ui_display": "🚨 **CALL 911 IMMEDIATELY**\n\nBased on your symptoms, you need emergency medical attention right away.\n\n**What to do NOW:**\n- Call 911 immediately\n- Do not drive yourself\n- If someone is with you, have them drive you to the ER\n- Take any medications you're currently taking with you\n- Bring your ID and insurance card if possible", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "emergency_care"}, "pain_injury_assessment": {"id": "pain_injury_assessment", "topic": "Pain and Injury Assessment", "question": "Let's assess your pain or injury. Where is the pain located?", "ui_display": "Let's assess your pain or injury. Where is the pain located?", "response_options": ["Head or neck", "Chest or ribs", "Back (upper or lower)", "Shoulder or arm", "Hand or wrist", "Abdomen or pelvis", "Hip or thigh", "Knee", "Ankle or foot", "Multiple locations", "All over body pain", "Joint pain and stiffness"], "should_reason": true, "reasoning_rules": "Assess pain severity, location, and mechanism of injury. Red flags include trauma, severe pain >8/10, neurological symptoms.", "additional_reasoning": "Consider if pain is acute vs chronic, associated symptoms, and functional impact", "required": true, "dependencies": [], "children": ["head_neck_pain", "chest_pain_assessment", "back_pain_detailed", "shoulder_arm_pain", "hand_wrist_pain", "abdominal_pain", "hip_thigh_pain", "knee_pain_assessment", "ankle_foot_pain", "multiple_pain_sites", "fibromyalgia_assessment", "joint_pain_assessment"], "is_terminal": false, "outcome": null}, "back_pain_detailed": {"id": "back_pain_detailed", "topic": "Back Pain Assessment", "question": "Tell me more about your back pain. How would you describe it?", "ui_display": "Tell me more about your back pain. How would you describe it?", "response_options": ["Sudden, severe pain that started after lifting/movement", "Gradual onset over days/weeks", "Sharp, shooting pain down my leg", "Constant aching pain", "Pain with numbness or tingling", "Pain that's worse in the morning", "Pain that's worse with sitting", "Pain after a fall or accident", "Pain with fever or bladder changes"], "should_reason": true, "reasoning_rules": "Red flags: trauma, neurological symptoms, bladder/bowel dysfunction, severe night pain, fever with back pain", "additional_reasoning": "Consider sciatica, herniated disc, muscle strain, or serious pathology", "required": true, "dependencies": [], "children": ["back_pain_red_flags", "back_pain_chronic", "sciatica_assessment", "back_pain_chronic", "neurological_back_pain", "back_pain_chronic", "back_pain_chronic", "trauma_back_pain", "back_pain_urgent"], "is_terminal": false, "outcome": null}, "chest_pain_assessment": {"id": "chest_pain_assessment", "topic": "Chest Pain Assessment", "question": "Chest pain needs careful evaluation. How would you describe your chest pain?", "ui_display": "🫀 **Chest Pain Assessment** - Chest pain needs careful evaluation. How would you describe your chest pain?", "response_options": ["Crushing or pressure-like pain", "Sharp, stabbing pain", "Burning sensation", "Pain that radiates to arm, jaw, or back", "Pain with shortness of breath", "Pain with sweating or nausea", "Pain that worsens with movement", "Pain that comes and goes"], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": true, "dependencies": [], "children": ["chest_pain_urgent", "chest_pain_urgent", "chest_pain_urgent", "chest_pain_urgent", "chest_pain_urgent", "chest_pain_urgent", "musculoskeletal_chest_pain", "chest_pain_urgent"], "is_terminal": false, "outcome": null}, "illness_assessment": {"id": "illness_assessment", "topic": "General Illness Assessment", "question": "I can help you understand your illness symptoms. What symptoms are you experiencing?", "ui_display": "🤒 **General Illness Assessment** - I can help you understand your illness symptoms. What symptoms are you experiencing?", "response_options": ["Fever and chills", "Nausea and vomiting", "Diarrhea", "Severe headache", "Fatigue and weakness", "Body aches and pains", "Sore throat", "Runny nose and congestion", "<PERSON><PERSON>", "Dizziness or lightheadedness", "Loss of appetite", "Multiple symptoms"], "should_reason": true, "reasoning_rules": "Assess for severity, duration, and potential dehydration. High fever, severe symptoms, or signs of serious illness require urgent care.", "additional_reasoning": "Consider patient age, immune status, and combination of symptoms", "required": true, "dependencies": [], "children": ["fever_assessment", "nausea_vomiting_assessment", "diarrhea_assessment", "headache_assessment", "fatigue_assessment", "body_aches_assessment", "sore_throat_assessment", "cold_congestion_assessment", "cough_assessment", "dizziness_assessment", "appetite_assessment", "multiple_symptoms_assessment"], "is_terminal": false, "outcome": null}, "cardiac_assessment": {"id": "cardiac_assessment", "topic": "Heart and Chest Symptoms", "question": "I want to make sure we address your heart/chest symptoms properly. Which describes your situation best?", "ui_display": "🫀 **Heart and Chest Symptoms** - I want to make sure we address your heart/chest symptoms properly. Which describes your situation best?", "response_options": ["Chest pain or pressure", "Heart racing or irregular heartbeat", "Shortness of breath with chest discomfort", "Dizziness with chest symptoms", "Chest pain with sweating or nausea", "Chest pain that radiates to arm/jaw", "Chest pain after physical activity", "Chronic heart condition follow-up"], "should_reason": true, "reasoning_rules": "Any chest pain with cardiac risk factors, radiation, associated symptoms (SOB, sweating, nausea) requires urgent evaluation", "additional_reasoning": "Consider age, gender, risk factors, and associated symptoms for cardiac vs non-cardiac chest pain", "required": true, "dependencies": [], "children": ["chest_pain_urgent", "arrhythmia_assessment", "chest_pain_urgent", "chest_pain_urgent", "chest_pain_urgent", "chest_pain_urgent", "exercise_chest_pain", "cardiac_follow_up"], "is_terminal": false, "outcome": null}, "mental_health_initial": {"id": "mental_health_initial", "topic": "Mental Health Assessment", "question": "I'm here to help with your mental health concerns. What best describes how you're feeling?", "ui_display": "🧠 **Mental Health Support** - I'm here to help with your mental health concerns. What best describes how you're feeling?", "response_options": ["Thoughts of hurting myself or others", "Severe anxiety or panic attacks", "Deep sadness or depression", "Unable to cope with daily activities", "Substance use concerns", "Trauma or PTSD symptoms", "Eating disorder concerns", "Sleep problems affecting mental health", "Relationship or family stress", "Work or school stress"], "should_reason": true, "reasoning_rules": "Immediate safety assessment is critical. Suicidal/homicidal ideation requires immediate intervention.", "additional_reasoning": "Screen for safety, severity, duration, and functional impairment", "required": true, "dependencies": [], "children": ["mental_health_crisis", "anxiety_panic_assessment", "depression_assessment", "functional_impairment_assessment", "substance_assessment", "trauma_assessment", "eating_disorder_assessment", "sleep_mental_health", "stress_assessment", "stress_assessment"], "is_terminal": false, "outcome": null}, "mental_health_crisis": {"id": "mental_health_crisis", "topic": "Mental Health Crisis", "question": "I'm very concerned about your safety. Are you having thoughts of hurting yourself or others right now?", "ui_display": "🆘 **Mental Health Crisis** - I'm very concerned about your safety. Are you having thoughts of hurting yourself or others right now?", "response_options": ["Yes, I'm thinking about hurting myself", "Yes, I'm thinking about hurting someone else", "I have a plan to hurt myself", "I took pills or tried to hurt myself", "I'm not sure if I'm safe", "No, but I'm having severe depression/anxiety"], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": true, "dependencies": [], "children": ["suicide_crisis_intervention", "homicide_crisis_intervention", "suicide_plan_emergency", "suicide_attempt_emergency", "safety_assessment", "mental_health_urgent"], "is_terminal": false, "outcome": null}, "suicide_crisis_intervention": {"id": "suicide_crisis_intervention", "topic": "Suicide Crisis Intervention", "question": "I'm very concerned about you. You need immediate help and support.", "ui_display": "🆘 **IMMEDIATE CRISIS INTERVENTION NEEDED**\n\n**Please reach out for help RIGHT NOW:**\n\n🔴 **Call 911** if you're in immediate danger\n\n📞 **National Suicide Prevention Lifeline: 988**\n\n💬 **Text HOME to 741741** (Crisis Text Line)\n\n**You are not alone. Help is available 24/7.**\n\n**If possible, please:**\n- Stay with someone you trust\n- Remove any means of self-harm\n- Go to your nearest emergency room\n\n**Your life matters and things can get better.**", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "crisis_intervention"}, "respiratory_initial": {"id": "respiratory_initial", "topic": "Breathing Assessment", "question": "Let's assess your breathing concerns. Which best describes your symptoms?", "ui_display": "🫁 **Breathing Assessment** - Let's assess your breathing concerns. Which best describes your symptoms?", "response_options": ["Sudden, severe difficulty breathing", "Gradual shortness of breath over days", "Persistent cough with fever", "Cough with blood", "Wheezing or chest tightness", "Chronic breathing problems worsening", "Breathing problems with chest pain", "COVID-19 related breathing concerns"], "should_reason": true, "reasoning_rules": "Severe SOB, hemoptysis, chest pain with SOB are red flags requiring urgent evaluation", "additional_reasoning": "Consider asthma, COPD exacerbation, pneumonia, PE, or cardiac causes", "required": true, "dependencies": [], "children": ["severe_breathing_emergency", "gradual_sob_assessment", "cough_fever_assessment", "hemoptysis_assessment", "asthma_assessment", "copd_exacerbation", "chest_pain_urgent", "covid_<PERSON>"], "is_terminal": false, "outcome": null}, "pediatric_initial": {"id": "pediatric_initial", "topic": "Child Health Assessment", "question": "I'll help assess your child's health concern. What's the child's age and main symptom?", "ui_display": "👶 **Child Health Assessment** - I'll help assess your child's health concern. What's the child's age and main symptom?", "response_options": ["Newborn (0-1 month) - any concern", "Infant (1-12 months) with fever", "<PERSON><PERSON> (1-3 years) with fever", "Child (3+ years) with fever", "Difficulty breathing or wheezing", "Persistent vomiting or diarrhea", "Rash or skin changes", "Injury or accident", "Behavioral or developmental concerns", "Other pediatric concern"], "should_reason": true, "reasoning_rules": "Newborn fever is always urgent. Age-specific fever thresholds and symptoms matter significantly in pediatrics", "additional_reasoning": "Consider age-specific normal values, developmental stage, and pediatric red flag symptoms", "required": true, "dependencies": [], "children": ["newborn_assessment", "infant_fever_assessment", "toddler_fever_assessment", "child_fever_assessment", "pediatric_respiratory", "pediatric_gi", "pediatric_rash", "pediatric_injury", "pediatric_development", "general_pediatric"], "is_terminal": false, "outcome": null}, "womens_health_initial": {"id": "womens_health_initial", "topic": "Women's Health", "question": "I'm here to help with your women's health concern. What brings you here today?", "ui_display": "💃 **Women's Health** - I'm here to help with your women's health concern. What brings you here today?", "response_options": ["Pregnancy-related concern", "Severe pelvic or abdominal pain", "Menstrual cycle concerns", "Vaginal discharge or infection symptoms", "Breast concerns or changes", "Menopause symptoms", "Birth control questions", "Fertility concerns", "Postpartum concerns", "Annual wellness or screening"], "should_reason": true, "reasoning_rules": "Pregnancy complications, severe pelvic pain, and breast changes require careful evaluation", "additional_reasoning": "Consider pregnancy status, age, and gynecological history for appropriate triage", "required": true, "dependencies": [], "children": ["pregnancy_assessment", "pelvic_pain_assessment", "menstrual_assessment", "vaginal_symptoms", "breast_assessment", "menopause_assessment", "contraception_assessment", "fertility_assessment", "postpartum_assessment", "womens_wellness"], "is_terminal": false, "outcome": null}, "mens_health_initial": {"id": "mens_health_initial", "topic": "Men's Health", "question": "I'm here to help with your men's health concern. What's your main concern today?", "ui_display": "💪 **Men's Health** - I'm here to help with your men's health concern. What's your main concern today?", "response_options": ["Testicular pain or changes", "Erectile dysfunction", "Urinary problems or changes", "Prostate concerns", "Sexual health concerns", "Low testosterone symptoms", "Hair loss or male pattern baldness", "Men's wellness checkup", "Sports or fitness injury", "Mental health (men-specific)"], "should_reason": true, "reasoning_rules": "Testicular pain, sudden urinary retention, and concerning urological symptoms need urgent assessment", "additional_reasoning": "Consider age-specific concerns and sensitive nature of men's health topics", "required": true, "dependencies": [], "children": ["testicular_assessment", "ed_assessment", "urinary_mens_assessment", "prostate_assessment", "mens_sexual_health", "testosterone_assessment", "hair_loss_assessment", "mens_wellness", "sports_injury_assessment", "mens_mental_health"], "is_terminal": false, "outcome": null}, "medication_assessment": {"id": "medication_assessment", "topic": "Medication Questions", "question": "I can help with medication-related questions. What's your concern?", "ui_display": "💊 **Medication Questions** - I can help with medication-related questions. What's your concern?", "response_options": ["Side effects from current medication", "Missed doses or dosing questions", "Drug interactions", "New medication questions", "Generic vs brand name questions", "Cost or insurance coverage", "Stopping or changing medications", "Over-the-counter medication questions", "Herbal or supplement interactions", "Prescription refill needs"], "should_reason": true, "reasoning_rules": "Serious side effects, drug interactions, and safety concerns require pharmacist or provider consultation", "additional_reasoning": "Consider medication complexity, patient understanding, and safety implications", "required": true, "dependencies": [], "children": ["medication_side_effects", "dosing_questions", "drug_interactions", "new_medication_education", "generic_brand_education", "medication_access", "medication_changes", "otc_guidance", "supplement_interactions", "refill_assistance"], "is_terminal": false, "outcome": null}, "preventive_care": {"id": "preventive_care", "topic": "Preventive Care", "question": "Great choice focusing on prevention! What type of preventive care are you interested in?", "ui_display": "🏥 **Preventive Care** - Great choice focusing on prevention! What type of preventive care are you interested in?", "response_options": ["Annual physical exam", "Cancer screening (mammogram, colonoscopy, etc.)", "Vaccination questions", "Health risk assessment", "Weight management", "Smoking cessation", "Diet and nutrition counseling", "Exercise and fitness guidance", "Stress management", "Sleep hygiene"], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": true, "dependencies": [], "children": ["annual_physical", "cancer_screening", "vaccination_assessment", "health_risk_assessment", "weight_management", "smoking_cessation", "nutrition_counseling", "fitness_guidance", "stress_management", "sleep_hygiene"], "is_terminal": false, "outcome": null}, "chest_pain_urgent": {"id": "chest_pain_urgent", "topic": "Urgent Chest Pain", "question": "Chest pain needs immediate evaluation. Please go to the emergency room now or call 911.", "ui_display": "🚨 **Urgent Chest Pain Evaluation Needed**\n\n**Go to the Emergency Room NOW or Call 911**\n\nChest pain can be serious and needs immediate medical evaluation.\n\n**What to do:**\n- Call 911 if symptoms are severe\n- Have someone drive you to the ER immediately\n- Do not drive yourself\n- Bring your medications and insurance card\n- If you have aspirin and no allergies, chew one 325mg aspirin", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "emergency_care"}, "primary_care_urgent": {"id": "primary_care_urgent", "topic": "Urgent Primary Care", "question": "You should see a healthcare provider within the next 24 hours.", "ui_display": "⚡ **Urgent Primary Care Needed**\n\nBased on your symptoms, you should see a healthcare provider within the next 24 hours.\n\n**Recommended next steps:**\n- Contact your primary care doctor\n- If unavailable, visit an urgent care center\n- If symptoms worsen, go to the emergency room\n\n**Monitor for worsening symptoms and seek immediate care if needed.**", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "urgent_care"}, "primary_care_routine": {"id": "primary_care_routine", "topic": "Routine Primary Care", "question": "You should schedule an appointment with your primary care provider within the next few days to a week.", "ui_display": "🏥 **Routine Primary Care**\n\nBased on your symptoms, you should schedule an appointment with your primary care provider within the next few days to a week.\n\n**Recommended next steps:**\n- Contact your primary care doctor's office\n- Schedule a routine appointment\n- Monitor symptoms and contact provider if they worsen\n\n**Self-care in the meantime:**\n- Rest and stay hydrated\n- Follow any relevant self-care measures\n- Keep track of your symptoms", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "routine_care"}, "sleep_initial": {"id": "sleep_initial", "topic": "Sleep Problems Assessment", "question": "Sleep issues can really affect your health. What sleep problems are you experiencing?", "ui_display": "😴 **Sleep Problems Assessment** - Sleep issues can really affect your health. What sleep problems are you experiencing?", "response_options": ["Di<PERSON><PERSON><PERSON>y falling asleep", "Waking up frequently during the night", "Waking up too early", "Snoring or sleep apnea symptoms", "Restless leg syndrome", "Nightmares or night terrors", "Sleepwalking", "Excessive daytime sleepiness", "Shift work sleep problems", "Insomnia lasting more than a month"], "should_reason": true, "reasoning_rules": "Assess duration, impact on daily function, and potential underlying conditions. Sleep apnea symptoms require evaluation.", "additional_reasoning": "Consider sleep hygiene, mental health factors, and medical conditions affecting sleep", "required": true, "dependencies": [], "children": ["insomnia_assessment", "sleep_maintenance_problems", "early_morning_awakening", "sleep_apnea_screening", "restless_legs_assessment", "parasomnias_assessment", "sleepwalking_assessment", "hypersomnia_assessment", "shift_work_disorder", "chronic_insomnia"], "is_terminal": false, "outcome": null}, "eye_vision_initial": {"id": "eye_vision_initial", "topic": "Eye and Vision Problems", "question": "Vision problems can be concerning. What eye or vision symptoms are you experiencing?", "ui_display": "👁️ **Eye and Vision Problems** - Vision problems can be concerning. What eye or vision symptoms are you experiencing?", "response_options": ["Sudden vision loss", "Flashing lights or floating spots", "Eye pain with vision changes", "Double vision", "Gradual vision changes", "Red, itchy, or watery eyes", "Eye injury or foreign object", "Dry eyes", "Light sensitivity", "Difficulty reading or seeing close up"], "should_reason": true, "reasoning_rules": "Sudden vision loss, severe eye pain, or trauma require immediate attention. Gradual changes may indicate need for eye exam.", "additional_reasoning": "Consider emergency vs routine eye care needs", "required": true, "dependencies": [], "children": ["vision_loss_emergency", "retinal_symptoms", "eye_pain_vision_change", "double_vision_assessment", "gradual_vision_change", "eye_irritation_assessment", "eye_injury_assessment", "dry_eyes_assessment", "photophobia_assessment", "presbyopia_assessment"], "is_terminal": false, "outcome": null}, "fever_assessment": {"id": "fever_assessment", "topic": "Fever Evaluation", "question": "Let me help assess your fever. How high is your temperature?", "ui_display": "🌡️ **Fever Evaluation** - Let me help assess your fever. How high is your temperature?", "response_options": ["100-101°F (37.8-38.3°C)", "101-102°F (38.3-38.9°C)", "102-103°F (38.9-39.4°C)", "Over 103°F (39.4°C)", "I haven't taken my temperature", "Fever with severe headache or neck stiffness", "Fever with difficulty breathing", "Fever lasting more than 3 days"], "should_reason": true, "reasoning_rules": "High fever >103°F, fever with severe symptoms, or prolonged fever requires medical evaluation", "additional_reasoning": "Consider age, immune status, and associated symptoms", "required": true, "dependencies": [], "children": ["low_grade_fever", "moderate_fever", "high_fever", "very_high_fever", "fever_unknown_temp", "fever_with_neurological", "fever_with_respiratory", "prolonged_fever"], "is_terminal": false, "outcome": null}, "joint_pain_assessment": {"id": "joint_pain_assessment", "topic": "Joint Pain Assessment", "question": "Joint pain can have many causes. Can you describe your joint symptoms?", "ui_display": "🦴 **Joint Pain Assessment** - Joint pain can have many causes. Can you describe your joint symptoms?", "response_options": ["One swollen, red, painful joint", "Multiple joints affected", "Morning stiffness lasting over an hour", "Joint pain with skin rash", "Joint pain after tick bite", "Joint pain with fever", "Chronic joint pain and stiffness", "Joint pain after injury"], "should_reason": true, "reasoning_rules": "Single hot, swollen joint may indicate septic arthritis. Multiple joints with systemic symptoms may suggest autoimmune condition.", "additional_reasoning": "Consider infectious, inflammatory, degenerative, or trauma-related causes", "required": true, "dependencies": [], "children": ["septic_arthritis_concern", "polyarthritis_assessment", "rheumatoid_arthritis_screening", "systemic_arthritis", "lyme_arthritis", "infectious_arthritis", "osteoarthritis_assessment", "traumatic_joint_injury"], "is_terminal": false, "outcome": null}, "low_grade_fever": {"id": "low_grade_fever", "topic": "Low Grade Fever Management", "question": "A low-grade fever is often your body's way of fighting infection. How are you feeling otherwise?", "ui_display": "🌡️ **Low Grade Fever (100-101°F)** - A low-grade fever is often your body's way of fighting infection. How are you feeling otherwise?", "response_options": ["Feeling mostly okay, just tired", "Mild cold symptoms", "Body aches and chills", "Sore throat", "<PERSON><PERSON><PERSON> upset", "Getting worse over time"], "should_reason": true, "reasoning_rules": "Low-grade fever with mild symptoms often manageable at home with monitoring", "additional_reasoning": "Watch for worsening symptoms or development of high fever", "required": true, "dependencies": [], "children": ["home_fever_management", "viral_syndrome_likely", "flu_like_symptoms", "throat_infection_possible", "gi_infection_possible", "fever_monitoring_needed"], "is_terminal": false, "outcome": null}, "home_fever_management": {"id": "home_fever_management", "topic": "Home Fever Management", "question": "You can likely manage this at home with self-care.", "ui_display": "🏠 **Home Fever Management**\n\n**Self-Care Recommendations:**\n- Rest and stay hydrated\n- Take acetaminophen or ibuprofen as directed\n- Use cool compresses if comfortable\n- Monitor temperature regularly\n\n**Watch for worsening:**\n- Fever over 101°F\n- Severe headache or neck stiffness\n- Difficulty breathing\n- Persistent vomiting\n\n**Call your doctor if symptoms worsen or persist more than 3 days.**", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "self_care"}, "very_high_fever": {"id": "very_high_fever", "topic": "Very High Fever - Urgent Care", "question": "A fever over 103°F needs immediate medical attention.", "ui_display": "🚨 **Very High Fever (>103°F) - URGENT CARE NEEDED**\n\n**Please seek immediate medical care:**\n- Go to urgent care or emergency room\n- Call your doctor immediately\n- Do not wait - high fever can be dangerous\n\n**While getting care:**\n- Take fever reducer if available\n- Stay hydrated\n- Use cool compresses\n- Have someone drive you - don't drive yourself\n\n**This level of fever requires professional medical evaluation.**", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "urgent_care"}, "routine_primary_care": {"id": "routine_primary_care", "topic": "Routine Primary Care", "question": "This sounds like something that would benefit from routine medical care.", "ui_display": "🏥 **Routine Primary Care Recommended**\n\n**Next Steps:**\n- Schedule an appointment with your primary care doctor\n- Typical wait time: 1-2 weeks is appropriate\n- Continue monitoring your symptoms\n\n**Call sooner if symptoms:**\n- Get significantly worse\n- Become severe or concerning\n- Are interfering with daily activities\n\n**In the meantime, continue any self-care measures that help.**", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "routine_care"}, "telehealth_appropriate": {"id": "telehealth_appropriate", "topic": "Telehealth Consultation", "question": "Your symptoms could be well-addressed through a virtual consultation.", "ui_display": "💻 **Telehealth Consultation Recommended**\n\n**Benefits of Virtual Care:**\n- Convenient access from home\n- Often same-day or next-day availability\n- Cost-effective option\n- Reduces exposure to other illnesses\n\n**Good for:**\n- Follow-up on existing conditions\n- Medication adjustments\n- Mild to moderate symptoms\n- Mental health consultations\n\n**Contact your healthcare provider to schedule a virtual visit.**", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "telehealth"}, "specialist_referral": {"id": "specialist_referral", "topic": "Specialist <PERSON><PERSON><PERSON> Needed", "question": "Your symptoms suggest you would benefit from seeing a medical specialist.", "ui_display": "👨‍⚕️ **Specialist Referral Recommended**\n\n**Next Steps:**\n- Discuss with your primary care doctor first\n- They can provide appropriate referral\n- May need insurance authorization\n\n**Types of specialists that might help:**\n- Based on your specific symptoms and condition\n- Your doctor will determine the best fit\n\n**Timeline:**\n- Primary care visit: 1-2 weeks\n- Specialist appointment: 2-8 weeks typically\n\n**Monitor symptoms and contact your doctor if they worsen.**", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "specialist_care"}, "other_health_concern": {"id": "other_health_concern", "topic": "Other Health Concern", "question": "Please describe your health concern, and I'll help guide you to the right care.", "ui_display": "🔍 **Other Health Concern** - Please describe your health concern, and I'll help guide you to the right care.", "response_options": ["It's an emergency", "I need urgent care today", "I can wait a few days", "It's for prevention/wellness", "I'm not sure how urgent it is"], "should_reason": true, "reasoning_rules": "Use clinical reasoning to assess urgency based on patient description", "additional_reasoning": "Listen for red flag symptoms and assess severity", "required": true, "dependencies": [], "children": ["emergency_assessment", "urgent_primary_care", "routine_primary_care", "preventive_care", "triage_assessment"], "is_terminal": false, "outcome": null}, "urgent_primary_care": {"id": "urgent_primary_care", "topic": "Urgent Primary Care", "question": "You should see a healthcare provider within the next 24 hours.", "ui_display": "⚡ **Urgent Primary Care Needed**\n\nBased on your symptoms, you should see a healthcare provider within the next 24 hours.\n\n**Recommended next steps:**\n- Contact your primary care doctor\n- If unavailable, visit an urgent care center\n- If symptoms worsen, go to the emergency room\n\n**Monitor for worsening symptoms and seek immediate care if needed.**", "response_options": [], "should_reason": false, "reasoning_rules": "", "additional_reasoning": "", "required": false, "dependencies": [], "children": [], "is_terminal": true, "outcome": "urgent_care"}}}