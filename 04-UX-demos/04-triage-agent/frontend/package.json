{"name": "intelligent-triage-agent", "version": "2.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "axios": "^1.6.2", "d3": "^7.9.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-d3-tree": "^3.6.6", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.6.1", "react-tree-graph": "^8.0.3", "web-vitals": "^3.5.0"}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.45", "tailwindcss": "^3.3.6"}, "overrides": {"prismjs": "^1.30.0", "refractor": "^4.7.0", "nth-check": "^2.1.1", "webpack-dev-server": "^4.15.1", "svgo": "^3.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}