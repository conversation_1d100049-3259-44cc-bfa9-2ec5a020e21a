/* Left Sidebar Styles - Modern & Clean Design */
.left-sidebar {
  width: 100%;
  height: 100%;
  background: var(--background-primary);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.left-sidebar.collapsed {
  width: 50px;
  background: var(--background-secondary);
}

/* Header */
.sidebar-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid var(--border-light);
  background: var(--background-primary);
  flex-shrink: 0;
}

.sidebar-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Content */
.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px 0;
}

/* Custom Scrollbar */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* Config Section */
.config-section {
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 4px;
}

.config-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* Section Header */
.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 20px;
  margin-bottom: 8px;
}

.section-icon {
  font-size: 14px;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 11px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-toggle {
  font-size: 10px;
  color: var(--text-muted);
  transition: var(--transition);
}

.section-header[aria-expanded="false"] .section-toggle {
  transform: rotate(-90deg);
}

/* Section Content */
.section-content {
  padding: 8px 20px;
  background: var(--background-primary);
}

/* Model Selection - Compact Dropdown */
.model-selector {
  padding: 0 20px 12px;
}

.input-label {
  display: block;
  font-size: 11px;
  font-weight: 500;
  color: var(--text-muted);
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.model-dropdown {
  width: 100%;
  background: var(--background-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: 8px 12px;
  font-size: 13px;
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
}

.model-dropdown:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.model-dropdown:hover {
  border-color: var(--border-hover);
}

/* Current Model Info */
.model-info {
  padding: 0 20px 12px;
}

.model-details {
  background: var(--background-tertiary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: 10px 12px;
}

.model-name {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.model-description {
  font-size: 10px;
  color: var(--text-muted);
  line-height: 1.4;
}

/* Server List - Simplified */
.servers-list {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.server-item {
  display: flex;
  align-items: center;
  background: var(--background-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: 12px;
  transition: var(--transition);
  width: 100%;
  gap: 12px;
}

.server-item:hover {
  background: var(--background-tertiary);
  border-color: var(--border-hover);
}

.server-info {
  flex: 1;
  min-width: 0;
}

.server-header {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.server-name {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
}

.server-status {
  font-size: 10px;
  font-weight: 600;
}

.server-status.enabled {
  color: #10b981;
}

.server-status.disabled {
  color: var(--text-muted);
}

.server-description {
  font-size: 10px;
  color: var(--text-muted);
  line-height: 1.3;
}

/* Toggle Switch - Minimalist */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
  margin-left: 12px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: var(--transition);
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: var(--transition);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-switch input:checked + .toggle-slider {
  background-color: var(--primary-blue);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(16px);
}

.toggle-switch:hover .toggle-slider {
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 20px;
  color: var(--text-muted);
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.empty-title {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 4px;
}

.empty-description {
  font-size: 11px;
  line-height: 1.4;
}

/* Collapsed State */
.left-sidebar.collapsed .sidebar-header,
.left-sidebar.collapsed .section-content {
  display: none;
}

.left-sidebar.collapsed .section-header {
  padding: 12px 0;
  justify-content: center;
}

.left-sidebar.collapsed .section-toggle {
  display: none;
}

/* Close Button */
.sidebar-close-btn {
  background: transparent;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: var(--transition);
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 12px;
}

.sidebar-close-btn:hover {
  background: var(--background-tertiary);
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .left-sidebar {
    width: 260px;
  }

  .sidebar-header,
  .section-header,
  .model-selector,
  .model-info,
  .servers-list {
    padding-left: 16px;
    padding-right: 16px;
  }

  .server-item {
    padding: 10px;
  }

  .model-name,
  .server-name {
    font-size: 11px;
  }

  .model-description,
  .server-description {
    font-size: 9px;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-content {
  animation: fadeIn 0.2s ease;
}

/* Focus States */
.server-item:focus,
.toggle-switch:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .server-item {
    border-width: 2px;
  }
  
  .toggle-switch {
    border: 2px solid var(--border-color);
  }
}

/* AI Triage Section */
.triage-status {
  padding: 0px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-label {
  font-size: 12px;
  color: #374151;
  font-weight: 600;
}

.status-indicator {
  font-size: 10px;
}

.status-indicator.online {
  color: #10b981;
}

.status-indicator.offline {
  color: #ef4444;
}

.status-value {
  font-size: 12px;
  font-weight: 700;
  color: #1f2937;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

/* Current Session Styling */
.status-item.current-session {
  background: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.25);
  border-radius: 6px;
  padding: 8px;
  margin-top: 8px;
}

.status-value.current-node {
  color: #3b82f6;
  font-weight: 700;
  background: rgba(59, 130, 246, 0.1);
}

.node-indicator {
  font-size: 10px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Triage Actions */
.triage-actions {
  padding: 0;
  margin-top: 8px; /* Add some space above the button */
}

.action-btn {
  width: 100%;
  background: #ffffff; /* White background */
  border: 1px solid #e5e7eb; /* Light gray border */
  border-radius: var(--radius-md);
  padding: 10px 12px;
  font-size: 13px;
  font-weight: 500;
  color: #374151; /* Dark gray text */
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.action-btn:hover {
  background: #f9fafb; /* Slightly off-white on hover */
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.action-btn.secondary {
  color: var(--text-secondary);
}

.action-btn.secondary:hover {
  color: var(--text-primary);
}

.triage-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tree-view-container {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: 0px;
  background: var(--background-secondary);
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

.triage-loading,
.triage-offline {
  font-size: 13px;
  color: #9ca3af; /* Muted gray text */
  text-align: center;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: var(--radius-md);
}

/* Sidebar Disclaimer */
.sidebar-disclaimer {
  background: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: 6px;
  color: #991b1b;
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  padding: 8px 12px;
  margin: 12px 16px 16px 16px;
  opacity: 0.9;
  line-height: 1.2;
} 