@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .toggle-switch {
    @apply relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .toggle-switch.enabled {
    @apply bg-primary-600;
  }
  
  .toggle-switch.disabled {
    @apply bg-gray-200;
  }
  
  .toggle-thumb {
    @apply inline-block h-4 w-4 transform rounded-full bg-white transition-transform;
  }
  
  .toggle-thumb.enabled {
    @apply translate-x-6;
  }
  
  .toggle-thumb.disabled {
    @apply translate-x-1;
  }
} 