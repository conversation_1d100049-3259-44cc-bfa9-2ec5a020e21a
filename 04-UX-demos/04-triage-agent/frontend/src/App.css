/* Modern SaaS Application Styles - AI Triage Agent */
:root {
  /* Colors */
  --primary-blue: #2563eb;
  --primary-blue-dark: #1d4ed8;
  --primary-blue-light: #3b82f6;
  --secondary-gray: #64748b;
  
  /* Backgrounds */
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  
  /* Text */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  
  /* Borders */
  --border-color: #9ca3af;
  --border-light: #e2e8f0;
  --border-hover: #6b7280;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  
  /* Transitions */
  --transition: all 0.2s ease;
}

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: #121212;
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  overflow: hidden; /* Prevent body scrollbars */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--background-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App Container */
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', sans-serif;
  background: var(--background-primary);
  color: var(--text-primary);
}

/* Simplified Header */
.app-header {
  background: var(--background-primary);
  border-bottom: 1px solid var(--border-light);
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Simplified App Logo */
.app-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  user-select: none;
}

.aws-text-logo {
  font-size: 18px;
  font-weight: 700;
  color: #232f3e;
  background: linear-gradient(45deg, #ff9900, #232f3e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 1px;
}

.aws-logo {
  height: 24px;
  width: auto;
}

.app-title {
  display: flex;
  flex-direction: column;
}

.title-main {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.title-sub {
  font-size: 9px;
  font-weight: 500;
  color: var(--text-muted);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Sidebar Toggle Buttons */
.sidebar-toggle {
  background: var(--background-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: 6px 8px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  font-size: 12px;
  font-weight: 500;
}

.sidebar-toggle:hover {
  background: var(--background-tertiary);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

/* Main Content Area */
.app-main {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

/* Sidebar Containers */
.sidebar-container {
  display: flex;
  position: relative;
  background: var(--background-primary);
  border-right: 1px solid var(--border-light);
  overflow: hidden;
}

.sidebar-container.right {
  border-right: none;
  border-left: 1px solid var(--border-light);
}

.sidebar-container.closed {
  width: 0 !important;
  border: none;
}

/* Resize Handle */
.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background: transparent;
  cursor: col-resize;
  z-index: 10;
  transition: var(--transition);
}

.resize-handle.left {
  right: 0;
}

.resize-handle.right {
  left: 0;
}

.resize-handle:hover {
  background: var(--primary-blue);
  opacity: 0.5;
}

.resize-handle:active {
  background: var(--primary-blue);
  opacity: 0.8;
}

/* Chat Container - Simplified */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--background-primary);
  overflow: hidden;
  width: 100%;
  min-width: 0;
}

/* Simplified Chat Header */
.chat-header-info {
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-light);
  background: var(--background-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.chat-header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chat-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-icon {
  font-size: 16px;
}

.chat-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.chat-subtitle {
  font-size: 11px;
  color: var(--text-muted);
}

/* Token Counter */
.token-counter {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.token-count {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
}

.token-label {
  font-size: 9px;
  font-weight: 500;
  color: var(--text-muted);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Expand Sidebar Buttons */
.expand-sidebar-btn {
  background: var(--background-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: 6px 8px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
}

.expand-sidebar-btn:hover {
  background: var(--background-tertiary);
  border-color: var(--border-hover);
  color: var(--text-primary);
  transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .app-header {
    padding: 10px 16px;
    height: 52px;
  }

  .header-center {
    display: none;
  }

  .app-logo .title-sub {
    display: none;
  }

  .title-main {
    font-size: 13px;
  }

  .aws-logo {
    height: 20px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 8px 12px;
    height: 48px;
  }

  .app-logo {
    gap: 8px;
  }

  .chat-container {
    margin: 0;
  }
}

/* Loading and Animation States */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
*:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

button:focus {
  outline-offset: 0;
}

/* Loading Spinner */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  transition: var(--transition);
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-blue);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-blue-dark);
}

.btn-secondary {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--background-tertiary);
  border-color: var(--border-hover);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--background-secondary);
  color: var(--text-primary);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

/* Form Components */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-control {
  padding: 8px 12px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 14px;
  transition: var(--transition);
  background: var(--background-primary);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.form-control::placeholder {
  color: var(--text-muted);
}

/* Utility Classes */
.text-xs { font-size: 10px; }
.text-sm { font-size: 12px; }
.text-base { font-size: 14px; }
.text-lg { font-size: 16px; }
.text-xl { font-size: 18px; }
.text-2xl { font-size: 24px; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

/* Sidebar containers */
.sidebar-left {
  width: 280px;
  flex-shrink: 0;
}

.sidebar-right {
  width: 320px;
  flex-shrink: 0;
}

.app-container {
  display: flex;
  height: 100vh; /* Use vh for full viewport height */
  width: 100vw; /* Use vw for full viewport width */
  overflow: hidden; /* Prevent scrollbars on the main container */
}

.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden; /* Ensure no overflow here */
}