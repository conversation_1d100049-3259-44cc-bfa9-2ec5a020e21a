/* markdown styles */
.markdown-container {
  max-width: 100%;
  overflow-wrap: break-word;
}

/* Headings - Chat Optimized */
.markdown-heading1 {
  font-size: 1.4em;
  margin-top: 12px;
  margin-bottom: 12px;
  font-weight: 600;
  line-height: 1.2;
  color: #1a1a1a;
}

.markdown-heading2 {
  font-size: 1.25em;
  margin-top: 10px;
  margin-bottom: 10px;
  font-weight: 600;
  line-height: 1.2;
  color: #1a1a1a;
}

.markdown-heading3 {
  font-size: 1.15em;
  margin-top: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.2;
}

.markdown-heading4 {
  font-size: 1.05em;
  margin-top: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.2;
}

.markdown-heading5 {
  font-size: 1em;
  margin-top: 8px;
  margin-bottom: 6px;
  font-weight: 600;
  line-height: 1.2;
}

.markdown-heading6 {
  font-size: 0.95em;
  margin-top: 8px;
  margin-bottom: 6px;
  font-weight: 600;
  line-height: 1.2;
  color: #4a4a4a;
}

/* Paragraphs */
.markdown-paragraph {
  margin-top: 0;
  margin-bottom: 14px;
  line-height: 1.5;
}

/* Links */
.markdown-link {
  color: #0366d6;
  text-decoration: none;
}

.markdown-link:hover {
  text-decoration: underline;
}

/* Lists */
.markdown-list {
  padding-left: 1.5em;
  margin-top: 0;
  margin-bottom: 14px;
}

.markdown-list-item {
  margin-bottom: 3px;
  display: list-item;
}

.markdown-unordered-list {
  list-style-type: disc;
}

.markdown-ordered-list {
  list-style-type: decimal;
}

/* Code blocks and inline code */
.markdown-code-block {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 12px;
  overflow-x: auto;
  margin-top: 0;
  margin-bottom: 14px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 85%;
  line-height: 1.45;
}

.markdown-inline-code {
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 85%;
  padding: 0.2em 0.4em;
  margin: 0;
}

/* Blockquotes */
.markdown-blockquote {
  margin: 14px 0;
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-blockquote > :first-child {
  margin-top: 0;
}

.markdown-blockquote > :last-child {
  margin-bottom: 0;
}

/* Tables */
.markdown-table {
  width: 100%;
  margin-top: 0;
  margin-bottom: 14px;
  border-spacing: 0;
  border-collapse: collapse;
  background-color: rgba(255, 255, 255, 0.2);
}

.markdown-table th {
  font-weight: 600;
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
  background-color: rgba(246, 248, 250, 0.5);
}

.markdown-table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-table tr {
  border-top: 1px solid #c6cbd1;
}

.markdown-table tr:nth-child(2n) {
  background-color: rgba(246, 248, 250, 0.4);
}

/* Horizontal rule */
.markdown-hr {
  height: 1px;
  padding: 0;
  margin: 16px 0;
  background-color: #e1e4e8;
  border: 0;
}

/* Images */
.markdown-image {
  max-width: 100%;
  box-sizing: content-box;
  background-color: #fff;
}

/* Bold and italic */
.markdown-bold {
  font-weight: 600;
}

.markdown-italic {
  font-style: italic;
}

/* Strike */
.markdown-strike {
  text-decoration: line-through;
}

/* Sub and sup */
.markdown-sup {
  vertical-align: super;
  font-size: smaller;
}

.markdown-sub {
  vertical-align: sub;
  font-size: smaller;
}