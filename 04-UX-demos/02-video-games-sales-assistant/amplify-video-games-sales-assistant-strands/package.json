{"name": "amplify-video-games-sales-assistant-strands", "version": "0.1.1", "author": "<PERSON><PERSON> (<EMAIL>)", "private": true, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.785.0", "@aws-sdk/client-dynamodb": "^3.777.0", "@aws-sdk/credential-providers": "^3.778.0", "@aws-sdk/lib-dynamodb": "^3.787.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-scripts": "5.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}