# UI/UX and Demos to showcase

This folder contains UI/UX elements and demos with both Frontend and backend designed to work seamlessly with Strands agents. Each sample here demonstrates how user interfaces can be integrated, deployed, and used effectively with <PERSON><PERSON>, making it easy to build and showcase interactive agent-driven experiences.

| Example ID  | Link                                                                  | Features showcased                                                      |
|-------------|-----------------------------------------------------------------------|-------------------------------------------------------------------------|
| 1           | [Streamlit GenAI Template](./01-streamlit-template/)                | A ready-to-deploy template for testing and sharing GenAI agents with Streamlit interface. Built entirely in Python, it offers a quick starting point for GenAI web applications, supporting both local development and AWS deployment with user authentication. The sample provides a complete AWS infrastructure using CDK including Cognito authentication, ECS/Fargate deployment, and CloudFront distribution. The template demonstrates the architecture with a sample appointment management agent, which can be easily replaced with any custom logic.|
| 2           | [Data Analyst Assistant for Video Game Sales](./02-video-games-sales-assistant/)                  | This reference solution deploys a conversational data analyst assistant that transforms natural language questions about video game sales into SQL queries for PostgreSQL databases. Built with the Strands Agents SDK and featuring an integrated, ready-to-use Data Analyst Assistant application, it delivers AI-generated insights, tabular data, and automatic visualizations. This solution enables intuitive data exploration without requiring SQL knowledge.|
| 3          | [HVAC data analytics agent](./03-hvac-data-analytics-agent/)                  | A lightweight conversational AI agent built with Strands SDK that assists facility managers and building operators with HVAC (Heating, Ventilation, and Air Conditioning) data analytics in physical smart buildings. The agent queries relevant data sources and dynamically generates code to process building sensor data before answering user questions.. |
| 4          | [AI Triage Agent with MCP](./04-triage-agent/)                  | An AI-powered decision support system that demonstrates intelligent medical triage capabilities with structured assessment flows. This sample showcases AI-powered decision tree navigation for guiding users through structured assessments. Built with modular MCP (Model Context Protocol) server architecture, it integrates multiple productivity tools including task management, calendar events, weather services. The sample provides complete AWS infrastructure using CloudFormation including EC2 deployment, S3 frontend hosting, and CloudFront distribution. Key features include multi-modal chat interface with streaming responses, session-based conversation state management, and dynamic tool integration.|
| 5          | [Strands Playground](./05-strands-playground/)                  | A web-based interactive playground for experimenting with the Strands SDK, allowing users to quickly test and prototype AI agents with configurable system prompts, model parameters, and tool selection. Features comprehensive tool selection with 25+ Strands tools, real-time performance metrics including latency and token usage, persistent session management through DynamoDB or file storage, and customizable model parameters for Amazon Bedrock. The playground provides complete AWS infrastructure using CDK including VPC, ECS/Fargate deployment, DynamoDB, and Application Load Balancer.|

