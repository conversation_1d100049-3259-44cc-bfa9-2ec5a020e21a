{"name": "smart-building-analytics-agent", "lockfileVersion": 3, "requires": true, "packages": {"": {"devDependencies": {"cdk-nag": "^2.35.67"}}, "node_modules/@aws-cdk/asset-awscli-v1": {"version": "2.2.231", "resolved": "https://registry.npmjs.org/@aws-cdk/asset-awscli-v1/-/asset-awscli-v1-2.2.231.tgz", "integrity": "sha512-vPqD/K2pK/ALhU5r5Nafdc2nLB+LJKxNyxUmQnLsazU6AWDJfkqjHQx8m3J4Cjl2C3chQkIRMdzSDuXIlo43GA==", "dev": true, "license": "Apache-2.0", "peer": true}, "node_modules/@aws-cdk/asset-node-proxy-agent-v6": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@aws-cdk/asset-node-proxy-agent-v6/-/asset-node-proxy-agent-v6-2.1.0.tgz", "integrity": "sha512-7bY3J8GCVxLupn/kNmpPc5VJz8grx+4RKfnnJiO1LG+uxkZfANZG3RMHhE+qQxxwkyQ9/MfPtTpf748UhR425A==", "dev": true, "license": "Apache-2.0", "peer": true}, "node_modules/@aws-cdk/cloud-assembly-schema": {"version": "41.2.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-41.2.0.tgz", "integrity": "sha512-JaulVS6z9y5+u4jNmoWbHZRs9uGOnmn/ktXygNWKNu1k6lF3ad4so3s18eRu15XCbUIomxN9WPYT6Ehh7hzONw==", "bundleDependencies": ["jsonschema", "semver"], "dev": true, "license": "Apache-2.0", "peer": true, "dependencies": {"jsonschema": "~1.4.1", "semver": "^7.7.1"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": "*"}}, "node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.7.1", "dev": true, "inBundle": true, "license": "ISC", "peer": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/aws-cdk-lib": {"version": "2.189.0", "resolved": "https://registry.npmjs.org/aws-cdk-lib/-/aws-cdk-lib-2.189.0.tgz", "integrity": "sha512-B5Uha7uRntOAyuKfU0eFtxij3ZVTzGAbetw5qaXlURa68wsWpKlU72/OyKugB6JYkhjCZkSTVVBxd1pVTosxEw==", "bundleDependencies": ["@balena/dockerignore", "case", "fs-extra", "ignore", "jsonschema", "minimatch", "punycode", "semver", "table", "yaml", "mime-types"], "dev": true, "license": "Apache-2.0", "peer": true, "dependencies": {"@aws-cdk/asset-awscli-v1": "^2.2.229", "@aws-cdk/asset-node-proxy-agent-v6": "^2.1.0", "@aws-cdk/cloud-assembly-schema": "^41.0.0", "@balena/dockerignore": "^1.0.2", "case": "1.6.3", "fs-extra": "^11.3.0", "ignore": "^5.3.2", "jsonschema": "^1.5.0", "mime-types": "^2.1.35", "minimatch": "^3.1.2", "punycode": "^2.3.1", "semver": "^7.7.1", "table": "^6.9.0", "yaml": "1.10.2"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"constructs": "^10.0.0"}}, "node_modules/aws-cdk-lib/node_modules/@balena/dockerignore": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "Apache-2.0", "peer": true}, "node_modules/aws-cdk-lib/node_modules/ajv": {"version": "8.17.1", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/aws-cdk-lib/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/aws-cdk-lib/node_modules/astral-regex": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/balanced-match": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT", "peer": true}, "node_modules/aws-cdk-lib/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/aws-cdk-lib/node_modules/case": {"version": "1.6.3", "dev": true, "inBundle": true, "license": "(MIT OR GPL-3.0-or-later)", "peer": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/aws-cdk-lib/node_modules/color-convert": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/aws-cdk-lib/node_modules/color-name": {"version": "1.1.4", "dev": true, "inBundle": true, "license": "MIT", "peer": true}, "node_modules/aws-cdk-lib/node_modules/concat-map": {"version": "0.0.1", "dev": true, "inBundle": true, "license": "MIT", "peer": true}, "node_modules/aws-cdk-lib/node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true}, "node_modules/aws-cdk-lib/node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "inBundle": true, "license": "MIT", "peer": true}, "node_modules/aws-cdk-lib/node_modules/fast-uri": {"version": "3.0.6", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/aws-cdk-lib/node_modules/fs-extra": {"version": "11.3.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/aws-cdk-lib/node_modules/graceful-fs": {"version": "4.2.11", "dev": true, "inBundle": true, "license": "ISC", "peer": true}, "node_modules/aws-cdk-lib/node_modules/ignore": {"version": "5.3.2", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": ">= 4"}}, "node_modules/aws-cdk-lib/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true}, "node_modules/aws-cdk-lib/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/aws-cdk-lib/node_modules/jsonschema": {"version": "1.5.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": "*"}}, "node_modules/aws-cdk-lib/node_modules/lodash.truncate": {"version": "4.4.2", "dev": true, "inBundle": true, "license": "MIT", "peer": true}, "node_modules/aws-cdk-lib/node_modules/mime-db": {"version": "1.52.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": ">= 0.6"}}, "node_modules/aws-cdk-lib/node_modules/mime-types": {"version": "2.1.35", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/aws-cdk-lib/node_modules/minimatch": {"version": "3.1.2", "dev": true, "inBundle": true, "license": "ISC", "peer": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/aws-cdk-lib/node_modules/punycode": {"version": "2.3.1", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": ">=6"}}, "node_modules/aws-cdk-lib/node_modules/require-from-string": {"version": "2.0.2", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/aws-cdk-lib/node_modules/semver": {"version": "7.7.1", "dev": true, "inBundle": true, "license": "ISC", "peer": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/aws-cdk-lib/node_modules/slice-ansi": {"version": "4.0.0", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/aws-cdk-lib/node_modules/string-width": {"version": "4.2.3", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/table": {"version": "6.9.0", "dev": true, "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true, "dependencies": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/aws-cdk-lib/node_modules/universalify": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "MIT", "peer": true, "engines": {"node": ">= 10.0.0"}}, "node_modules/aws-cdk-lib/node_modules/yaml": {"version": "1.10.2", "dev": true, "inBundle": true, "license": "ISC", "peer": true, "engines": {"node": ">= 6"}}, "node_modules/cdk-nag": {"version": "2.35.67", "resolved": "https://registry.npmjs.org/cdk-nag/-/cdk-nag-2.35.67.tgz", "integrity": "sha512-5KnQNNNdTm82bK4kg3hOKfzqA96r2C+19urPO7LmwWnsAGL7GbM8DQVWmZfrznKbmMZ6M/MV5GLOPt5FU3VrbA==", "dev": true, "license": "Apache-2.0", "peerDependencies": {"aws-cdk-lib": "^2.156.0", "constructs": "^10.0.5"}}, "node_modules/constructs": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/constructs/-/constructs-10.4.2.tgz", "integrity": "sha512-wsNxBlAott2qg8Zv87q3eYZYgheb9lchtBfjHzzLHtXbttwSrHPs1NNQbBrmbb1YZvYg2+Vh0Dor76w4mFxJkA==", "dev": true, "license": "Apache-2.0", "peer": true}}}