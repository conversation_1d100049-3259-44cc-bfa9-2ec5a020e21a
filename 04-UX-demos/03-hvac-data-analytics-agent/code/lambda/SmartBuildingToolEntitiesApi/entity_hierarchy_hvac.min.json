{"id": {"entityType": "ASSET", "id": "b5c24680-50f1-11ef-b4ce-d5aee9e495ad"}, "name": "Office Building HVAC", "type": "Building", "children": [{"entity": {"id": {"entityType": "ASSET", "id": "b5c24681-50f1-11ef-b4ce-d5aee9e495ad"}, "name": "Ground Floor", "type": "Floor", "children": [{"entity": {"id": {"entityType": "ASSET", "id": "gf-zone-1"}, "name": "GF-Zone-1", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "gf-ts-1"}, "name": "GF-TS-1", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "gf-vav-1"}, "name": "GF-VAV-1", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "gf-zone-2"}, "name": "GF-Zone-2", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "gf-ts-2"}, "name": "GF-TS-2", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "gf-vav-2"}, "name": "GF-VAV-2", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "gf-zone-3"}, "name": "GF-Zone-3", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "gf-ts-3"}, "name": "GF-TS-3", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "gf-vav-3"}, "name": "GF-VAV-3", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "gf-zone-4"}, "name": "GF-Zone-4", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "gf-ts-4"}, "name": "GF-TS-4", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "gf-vav-4"}, "name": "GF-VAV-4", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "gf-zone-5"}, "name": "GF-Zone-5", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "gf-ts-5"}, "name": "GF-TS-5", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "gf-vav-5"}, "name": "GF-VAV-5", "type": "VAV", "children": []}}]}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "b5c24682-50f1-11ef-b4ce-d5aee9e495ae"}, "name": "First Floor", "type": "Floor", "children": [{"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-1"}, "name": "F1-Zone-1", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-1"}, "name": "F1-TS-1", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-1"}, "name": "F1-VAV-1", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-2"}, "name": "F1-Zone-2", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-2"}, "name": "F1-TS-2", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-2"}, "name": "F1-VAV-2", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-3"}, "name": "F1-Zone-3", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-3"}, "name": "F1-TS-3", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-3"}, "name": "F1-VAV-3", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-4"}, "name": "F1-Zone-4", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-4"}, "name": "F1-TS-4", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-4"}, "name": "F1-VAV-4", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-5"}, "name": "F1-Zone-5", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-5"}, "name": "F1-TS-5", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-5"}, "name": "F1-VAV-5", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-6"}, "name": "F1-Zone-6", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-6"}, "name": "F1-TS-6", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-6"}, "name": "F1-VAV-6", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-7"}, "name": "F1-Zone-7", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-7"}, "name": "F1-TS-7", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-7"}, "name": "F1-VAV-7", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-8"}, "name": "F1-Zone-8", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-8"}, "name": "F1-TS-8", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-8"}, "name": "F1-VAV-8", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-9"}, "name": "F1-Zone-9", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-9"}, "name": "F1-TS-9", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-9"}, "name": "F1-VAV-9", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f1-zone-10"}, "name": "F1-Zone-10", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f1-ts-10"}, "name": "F1-TS-10", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f1-vav-10"}, "name": "F1-VAV-10", "type": "VAV", "children": []}}]}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "b5c24682-50f2-11ef-b4ce-d5aee9e495ae"}, "name": "Second Floor", "type": "Floor", "children": [{"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-1"}, "name": "F2-Zone-1", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-1"}, "name": "F2-TS-1", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-1"}, "name": "F2-VAV-1", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-2"}, "name": "F2-Zone-2", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-2"}, "name": "F2-TS-2", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-2"}, "name": "F2-VAV-2", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-3"}, "name": "F2-Zone-3", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-3"}, "name": "F2-TS-3", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-3"}, "name": "F2-VAV-3", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-4"}, "name": "F2-Zone-4", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-4"}, "name": "F2-TS-4", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-4"}, "name": "F2-VAV-4", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-5"}, "name": "F2-Zone-5", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-5"}, "name": "F2-TS-5", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-5"}, "name": "F2-VAV-5", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-6"}, "name": "F2-Zone-6", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-6"}, "name": "F2-TS-6", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-6"}, "name": "F2-VAV-6", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-7"}, "name": "F2-Zone-7", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-7"}, "name": "F2-TS-7", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-7"}, "name": "F2-VAV-7", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-8"}, "name": "F2-Zone-8", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-8"}, "name": "F2-TS-8", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-8"}, "name": "F2-VAV-8", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-9"}, "name": "F2-Zone-9", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-9"}, "name": "F2-TS-9", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-9"}, "name": "F2-VAV-9", "type": "VAV", "children": []}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "f2-zone-10"}, "name": "F2-Zone-10", "type": "Zone", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "f2-ts-10"}, "name": "F2-TS-10", "type": "TemperatureSensor", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "f2-vav-10"}, "name": "F2-VAV-10", "type": "VAV", "children": []}}]}}]}}, {"entity": {"id": {"entityType": "ASSET", "id": "b5c24682-50f1-11ef-b4ce-d5aee9e495ad"}, "name": "Mechanical Plant", "type": "Plant", "children": [{"entity": {"id": {"entityType": "DEVICE", "id": "ahu-1"}, "name": "AHU-1", "type": "AirHandlingUnit", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "ahu-2"}, "name": "AHU-2", "type": "AirHandlingUnit", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "ahu-3"}, "name": "AHU-3", "type": "AirHandlingUnit", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "chiller-1"}, "name": "Chiller-1", "type": "<PERSON><PERSON>", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "chwp-1"}, "name": "CHWP-1", "type": "ChilledWaterPump", "children": []}}, {"entity": {"id": {"entityType": "DEVICE", "id": "chwp-2"}, "name": "CHWP-2", "type": "ChilledWaterPump", "children": []}}]}}]}