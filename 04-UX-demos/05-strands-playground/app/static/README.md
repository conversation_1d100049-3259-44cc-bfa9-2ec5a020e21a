# Chatbot with Built-in Tools

This is a web interface for interacting with a Strands-powered chatbot that has access to various tools.

## Features

- Interactive chat interface
- Customizable system prompt
- User ID management for session persistence
- Tool selection panel
- Support for HTML/XML rendering in responses
- File generation and access capabilities

## HTML/XML Rendering

The chatbot can render HTML or XML content in its responses. This is useful for displaying:

- Formatted text with headings, bold, italics, etc.
- Tables with structured data
- Code blocks with syntax highlighting
- Lists and other structured content

## Accessing Generated Files

If you want to view files generated by the chatbot:

1. Ask the chatbot to save files to the `./static` directory
2. Access the files at `hosturl/filename`

For example, if the chatbot generates an image called `chart.png` in the static directory, you can access it at `http://your-host-url/chart.png`.

## Available Tools

The chatbot has access to various tools that can be enabled or disabled through the tools panel. These include:

- Calculator for mathematical operations
- HTTP requests for accessing external APIs
- AWS service operations
- File reading and writing
- Shell command execution
- And many more!

Select the tools you want the chatbot to have access to and click "Update Tools" to apply the changes.